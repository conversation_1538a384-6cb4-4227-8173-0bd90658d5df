<?php defined('BASEPATH') OR exit('No direct script access allowed'); ?>

<ul class="breadcrumb">
  <li><a href="<?php echo site_url('dashboard');?>">Dashboard</a></li>
  <li><a href="<?php echo site_url('admin/staff_login_logs');?>">Staff Login Logs</a></li>
  <li>Active Sessions</li>
</ul>

<div class="col-md-12">
  <div class="card cd_border">
    <div class="card-header panel_heading_new_style_staff_border">
      <div class="row" style="margin: 0px;">
        <div class="d-flex justify-content-between" style="width:100%;">
          <h3 class="card-title panel_title_new_style_staff">
            <i class="fas fa-users"></i> Active Staff Sessions
          </h3>
          <ul class="panel-controls">
            <a style="margin-left:3px;" href="<?php echo base_url('admin/staff_login_logs'); ?>" class="btn btn-secondary pull-right">
              <i class="fas fa-arrow-left"></i> Back
            </a>
            <a style="margin-left:3px;" href="<?php echo base_url('admin/staff_login_logs/export_csv?is_active=1'); ?>" class="btn btn-success pull-right">
              <i class="fas fa-file-excel"></i> Export
            </a>
          </ul>
        </div>
      </div>
    </div>
    
    <div class="card-body pt-1">
      <!-- Summary Cards -->
      <div class="row mb-3">
        <div class="col-md-3">
          <div class="card bg-primary text-white">
            <div class="card-body">
              <div class="d-flex justify-content-between">
                <div>
                  <h4><?php echo count($active_sessions); ?></h4>
                  <p class="mb-0">Active Sessions</p>
                </div>
                <div class="align-self-center">
                  <i class="fas fa-users fa-2x"></i>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="col-md-3">
          <div class="card bg-success text-white">
            <div class="card-body">
              <div class="d-flex justify-content-between">
                <div>
                  <h4><?php echo count(array_unique(array_column($active_sessions, 'user_id'))); ?></h4>
                  <p class="mb-0">Unique Users</p>
                </div>
                <div class="align-self-center">
                  <i class="fas fa-user fa-2x"></i>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="col-md-3">
          <div class="card bg-info text-white">
            <div class="card-body">
              <div class="d-flex justify-content-between">
                <div>
                  <h4><?php echo count(array_filter($active_sessions, function($s) { return $s['device_type'] == 'mobile'; })); ?></h4>
                  <p class="mb-0">Mobile Sessions</p>
                </div>
                <div class="align-self-center">
                  <i class="fas fa-mobile-alt fa-2x"></i>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="col-md-3">
          <div class="card bg-warning text-white">
            <div class="card-body">
              <div class="d-flex justify-content-between">
                <div>
                  <h4><?php echo count(array_filter($active_sessions, function($s) { return $s['device_type'] == 'desktop'; })); ?></h4>
                  <p class="mb-0">Desktop Sessions</p>
                </div>
                <div class="align-self-center">
                  <i class="fas fa-desktop fa-2x"></i>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Active Sessions Table -->
      <div class="card">
        <div class="card-header">
          <h6 class="card-title">
            <i class="fas fa-table"></i> Currently Active Sessions
          </h6>
        </div>
        <div class="card-body">
          <?php if (!empty($active_sessions)): ?>
            <div class="table-responsive">
              <table class="table table-striped table-hover" id="activeSessionsTable">
                <thead>
                  <tr>
                    <th><i class="fas fa-user"></i> User</th>
                    <th><i class="fas fa-network-wired"></i> IP Address</th>
                    <th><i class="fas fa-sign-in-alt"></i> Login Time</th>
                    <th><i class="fas fa-hourglass-half"></i> Duration</th>
                    <th><i class="fas fa-mobile-alt"></i> Device</th>
                    <th><i class="fab fa-chrome"></i> Browser</th>
                    <th><i class="fas fa-cogs"></i> Actions</th>
                  </tr>
                </thead>
                <tbody>
                  <?php foreach ($active_sessions as $session): ?>
                    <tr class="table-success">
                      <td>
                        <div class="user-info">
                          <strong><?php echo htmlspecialchars($session['username']); ?></strong>
                          <?php if (!empty($session['first_name']) || !empty($session['last_name'])): ?>
                            <br><small class="text-muted"><?php echo trim($session['first_name'] . ' ' . $session['last_name']); ?></small>
                          <?php endif; ?>
                        </div>
                      </td>
                      <td>
                        <span class="badge badge-info"><?php echo htmlspecialchars($session['ip_address']); ?></span>
                        <?php if ($session['login_method']): ?>
                          <br><small class="text-muted"><?php echo ucfirst($session['login_method']); ?></small>
                        <?php endif; ?>
                      </td>
                      <td>
                        <?php echo date('M j, Y H:i:s', strtotime($session['login_time'])); ?>
                        <br><small class="text-muted"><?php echo time_elapsed_string($session['login_time']); ?> ago</small>
                      </td>
                      <td>
                        <span class="badge badge-warning">
                          <?php echo format_duration(time() - strtotime($session['login_time'])); ?>
                        </span>
                      </td>
                      <td>
                        <?php
                        $device_icon = get_device_icon($session['device_type']);
                        $device_class = ($device_icon == 'mobile-alt' || $device_icon == 'tablet-alt' || $device_icon == 'desktop') ? 'fas' : 'fas';
                        ?>
                        <i class="<?php echo $device_class; ?> fa-<?php echo $device_icon; ?>"></i>
                        <?php echo ucfirst($session['device_type'] ?: 'Unknown'); ?>
                        <?php if ($session['operating_system']): ?>
                          <br><small class="text-muted"><?php echo $session['operating_system']; ?></small>
                        <?php endif; ?>
                      </td>
                      <td>
                        <?php
                        $browser_icon = get_browser_icon($session['browser_name']);
                        $browser_class = in_array($browser_icon, ['chrome', 'firefox', 'safari', 'edge', 'opera', 'internet-explorer']) ? 'fab' : 'fas';
                        ?>
                        <i class="<?php echo $browser_class; ?> fa-<?php echo $browser_icon; ?>"></i>
                        <?php echo $session['browser_name'] ?: 'Unknown'; ?>
                        <?php if ($session['browser_version']): ?>
                          <br><small class="text-muted">v<?php echo $session['browser_version']; ?></small>
                        <?php endif; ?>
                      </td>
                      <td>
                        <div class="btn-group" role="group">
                          <a href="<?php echo base_url('admin/staff_login_logs/user_history/' . $session['user_id']); ?>" 
                             class="btn btn-sm btn-info" title="View User History">
                            <i class="fas fa-history"></i>
                          </a>
                          <a href="<?php echo base_url('admin/staff_login_logs/force_logout/' . $session['user_id']); ?>" 
                             class="btn btn-sm btn-danger" title="Force Logout"
                             onclick="return confirm('Are you sure you want to force logout this user?')">
                            <i class="fas fa-sign-out-alt"></i>
                          </a>
                        </div>
                      </td>
                    </tr>
                  <?php endforeach; ?>
                </tbody>
              </table>
            </div>
          <?php else: ?>
            <div class="alert alert-info">
              <i class="fas fa-info-circle"></i>
              No active sessions found.
            </div>
          <?php endif; ?>
        </div>
      </div>
    </div>
  </div>
</div>

<style>
/* Icon alignment improvements */
.fas, .fab, .far {
    margin-right: 5px;
    vertical-align: middle;
}

.btn .fas, .btn .fab, .btn .far {
    margin-right: 3px;
}

.card-title .fas, .card-title .fab, .card-title .far {
    margin-right: 8px;
}

.table th .fas, .table th .fab, .table th .far {
    margin-right: 5px;
}

.badge .fas, .badge .fab, .badge .far {
    margin-right: 3px;
}

.alert .fas, .alert .fab, .alert .far {
    margin-right: 5px;
}
</style>

<script>
$(document).ready(function() {
    // Initialize DataTable
    $('#activeSessionsTable').DataTable({
        "responsive": true,
        "lengthChange": true,
        "autoWidth": false,
        "pageLength": 25,
        "order": [[ 2, "desc" ]], // Sort by login time descending
        "columnDefs": [
            { "orderable": false, "targets": 6 } // Disable sorting on Actions column
        ]
    });
});
</script>
