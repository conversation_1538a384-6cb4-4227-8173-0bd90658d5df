<ul class="breadcrumb">
    <li><a href="<?php echo site_url('/avatars') ?>">Dashboard</a></li>
    <li><a href="<?php echo site_url('staff/staff_menu/'); ?>">Staff Menu</a></li>
    <li><a href="<?php echo site_url('staff/staff_controller/'); ?>">Staff Index</a></li>
    <li><a href="">Staff Profile</a></li>
</ul>
<script>
function printProfile() {
    var restorepage = document.body.innerHTML;
    var printcontent = document.getElementById('profile').innerHTML;
    document.body.innerHTML = printcontent;
    window.print();
    document.body.innerHTML = restorepage;
}
$(function() {
    var heightScreen = $(window).height();
    $('.border-bottom').css({
        'height': heightScreen - 410
    });
    $('.personal_info').css({
        'height': heightScreen - 240
    });
    // $('.personal_info').css({
    //   'overflow': 'scroll'
    // });
    $('.no_data_found_hide').css({
        'height': heightScreen - 300
    });

})
</script>

<div class="col-md-12">
    <div class="card cd_border">
        <div class="card-header panel_heading_new_style_staff_border">
            <div class="row" style="margin: 0px">
                <div class="d-flex justify-content-between" style="width:100%;">
                    <h3 class="card-title panel_title_new_style_staff">
                        <a class="back_anchor" href="<?php echo site_url('dashboard'); ?>">
                            <span class="fa fa-arrow-left"></span>
                        </a>Details of Staff
                    </h3>
                    <?php if ($profile_lock_unlock_status->profile_status == 'unlocked' ) { ?>
                    <h4 style="color: #101087;font-weight: 700;padding: 10px">Click the Confirm button if the below
                        information is correct.
                        <a class="btn btn-md btn-warning pull-right" style="margin-top: -5px;margin-left: 10px;"
                            onclick="update_profile_confirmedbyuser('<?php echo $staff_id ?>')"
                            id="confirm_profile">Confirm</a>
                    </h4>
                    <?php }else{ ?>
                    <?php if ($profile_lock_unlock_status->profile_confirmed == 'Yes' ) { ?>
                    <?php if ($profile_lock_unlock_status->profile_confirmed_date) { ?>
                    <h5 style="line-height: 26px;">You have confirmed profile information on Date :
                        <?php echo date('d-M-Y',strtotime($profile_lock_unlock_status->profile_confirmed_date)) ?> <i
                            class="fa fa-check-square-o" aria-hidden="true"></i><span class="pull-right"> </span></h5>
                    <?php }else{ ?>
                    <h5 style="line-height: 26px;">You have confirmed profile information <i
                            class="fa fa-check-square-o" aria-hidden="true"></i><span class="pull-right"> </span></h5>
                    <?php } ?>
                    <?php }else{ ?>
                    <?php if ($profile_lock_unlock_status->profile_status_changed_date) { ?>
                    <h5 style="line-height: 26px;">The profile has been locked to prevent further edits on this date :
                        <?php echo date('d-M-Y',strtotime($profile_lock_unlock_status->profile_status_changed_date)) ?>
                        <i class="fa fa-check-square-o" aria-hidden="true"></i><span class="pull-right"> </span>
                    </h5>
                    <?php }else{ ?>
                    <h5 style="line-height: 26px;">The profile has been locked to prevent further edits <i
                            class="fa fa-check-square-o" aria-hidden="true"></i><span class="pull-right"> </span></h5>
                    <?php } ?>
                    <?php } ?>
                    <?php } ?>

                </div>
            </div>
        </div>
        <div class="card-body loadingIcon">
            <div class="col-md-2" style="padding: 0;">
                <div class="panel-body profile" style="background-color:#ffffff; text-align: center;">
                    <?php
          $displayPhoto = 'display:none';
          $pictureUrl = 0;
          if (isset($showEditfields['personal_info'])) {
            if (in_array('picture_url', $showEditfields['personal_info'])) {
              $displayPhoto = '';
              $pictureUrl = 1;
            }
          }

          ?>
                    <?php if ($pictureUrl) { ?>
                    <img onclick="$('#fileupload').click()" class="img-responsive " id="staff_profile_img">
                    <?php } else { ?>
                    <img class="img-responsive" id="staff_profile_img" style="border-radius:20%" width="120px"
                        height="120px">
                    <?php } ?>
                    <input type="hidden" id="old_img" name="old_img">
                    <i style="position: absolute;top: 9rem;right: 4rem;<?php echo $displayPhoto ?>"
                        onclick="$('#fileupload').click();" class="fa fa-pencil"></i>
                    <input hidden="hidden" type="file" id="fileupload" class="file" data-preview-file-type="jpeg"
                        name="staff_photo" accept="image/*">
                    <span id="fileuploadError"
                        style="color:red;display: block;padding-top:5px;padding-bottom:5px;"></span>
                    <span id="percentage-completed_staff"
                        style="font-size: 20px; display: none; position: absolute;top: 64px;left: 0;right: 0;">0
                        %</span>
                    <button id="photo_staff_profile" <?php echo $displayPhoto ?> type="button"
                        onclick="save_profile_photos(<?php echo $staff_id; ?>)"
                        style="display: none;margin: auto;width:100px;" class="btn photo_btn">Save</button>
                    <span class="help-block">Allowed file types: JPEG, JPG, and PNG; Allowed size: up to 10MB</span>
                    <div class="profile-data">
                        <div class="profile-data-name" id="staff-staff_name" style="margin-top: 1.5rem;color: #073a71;">
                        </div>
                        <div class="profile-data-title" id="staff-staff_type" style="color: #073a71;"></div>
                        <div class="profile-data-title" id="stafftopProfile-employee_code" style="color: #073a71;">
                        </div>
                    </div>
                </div>

                <div class="panel-body list-group border-bottom" style="padding: 0px 1px;overflow:scroll; ">
                    <?php $k = 1;
          foreach ($profileTabs as $tabName => $value) {
            //echo "<pre>"; print_r($profileTabs); die();
            $tabDisplay = 'display:none';
            if (isset($displayfields[$tabName])) {
              $tabDisplay = 'display:';
            }
          ?>
                    <a href="#" style="border-radius: 10px;margin-bottom: 0.5rem;<?php echo $tabDisplay ?>"
                        onclick="get_staff_profile_data('<?php echo $tabName ?>')"
                        class="d-flex justify-content-between align-items-center list-group-item <?php if ($k == 1) echo 'active' ?>">
                        <?php
                if ($tabName == 'document') {
                  $tabName = 'Documents';
                }
                if ($tabName == 'workshop') {
                  $tabName = 'Training / Workshop';
                }
                if ($tabName == 'publications_citations') {
                  $tabName = 'Publication / Citations';
                }
                if ($tabName == 'initiative') {
                  $tabName = 'Initiatives';
                }
                if ($tabName == 'payroll_details') {
                  $tabName = 'Payroll Details';
                }
                if ($tabName == 'family_info') {
                  $tabName = 'Family Info';
                }
                if ($tabName == 'transport_request') {
                  $tabName = 'Transport Request';
                }
                echo ucfirst(str_replace("_", ' ', $tabName)) ?>
                        <span style="float: right;" class="glyphicon glyphicon-chevron-right"></span></a>
                    <?php $k++;
          } ?>
                </div>
            </div>

            <div class="col-md-10">
                <?php $i = 1;
        foreach ($profileTabs as $tabName => $value) {
          $tab_name = '';
          if (isset($displayfields[$tabName])) {
            $tab_name = $tabName;
          }

        ?>
                <div class="<?php echo $tab_name ?> opacity" style="<?php if ($i != 1) echo 'display:none' ?>">
                    <div id="loader" class="loaderclass" style="display:none;"></div>
                    <div class="card cd_border">
                        <div class="card-header panel_heading_new_style_staff_border">
                            <div class="row" style="margin: 0px">
                                <div style="width: 100%;" class="d-flex justify-content-between">
                                    <h3 class="card-title panel_title_new_style_staff">
                                        <?php
                        if ($tab_name == 'workshop') {
                          echo 'Training / Workshop';
                        } else if ($tab_name == 'document') {
                          echo 'Documents';
                        } else if ($tab_name == 'publications_citations') {
                          echo 'Publication / Citations';
                        } else if ($tab_name == 'initiative') {
                          echo 'Initiatives';
                        } else {
                          echo ucfirst(str_replace("_", ' ', $tab_name));
                        }
                      ?>
                                    </h3>
                                    <?php
                    if (!empty($showEditfields[$tab_name])) { ?>
                                    <?php if ($showEditfields[$tab_name][0] == 'document') { ?>
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <?php  if($profile_lock_unlock_status->profile_status=='unlocked'){ ?>

                                            <div class="new_circleShape" style="background-color:#fe970a;float:right;">
                                                <a class="control-primary" style="cursor:pointer;" data-toggle="modal"
                                                    onclick="document_popup()"
                                                    data-target="#add_staff_document_by_user">
                                                    <span class="fa fa-plus" style="line-height:3.2rem"></span>
                                                </a>
                                            </div>
                                            <?php  } ?>
                                        </div>
                                    </div>
                                    <?php } ?>

                                    <?php if ($showEditfields[$tab_name][0] == 'awards') { ?>
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <?php  if($profile_lock_unlock_status->profile_status=='unlocked'){ ?>

                                            <div class="new_circleShape" style="background-color:#fe970a;float:right;">
                                                <a class="control-primary" style="cursor:pointer;" data-toggle="modal"
                                                    onclick="awards_popup()" data-target="#add_staff_awards_by_user">
                                                    <span class="fa fa-plus" style="line-height:3.2rem"></span>
                                                </a>
                                            </div>
                                            <?php  } ?>
                                        </div>
                                    </div>
                                    <?php } ?>

                                    <?php if ($showEditfields[$tab_name][0] == 'qualification') { ?>
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <?php  if($profile_lock_unlock_status->profile_status=='unlocked'){ ?>
                                            <div class="new_circleShape" style="background-color:#fe970a;float:right;">
                                                <a class="control-primary" style="cursor:pointer;" data-toggle="modal"
                                                    onclick="qualification_popup()"
                                                    data-target="#add_qualification_modal">
                                                    <span class="fa fa-plus" style="line-height:3.2rem"></span>
                                                </a>
                                            </div>
                                            <?php } ?>

                                        </div>
                                    </div>
                                    <?php } ?>

                                    <?php if ($showEditfields[$tab_name][0] == 'experience') {
                         ?>
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <?php  if($profile_lock_unlock_status->profile_status =='unlocked'){ ?>
                                            <div class="new_circleShape" style="background-color:#fe970a;float:right;">
                                                <a class="control-primary" style="cursor:pointer;" data-toggle="modal"
                                                    onclick="experience_popup()" data-target="#_add_experience_modal">
                                                    <span class="fa fa-plus" style="line-height:3.2rem"></span>
                                                </a>
                                            </div>
                                            <?php } ?>
                                        </div>
                                    </div>
                                    <?php } ?>
                                    <?php if ($showEditfields[$tab_name][0] == 'workshop_training') { ?>
                                    <div class="d-flex justify-content-between">
                                        <?php if($profile_lock_unlock_status->profile_status=='unlocked'){ ?>
                                        <div>
                                            <div class="new_circleShape" style="background-color:#fe970a;float:right;">
                                                <a class="control-primary" style="cursor:pointer;" data-toggle="modal"
                                                    onclick="workshop_popup()" data-target="#staff_traning">
                                                    <span class="fa fa-plus" style="line-height:3.2rem"></span>
                                                </a>
                                            </div>
                                        </div>
                                        <?php } ?>
                                    </div>
                                    <?php } ?>
                                    <?php if ($showEditfields[$tab_name][0] == 'interest') { ?>
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <?php  if($profile_lock_unlock_status->profile_status=='unlocked'){ ?>
                                            <div class="new_circleShape" style="background-color:#fe970a;float:right;">
                                                <a class="control-primary" style="cursor:pointer;" data-toggle="modal"
                                                    onclick="interest_popup()" data-target="#staff_interests">
                                                    <span class="fa fa-plus" style="line-height:3.2rem"></span>
                                                </a>
                                            </div>
                                            <?php } ?>
                                        </div>
                                    </div>
                                    <?php } ?>
                                    <?php if ($showEditfields[$tab_name][0] == 'initiative') { ?>
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <?php  if($profile_lock_unlock_status->profile_status=='unlocked'){ ?>
                                            <div class="new_circleShape" style="background-color:#fe970a;float:right;">
                                                <a class="control-primary" style="cursor:pointer;" data-toggle="modal"
                                                    onclick="clearInputFields()"
                                                    data-target="#_staff_inititiative_add_modal">
                                                    <span class="fa fa-plus" style="line-height:3.2rem"></span>
                                                </a>
                                            </div>
                                            <?php } ?>
                                        </div>
                                    </div>
                                    <?php } ?>
                                    <?php if ($showEditfields[$tab_name][0] == 'publications_citations') { ?>
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <?php  if($profile_lock_unlock_status->profile_status=='unlocked'){ ?>
                                            <div class="new_circleShape" style="background-color:#fe970a;float:right;">
                                                <a class="control-primary" style="cursor:pointer;" data-toggle="modal"
                                                    onclick="publication_popup()"
                                                    data-target="#staff_publicationcircle">
                                                    <span class="fa fa-plus" style="line-height:3.2rem"></span>
                                                </a>
                                            </div>
                                            <?php } ?>
                                        </div>
                                    </div>
                                    <?php } ?>

                                    <?php } ?>

                                </div>
                            </div>
                        </div>
                        <div class="no_data_found" style="display:none; width: 90%;margin: auto;">
                            <h4 class="no-data-display">No data found </h4>
                        </div>
                        <div class="card-body no_data_found_hide" style="display:none;overflow:scroll;">
                            <?php if($tab_name == 'transport_request') { ?>
                                <div id="transport_details">

                                </div>
                            <?php } ?>
                            <table class="table table-border">
                                <?php
                  if ($tab_name == 'document') { ?>
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>Uploaded On</th>
                                        <th>Document Type</th>
                                        <th>Approval Status</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody id="document_data">

                                </tbody>

                                <?php } else if ($tab_name == 'awards') {
                    echo "<thead>
                            <tr>
                                <th>#</th>
                                <th>Uploaded On</th>
                                <th>Award Name</th>
                                <th>Awarded By</th>
                                <th>Awarded on</th>
                                <th>Cash value</th>
                                <th>Approval Status</th>
                                <th>Action</th>
                            </tr>
                          </thead>
                          <tbody id='awards_data'>
                          
                          </tbody>";
                  } else if ($tab_name == 'qualification') {
                    echo "<thead>
                            <tr>
                                <th>#</th>
                                <th>Degree/Program</th>
                                <th>Combination/Branch</th>
                                <th>Specialization</th>
                                <th>University/Institute</th>
                                <th>Approval Status</th>
                                <th>Action</th>
                            </tr>
                          </thead>
                          <tbody id='qualification_data'>
                          
                          </tbody>";
                  } else if ($tab_name == 'experience') {
                    echo "<thead>
                            <tr>
                                <th>#</th>
                                <th>Worked For</th>
                                <th>Duration</th>
                                <th>Experience type</th>
                                <th>Approval Status</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody id='experience_data'>
                        </tbody>";
                  } else if ($tab_name == 'workshop') {
                    echo " <thead>
                            <tr>
                                <th>#</th>
                                <th>Traning Name</th>
                                <th>Institute Name</th>
                                <th>Traning Related To</th>
                                <th>Duration</th>
                                <th>Approval Status</th>
                                <th>Action</th>
                            </tr>
                          </thead>
                          <tbody id='workshop_data'>
                          </tbody>";
                  } else if ($tab_name == 'publications_citations') {
                    echo "<thead>
                            <tr>
                                <th>#</th>
                                <th>Publication Type</th>
                                <th>Publication Name</th>
                                <th>Publication URL</th>
                                <th>Published on</th>
                                <th>Approval Status</th>
                                <th>Action</th>
                            </tr>
                          </thead>
                          <tbody id='publication_data'>
                          </tbody>";
                  } else  if ($tab_name == 'initiative') {
                    echo "<thead>
                            <tr>
                                <th>#</th>
                                <th>Initiative</th>
                                <th>On</th>
                                <th>Attended By</th>
                                <th>Approval Status</th>
                                <th>Action</th>
                            </tr>
                          </thead>
                          <tbody id='initiative_data'>
                          </tbody>";
                  } else if ($tab_name == 'interest') {
                    echo "<thead>
                            <tr>
                                <th>#</th>
                                <th>Area of Interests</th>
                                <th>Specific Interest</th>
                                <th>Approval Status</th>
                                <th>Action</th>
                            </tr>
                          </thead>
                          <tbody id='interest_data'>
                              
                          </tbody>";
                  } else if($tab_name == 'family_info'){
                    echo '<div class="no-data-display" id="family_info_no_data">No Data Found</div>
                          <div id="family_info_table_container" style="display:none;">
                              <!-- Table will be inserted here -->
                          </div>';
                  }else if($tab_name != 'transport_request'){ ?>
                                <?php foreach ($value as $key => $val) {
                      $display = 'display:none';
                      if (in_array($val['column_name'], $displayfields[$tab_name]) && $val['column_name'] != 'picture_url') {
                        $display = 'display:';
                      }

                      $edit_display = 'display:none';
                      if (!empty($showEditfields[$tab_name])) {
                        if (in_array($val['column_name'], $showEditfields[$tab_name])) {
                          $edit_display = 'display:';
                        }
                      }
                    ?>
                                <tr style="<?php echo $display ?>">
                                    <th><?php echo ucfirst($val['dispaly_name']) ?></th>
                                    <td id="staff-<?php echo $val['column_name'] ?>"></td>
                                    <?php  if($profile_lock_unlock_status->profile_status=='unlocked'){ ?>
                                    <td width="12%" id="show-edit-staff-<?php echo $val['column_name'] ?>"><a
                                            style="<?php echo $edit_display ?>" href="javascript:void(0)"
                                            id="edit-staff-<?php echo $val['column_name'] ?>"
                                            data-labe_name="<?php echo $val['dispaly_name'] ?>"
                                            data-input="<?php echo $val['data_input'] ?>" class="btn btn-warning btn-sm"
                                            onclick="edit_each_row_staff_data('<?php echo $val['column_name'] ?>') "
                                            data-input_name="<?php echo $val['column_name'] ?>" data-toggle='modal'
                                            data-target='#staff_edit_by_user'><span class="fa fa-edit"></span></a>
                                        <span style="display:none;margin:0"
                                            id="savedLabel-<?php echo $val['column_name'] ?>"
                                            class="label label-success label-form">Saved</span>
                                        <span style="display:none;margin:0"
                                            id="unsavedLabel-<?php echo $val['column_name'] ?>"
                                            class="label label-danger label-form">Un-Success</span>
                                    </td>
                                    <?php }else{ ?>
                                    <td></td>
                                    <?php } ?>
                                </tr>
                                <?php } ?>
                                <?php }
                  ?>

                            </table>

                        </div>
                    </div>
                </div>

                <?php $i++;
        } ?>
            </div>


        </div>
    </div>
</div>

<div class="modal fade" id="show_detailed_qualification_modal" tabindex="-1" role="dialog"
    aria-labelledby="exampleModalLabel">
    <div class="modal-dialog" role="document" style="border-radius: .75rem">
        <div class="modal-content" style="width: 50%;margin: 1% auto;">
            <div class="modal-header" style="border-top-left-radius: .75rem;border-top-right-radius: .75rem;">
                <h4 class="modal-title">Details of <span id="details_degree"></span></h4>
                <button type="button" class="close" data-dismiss="modal"><i class="fa fa-times" aria-hidden="true"
                        style="color: #d80403;font-size: 21px;"></i></button>
            </div>
            <form enctype="">
                <div class="modal-body" style="overflow-y: scroll;">
                    <div class="col-sm-12" style="margin-bottom: 10px; padding-left:0px;
                padding-right:0px;">
                        <div class="form-group col-sm-6">
                            <div class="form-group">
                                <label class="control-label" style="margin-bottom: 0px;"> Degree Type</label>
                                <input type="text" readonly class="form-control" id="show_degree_type"
                                    name="show_degree_type">
                            </div>
                            <div class="form-group">
                                <label class="control-label" style="margin-bottom: 0px;"> Degree/Program</label>
                                <input type="text" readonly class="form-control" id="show_degree" name="show_degree">
                            </div>
                            <div class="form-group remove_edit_form">
                                <label class="control-label" style="margin-bottom: 0px;"> Combination/Branch</label>
                                <input type="text" readonly class="form-control" id="show_branch" name="show_branch">
                            </div>
                        </div>
                        <div class="form-group col-sm-6">
                            <div class="form-group">
                                <label class="control-label" style="margin-bottom: 0px;"> Specialization</label>
                                <input type="text" readonly class="form-control" id="show_spec" name="show_spec">
                            </div>
                            <div class="form-group">
                                <label class="control-label" style="margin-bottom: 0px;"> Duration of the
                                    program</label>
                                <input type="text" readonly class="form-control" id="show_duration"
                                    name="show_duration">
                            </div>
                            <div class="form-group remove_edit_form">
                                <label class="control-label" style="margin-bottom: 0px;"> Completed Month/Year</label>
                                <input type="text" readonly class="form-control" id="show_month" name="show_month">
                            </div>
                        </div>
                    </div><br>
                    <div class="col-sm-12" style="padding: 0px 15px 10px 5px;margin-top:5px ">
                        <div class="form-group col-sm-12" style="padding:7px 0 7px 8px;">
                            <label class="control-label" style="margin-bottom: 0px;"> University/Institute</label>
                            <input type="text" id="show_uni" class="form-control" disabled>
                        </div>
                    </div>
                    <div class="col-sm-12" style="padding: 0px 15px 10px 15px; ">
                        <div class="form-group col-sm-12" style="border: 1px solid #d3d3d3;padding:7px 0 7px 8px;">
                            <label for="show_doc">Supporting Document</label>
                            <!-- <input type="text" id="show_doc" class="form-control" disabled> -->
                            <div id="download_link">

                            </div>
                        </div>
                    </div><br>
                    <div class="col-sm-12" style="padding: 0px 15px 10px 5px; ">
                        <div class="form-group col-sm-12" style="padding:7px 0 7px 8px;">
                            <label class="control-label" style="margin-bottom: 0px;"> Remarks</label>
                            <textarea readonly rows="4" cols="500" class="form-control" id="show_remarks"
                                name="show_remarks">
                  </textarea>
                        </div>
                    </div>
                    <div class="form-group col-sm-6">
                        <div class="form-group">
                            <label class="control-label" style="margin-bottom: 0px;"> Created On</label>
                            <input type="text" readonly class="form-control" id="show_created_on"
                                name="show_created_on">
                        </div>
                        <div class="form-group">
                            <label class="control-label" style="margin-bottom: 0px;"> Created By</label>
                            <input type="text" readonly class="form-control" id="show_created_by"
                                name="show_created_by">
                        </div>
                    </div>
                    <div class="form-group col-sm-6">
                        <div class="form-group">
                            <label class="control-label" style="margin-bottom: 0px;"> Disabled By</label>
                            <input type="text" readonly class="form-control" id="show_disabled_by"
                                name="show_disabled_by">
                        </div>
                        <div class="form-group">
                            <label class="control-label" style="margin-bottom: 0px;"> Status</label>
                            <input type="text" readonly class="form-control" id="show_status" name="show_status">
                        </div>
                    </div>
                    <div class="form-group col-sm-6">
                        <div class="form-group">
                            <label class="control-label" style="margin-bottom :0px;">Approved Status</label>
                            <input type="text" readonly class="form-control" id="show_qualification_approved_status"
                                name="show_qualification_approved_status">
                        </div>

                    </div>
                    <div class="col-sm-12" style="padding: 0px 15px 10px 5px;">
                        <div class="form-group col-sm-12" style="padding:7px 0 7px 8px;">
                            <label class="control-label" id="approved_label_remarks_for_qualification"
                                style="margin-bottom: 0px;">Approved Remarks</label>
                            <textarea readonly rows="4" cols="500" class="form-control"
                                id="approved_remarks_for_qualification" name="approved_remarks_for_qualification">

              </textarea>
                        </div>

                    </div>

                </div>
            </form>
        </div>
    </div>
</div>

<div class="modal fade" id="view_training_details" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel"
    aria-hidden="true">
    <div class="modal-dialog" role="document" style="margin: auto;border-radius: .75rem">
        <input type="hidden" id="workshop_staff_id">
        <input type="hidden" id="workshop_staff_certificate_id">
        <div class="modal-content" style="width: 50%;">
            <div class="modal-header" style="border-top-left-radius: .75rem;border-top-right-radius: .75rem;">
                <h4 class="modal-title">Trainings / Workshop</h4>
                <button type="button" class="close" data-dismiss="modal"><i class="fa fa-times" aria-hidden="true"
                        style="color: #d80403;font-size: 21px;"></i></button>
            </div>
            <form enctype="multipart/form-data" id="update_form_staff_workshop" class="form-horizontal"
                data-parsley-validate method="post">
                <input type="hidden" name="interests_id2" id="interestsId2">
                <div class="modal-body" style="overflow: scroll;">
                    <div class="col-sm-12" style="margin-bottom: 10px; padding-left:0px;
            padding-right:0px;">
                        <div class="form-group col-sm-6">
                            <div class="form-group">
                                <label class="control-label" style="margin-bottom: 0px;"> Traning Name</label>
                                <input type="text" readonly class="form-control" id="trainingName" name="trainer_name">
                            </div>
                            <div class="form-group">
                                <label class="control-label" style="margin-bottom: 0px;"> Traning Related To</label>
                                <input type="text" readonly class="form-control" id="traningRelatedTo"
                                    name="traning_related_to">
                            </div>
                            <div class="form-group remove_edit_form">
                                <label class="control-label" style="margin-bottom: 0px;"> Created By</label>
                                <input type="text" readonly class="form-control" id="traningaddedby"
                                    name="traningaddedby">
                            </div>
                        </div>
                        <div class="form-group col-sm-6">
                            <div class="form-group">
                                <label class="control-label" style="margin-bottom: 0px;"> Institute Name</label>
                                <input type="text" readonly class="form-control" id="instituteName"
                                    name="institute_name">
                            </div>
                            <div class="form-group">
                                <label class="control-label" style="margin-bottom: 0px;"> Duration</label>
                                <input type="text" readonly class="form-control" id="traningduration" name="duration">
                            </div>
                            <div class="form-group remove_edit_form">
                                <label class="control-label" style="margin-bottom: 0px;"> Created On</label>
                                <input type="text" readonly class="form-control" id="traningaddedon"
                                    name="traningaddedon">
                            </div>
                        </div>
                    </div><br>
                    <div class="col-sm-12 remove_edit_form" style="padding: 0px 15px 10px 15px; ">
                        <div class="form-group col-sm-12" style="border: 1px solid #d3d3d3;padding:7px 0 7px 8px;">
                            <label class="control-label" style="margin-bottom: 0px;"> Certificate </label>
                            <span id="training_doc" style="dispaly:none"> No document found</span>
                            <a id="training_doc_download" onclick="download_workshop_certificate()"
                                class="btn btn-primary"><i class="fa fa-download"> </i> Download </a>
                        </div>
                    </div><br>
                    <div class="col-sm-12" style="padding: 0px 15px 10px 5px; ">
                        <div class="form-group col-sm-12" style="padding:7px 0 7px 8px;">
                            <label class="control-label" style="margin-bottom: 0px;"> Remarks</label>
                            <textarea readonly rows="4" cols="500" class="form-control" id="Remarks" name="remarks">
              </textarea>
                        </div>
                    </div>
                    <div class="form-group col-sm-6">
                        <div class="form-group">
                            <label class="control-label" style="margin-bottom: 0px;">Approved Status</label>
                            <input type="text" readonly class="form-control" id="approved_training_status"
                                name="approved_training_status">

                        </div>
                    </div>
                    <div class="col-sm-12" style="padding: 0px 15px 10px 5px; ">
                        <div class="form-group col-sm-12" style="padding:7px 0 7px 8px;">
                            <label class="control-label" id="label_training_approved_remarks"
                                style="margin-bottom: 0px;"> Approved Remarks</label>
                            <textarea readonly rows="4" cols="500" class="form-control" id="training_approved_remarks"
                                name="training_approved_remarks">
              </textarea>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<div class="modal fade" id="view_publication_details" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel"
    aria-hidden="true">
    <div class="modal-dialog" role="document" style="margin: auto;border-radius: .75rem">
        <input type="hidden" id="publication_staff_id">
        <input type="hidden" id="publication_staff_certificate_id">
        <div class="modal-content" style="width: 50%;">
            <div class="modal-header" style="border-top-left-radius: .75rem;border-top-right-radius: .75rem;">
                <h4 class="modal-title">Publication / Citations</h4>
                <button type="button" class="close" data-dismiss="modal"><i class="fa fa-times" aria-hidden="true"
                        style="color: #d80403;font-size: 21px;"></i></button>
            </div>
            <form enctype="multipart/form-data" id="update_form_staff_publication" class="form-horizontal"
                data-parsley-validate method="post">
                <input type="hidden" name="interests_id1" id="interestsId1">
                <div class="modal-body" style="overflow: scroll;">
                    <div class="col-sm-12" style="margin-bottom: 10px; padding-left:0px;
          padding-right:0px;">
                        <div class="form-group col-sm-6">
                            <div class="form-group">
                                <label class="control-label" style="margin-bottom: 0px;"> Publication Type</label>
                                <input type="text" readonly class="form-control" id="publicationtype"
                                    name="publication_type">
                            </div>
                        </div>
                        <div class="form-group col-sm-6">
                            <div class="form-group">
                                <label class="control-label" style="margin-bottom: 0px;"> Publication URL</label>
                                <input type="text" readonly class="form-control" id="publicationurl"
                                    name="publication_url">
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-12" style="margin-bottom: 10px; padding-left:0px;
        padding-right:0px;">
                        <div class="form-group col-sm-6">
                            <div class="form-group">
                                <label class="control-label" style="margin-bottom: 0px;"> Publication Name</label>
                                <input type="text" readonly class="form-control" id="publicationname"
                                    name="publication_name">
                            </div>
                        </div>
                        <div class="form-group col-sm-6">
                            <div class="form-group">
                                <label class="control-label" style="margin-bottom: 0px;"> Published on</label>
                                <input type="date" readonly class="form-control" id="publishedon" name="publication_on">
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-12" style="margin-bottom: 10px; padding-left:0px;
      padding-right:0px;">
                        <div class="form-group col-sm-6 remove_edit_form">
                            <div class="form-group">
                                <label class="control-label" style="margin-bottom: 0px;"> Created By</label>
                                <input type="text" readonly class="form-control" id="createdby" name="createdby">
                            </div>
                        </div>
                        <div class="form-group col-sm-6 remove_edit_form">
                            <div class="form-group">
                                <label class="control-label" style="margin-bottom: 0px;"> Created On</label>
                                <input type="text" readonly class="form-control" id="createdon"
                                    name="publication_remarks">
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-12" style="padding: 0px 15px 10px 5px; ">
                        <div class="form-group col-sm-12" style="padding:7px 0 7px 8px;">
                            <label class="control-label" style="margin-bottom: 0px;"> Remarks</label>
                            <textarea readonly rows="4" class="form-control" id="publicationremarks"
                                name="publication_remarks">
        </textarea>
                        </div>
                    </div>
                    <div class="form-group col-sm-6">
                        <div class="form-group">
                            <label class="control-label" style="margin-bottom: 0px;">Approved Status</label>
                            <input type="text" readonly class="form-control" id="approved_status_publication"
                                name="approved_status_publication">
                        </div>
                    </div>
                    <div class="col-sm-12" style="padding: 0px 15px 10px 5px; ">
                        <div class="form-group col-sm-12" style="padding:7px 0 7px 8px;">
                            <label class="control-label" id="label_approved_publication_remarks"
                                style="margin-bottom: 0px;">Approved Remarks</label>
                            <textarea readonly rows="4" class="form-control" id="approved_publication_remarks"
                                name="approved_publication_remarks">
              </textarea>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<div class="modal fade" id="view_interest_details" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel"
    aria-hidden="true">
    <div class="modal-dialog" role="document" style="margin: auto;border-radius: .75rem">
        <div class="modal-content" style="width: 50%;">
            <div class="modal-header" style="border-top-left-radius: .75rem;border-top-right-radius: .75rem;">
                <h4 class="modal-title">Interests</h4>
                <button type="button" class="close" data-dismiss="modal"><i class="fa fa-times" aria-hidden="true"
                        style="color: #d80403;font-size: 21px;"></i></button>
            </div>
            <form enctype="multipart/form-data" id="update_form_staff_interests" action="" class="form-horizontal"
                data-parsley-validate method="post">
                <input type="hidden" name="interests_id" id="interestsId">
                <div class="modal-body" style="overflow: scroll;">
                    <div class="col-sm-12" style="margin-bottom: 10px; padding-left:0px;
          padding-right:0px;">
                        <div class="form-group col-sm-6">
                            <div class="form-group">
                                <label class="control-label" style="margin-bottom: 0px;"> Area of Interests</label>
                                <input type="text" readonly class="form-control" id="area_of_staff_interest"
                                    name="area_of_staff_interest">
                            </div>
                        </div>
                        <div class="form-group col-sm-6">
                            <div class="form-group">
                                <label class="control-label" style="margin-bottom: 0px;"> Specific Interest</label>
                                <input type="text" readonly class="form-control" id="view_specify_interst"
                                    name="specify_interest_update">
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-12" style="margin-bottom: 10px; padding-left:0px;
          padding-right:0px;">
                        <div class="form-group col-sm-6 remove_edit_form">
                            <div class="form-group">
                                <label class="control-label" style="margin-bottom: 0px;"> Created By</label>
                                <input type="text" readonly class="form-control" id="created_by" name="created_by">
                            </div>
                        </div>
                        <div class="form-group col-sm-6 remove_edit_form">
                            <div class="form-group">
                                <label class="control-label" style="margin-bottom: 0px;"> Created On</label>
                                <input type="text" readonly class="form-control" id="created_on" name="created_on">
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-12" style="padding: 0px 15px 10px 5px; ">
                        <div class="form-group col-sm-12" style="padding:7px 0 7px 8px;">
                            <label class="control-label" style="margin-bottom: 0px;"> Achievements if any</label>
                            <textarea readonly rows="4" cols="500" class="form-control" id="achievements_view"
                                name="achievements_update">
            </textarea>
                        </div>
                    </div>
                    <div class="form-group col-sm-6">
                        <div class="form-group">
                            <label class="control-label" style="margin-bottom: 0px;">Approved Status</label>
                            <input type="text" readonly class="form-control" id="approved_status_interest"
                                name="approved_status_interest">
                        </div>
                    </div>
                    <div class="col-sm-12" style="padding: 0px 15px 10px 5px; ">
                        <div class="form-group col-sm-12" style="padding:7px 0 7px 8px;">
                            <label class="control-label" id="label_approved_interest_remarks"
                                style="margin-bottom: 0px;">Approved Remarks</label>
                            <textarea readonly rows="4" class="form-control" id="approved_interest_remarks"
                                name="approved_interest_remarks">
              </textarea>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<div id="_staff_inititiative_add_modal" class="modal fade" tabindex="-1" role="dialog" data-backdrop="static">
    <div class="modal-dialog modal-dialog-scrollable" style="margin:auto;">
        <div class="modal-content" style="width:48%;border-radius: 8px;">
            <div class="modal-header" style="border-bottom: 2px solid #ccc;">
                <h4 class="modal-title" id="modal-title">Add New Initiative</h4>
                <button style="font-size: 32px;font-weight: bold;color: #e04b4a;opacity: 1;padding-top: .5rem;"
                    type="button" class="close" data-dismiss="modal">&times;</button>
            </div>
            <div class="modal-body">
                <form enctype="multipart/form-data"
                    action="<?php echo site_url('staff/Staff_controller/insertStaffInitiative/' . $staff_id) ?>"
                    method="post" id="initiative_form" class="form-horizontal" data-parsley-validate>
                    <div class="form-group">
                        <label for="sms_content" class="control-label">Initiative</label>
                        <input type="text" name="initiative_name" id="initiative_name" placeholder="Enter Topic"
                            class="form-control" required>
                    </div>

                    <div class="form-group">
                        <label for="sms_content" class="control-label"> Who attended ?</label>
                        <textarea type="text" name="who_attend" id="who_attend" value="" class="form-control"
                            style="height: 50px;" placeholder="Enter Staffs Name, All Staff, Students ?"
                            required></textarea>
                        <small class="text-muted"> [Eg: Staffs Name, All Staff, Students, etc..]</small>
                    </div>

                    <div class="form-group">
                        <label for="sms_content" class="control-label"> Supporting Document</label>
                        <input type="file" name="staff_initiative_document" id="document" class="form-control"
                            accept="application/pdf">
                        <span class="help-block" id="msges">Allowed file types - pdf; Allowed size - upto 5Mb</span>
                    </div>

                    <div class="d-flex" style="justify-content: space-around;">
                        <div class="form-group" style="width: 48%;">
                            <label for="sms_content" class="control-label">From Date</label>
                            <input type="date" id="from_date" name="from_date" autocomplete="off"
                                placeholder="Enter <?php echo date('d-m-Y') ?>" class="form-control">
                            <small class="text-muted"></small>
                        </div>

                        <div class="form-group" style="width: 48%;">
                            <label for="sms_content" class="control-label">To Date</label>
                            <input type="date" id="to_date" name="to_date" autocomplete="off"
                                placeholder="Enter <?php echo date('d-m-Y') ?>" class="form-control">
                            <small class="text-muted"></small>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="sms_content" class="control-label">Initiative Details</label>
                        <textarea class="form-control" placeholder="Enter Initiative Details" name="initiative_details"
                            id="initiative_details"></textarea>
                    </div>

                    <input type="hidden" name="sub_type" id="sub_type" value="">
                    <input type="hidden" name="initiative_id" id="initiative_id" value="">
            </div>
            </form>
            <div class="modal-footer">
                <a class="btn btn-secondary" data-dismiss="modal" id="btn">Cancel</a>
                <button type="button" class="btn btn-primary" id="sub_btn"
                    onClick="insertStaffInitiative()">Submit</button>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="show_award_modal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel1"
    aria-hidden="true">
    <div class="modal-dialog" role="document" style="margin: auto;border-radius: .75rem">
        <div class="modal-content" style="width: 50%;">
            <div class="modal-header" style="border-top-left-radius: .75rem;border-top-right-radius: .75rem;">
                <h4 class="modal-title">Awards</h4>
                <button type="button" class="close" data-dismiss="modal"><i class="fa fa-times" aria-hidden="true"
                        style="color: #d80403;font-size: 21px;"></i></button>
            </div>
            <form>
                <div class="modal-body" style="overflow: scroll;">
                    <div class="col-sm-12" style="margin-bottom: 10px; padding-left:0px;
            padding-right:0px;">
                        <div class="form-group col-sm-6">
                            <div class="form-group">
                                <label class="control-label" style="margin-bottom: 0px;"> Award Name</label>
                                <input type="text" readonly class="form-control" id="show_award_name"
                                    name="show_award_name">
                            </div>
                            <div class="form-group">
                                <label class="control-label" style="margin-bottom: 0px;"> Awarded On</label>
                                <input type="text" readonly class="form-control" id="show_awarded_on"
                                    name="show_awarded_on">
                            </div>

                        </div>
                        <div class="form-group col-sm-6">
                            <div class="form-group">
                                <label class="control-label" style="margin-bottom: 0px;"> Award Cash</label>
                                <input type="text" readonly class="form-control" id="show_cash" name="show_cash">
                            </div>
                            <div class="form-group">
                                <label class="control-label" style="margin-bottom: 0px;"> Awarded By</label>
                                <input type="text" readonly class="form-control" id="show_awarded_by"
                                    name="show_awarded_by">
                            </div>
                        </div>


                    </div>
                    <div class="col-sm-12" style="padding: 0px 15px 10px 5px; ">
                        <div class="form-group col-sm-12" style="padding:7px 0 7px 8px;">
                            <label class="control-label" style="margin-bottom: 0px;"> Remarks</label>
                            <textarea readonly rows="4" cols="500" class="form-control" id="show_remarks_awards"
                                name="show_remarks_awards">
              </textarea>
                        </div>
                    </div>


                    <div class="form-group col-sm-6">
                        <div class="form-group">
                            <label class="control-label" style="margin-bottom: 0px;"> Created On</label>
                            <input type="text" readonly class="form-control" id="show_created_on_awards"
                                name="show_created_on_awards">
                        </div>
                        <div class="form-group">
                            <label class="control-label" style="margin-bottom: 0px;"> Created By</label>
                            <input type="text" readonly class="form-control" id="show_created_by_awards"
                                name="show_created_by_awards">
                        </div>
                    </div>
                    <div class="form-group col-sm-6">
                        <div class="form-group">
                            <label class="control-label" style="margin-bottom: 0px;"> Disabled By</label>
                            <input type="text" readonly class="form-control" id="show_disabled_by_awards"
                                name="show_disabled_by_awards">
                        </div>
                        <div class="form-group">
                            <label class="control-label" style="margin-bottom: 0px;"> Status</label>
                            <input type="text" readonly class="form-control" id="show_status_awards"
                                name="show_status_awards">
                        </div>
                    </div>
                    <div class="form-group col-sm-6">
                        <div class="form-group">
                            <label class="control-label" style="margin-bottom: 0px;">Approved Status</label>
                            <input type="text" readonly class="form-control" id="show_approved_awards_status"
                                name="show_approved_awards_status">
                        </div>
                    </div>
                    <div class="col-sm-12" style="padding: 0px 15px 10px 5px; ">
                        <div class="form-group col-sm-12" style="padding:7px 0 7px 8px;">
                            <label class="control-label" id="show_label_exit_remarks_awards"
                                style="margin-bottom: 0px;">Approved Remarks</label>
                            <textarea readonly rows="4" cols="500" class="form-control" id="show_exit_remarks_awards"
                                name="show_exit_remarks_awards">
              </textarea>
                        </div>
                    </div>

                </div>
            </form>
        </div>
    </div>
</div>

<div class="modal fade" id="edit_family_info_modal" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1"
    aria-labelledby="editFamilyModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editFamilyModalLabel"><b>Edit Details Of <span id="member_name"></span></b>
                </h5>
                <button type="button" class="close" data-dismiss="modal" onclick="close_modal()">&times;</button>
            </div>
            <div class="modal-body">
                <form id="editForm">
                    <!-- <div class="mb-3" id="relation-container">
            <label for="relation" class="form-label">Relation:</label>
            <input type="text" id="relation" name="relation" class="form-control" readonly>
          </div> -->
                    <input type="hidden" id="relation" name="relation">
                    <input type="hidden" value="" name="family_old_value" id="family_old_value">
                    <input type="hidden" value="" name="family_new_value" id="family_new_value">
                    <div class="mb-3" id="first-name-container">
                        <label for="first_name" class="form-label">First Name:</label>
                        <input type="text" id="first_name" name="first_name" class="form-control"
                            placeholder="First Name">
                    </div>
                    <div class="mb-3" id="last-name-container">
                        <label for="name" class="form-label">Last Name:</label>
                        <input type="text" id="last_name" name="last_name" class="form-control" placeholder="Last Name">
                    </div>
                    <div class="mb-3" id="gender-container">
                        <label for="gender" class="form-label">Gender:</label>
                        <select id="gender" name="gender" class="form-control" placeholder="Gender">
                            <option value="-">Select Gender</option>
                            <option value="M">Male</option>
                            <option value="F">Female</option>
                        </select>
                    </div>
                    <div class="mb-3" id="dob-container">
                        <label for="dob" class="form-label">Date of Birth:</label>
                        <input type="text" id="family_dob" name="family_dob" class="form-control"
                            placeholder="Date Of Birth">
                    </div>
                    <div class="mb-3" id="contact-container">
                        <label for="contact" class="form-label">Contact No:</label>
                        <input type="text" id="contact" name="contact" class="form-control" placeholder="Contact No"
                            oninput="validateInput(this)">
                    </div>
                    <div class="mb-3" id="occupation-container">
                        <label for="occupation" class="form-label">Occupation:</label>
                        <input type="text" id="occupation" name="occupation" class="form-control"
                            placeholder="Occupation">
                    </div>
                    <div class="mb-3" id="isDependent-container">
                        <label for="isDependent" class="form-label">Is Dependent:</label>
                        <select id="isDependent" name="isDependent" class="form-control" placeholder="Is Dependent?">
                            <option value="0">No</option>
                            <option value="1">Yes</option>
                        </select>
                    </div>
                    <div class="mb-3" id="includeInsurance-container">
                        <label for="includeInsurance" class="form-label">Include Insurance:</label>
                        <select id="includeInsurance" name="includeInsurance" class="form-control"
                            placeholder="Include Insurance?">
                            <option value="0">No</option>
                            <option value="1">Yes</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-danger" data-dismiss="modal" onclick="close_modal()">Close</button>
                <button type="button" class="btn btn-primary" onclick="save_family_member_details()">Save</button>
            </div>
        </div>
    </div>
</div>

<script type="text/javascript">
function validateInput(input) {
    input.value = input.value.replace(/[^0-9()+-]/g, '');
}

function document_popup() {
    $('#add_staff_document_form').trigger("reset");
    $('#documentName').hide();
}

function awards_popup() {
    $('#add_staff_awards_form').trigger("reset");
}

function qualification_popup() {
    $('#add_qualification_modal').find("input,textarea,select").val('').end().find(
        "input[type=checkbox], input[type=radio]").prop("checked", "").end();

}

function experience_popup() {
    $('#_add_experience_modal').find("input,textarea,select").val('').end().find(
        "input[type=checkbox], input[type=radio]").prop("checked", "").end();
}

function workshop_popup() {
    $('#staff_traning').find("input,textarea,select").val('').end().find("input[type=checkbox], input[type=radio]")
        .prop("checked", "").end();
}

function interest_popup() {
    $('#staff_interests').find("input,textarea,select").val('').end().find("input[type=checkbox], input[type=radio]")
        .prop("checked", "").end();
}

function publication_popup() {
    $('#staff_publicationcircle').find("input,textarea,select").val('').end().find(
        "input[type=checkbox], input[type=radio]").prop("checked", "").end();
}
</script>

<div class="modal fade" id="staff_edit_by_user" data-backdrop="static" role="dialog">
    <div class="modal-dialog">
        <div class="modal-content" style="width:48%;margin: auto;border-radius: .75rem">
            <div class="modal-header" style="border-top-left-radius: .75rem;border-top-right-radius: .75rem;">
                <h4 class="modal-title" id="edit_columnName"></h4>
                <button type="button" class="close" data-dismiss="modal"><i class="fa fa-times" aria-hidden="true"
                        style="color: #d80403;font-size: 21px;"></i></button>
            </div>
            <form enctype="multipart/form-data" method="post" data-parsley-validate="" class="form-horizontal"
                id="save_form_staff_data_by_user">
                <input type="hidden" id="save_get_column_value" name="save_get_column_value">
                <input type="hidden" id="old_value" name="old_value">
                <input type="hidden" id="new_value" name="new_value">
                <input type="hidden" id="profile_selection_tab">
                <div class="modal-body" id="edit_staff_form_group">
                </div>
                <div class="modal-footer">
                    <div class="form-group">
                        <center>
                            <button class="btn btn-secondary" style="width: 10rem;" data-dismiss="modal">Close</button>
                            <a style="width: 10rem;" type="button" onclick="save_staff_profile_by_user()"
                                id="staffProfileSaveButton" class="btn btn-primary enterbuttonsave">Save</a>
                        </center>
                    </div>
                </div>
            </form>

        </div>
    </div>
</div>


<div class="modal fade" id="staff_traning" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel"
    aria-hidden="true" data-backdrop="static">
    <div class="modal-dialog" role="document">
        <div class="modal-content" style="width:48%;margin: auto;border-radius: .75rem">
            <div class="modal-header" style="border-top-left-radius: .75rem;border-top-right-radius: .75rem;">
                <h4 class="modal-title">Staff Traning / Workshop</h4>
                <button type="button" class="close" data-dismiss="modal"><i class="fa fa-times" aria-hidden="true"
                        style="color: #d80403;font-size: 21px;"></i></button>
            </div>
            <form enctype="multipart/form-data" id="training_workshop" class="form-horizontal" data-parsley-validate
                method="post">
                <div class="modal-body" style="overflow: scroll;">
                    <input type="hidden" name="staff_id" value="<?php echo $staff_id ?>">
                    <div class="form-group">
                        <label for="recipient-name" class="col-form-label"> Traning Name <font color="red">*</font>
                        </label>
                        <input type="text" name="trainer_name" required=""
                            data-parsley-required-message="Please enter Traning Name" class="form-control"
                            id="staff-name">
                    </div>
                    <div class="form-group">
                        <label for="message-text" class="col-form-label">Institute Name<font color="red">*</font>
                        </label>
                        <input type="text" required="" data-parsley-required-message="Please enter Institute Name"
                            name="institute_name" class="form-control" id="institute-name">
                    </div>
                    <div class="form-group">
                        <label for="message-text" class="col-form-label">Duration</label>
                        <input type="text" name="duration" class="form-control" id="duration">
                    </div>
                    <div class="form-group">
                        <label for="message-text" class="col-form-label">Traning Related To</label>
                        <input type="text" name="traning_related_to" class="form-control" id="traning_related_to">
                        <span class="help-block">Course / Subject for which the traning was taken</span>
                    </div>
                    <div class="form-group">
                        <label for="message-text" class="col-form-label">Certificate </label>
                        <input type="file" class="form-control verify-size"
                            data-parsley-required-message="Please select a document to upload" name="certificate_name"
                            id="cerificate_img_Id" type="file" accept="application/pdf">
                        <span class="help-block">Allowed file types - pdf; Allowed size - upto 5Mb</span>
                        <span id="file-size-error" style="color: red;"></span>
                        <span id="resource-file_error" style="color: red;"></span>
                        <p id="error1" style="color:red;"></p>
                    </div>
                    <div class="form-group">
                        <label for="message-text" class="col-form-label">Remarks</label>
                        <textarea class="form-control" name="remarks" id="remarks"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                    <button type="button" onclick="trainingworkshop()" id="training_workshop_button"
                        class="btn btn-primary">Submit</button>

                </div>
            </form>
        </div>
    </div>
</div>
<script type="text/javascript">
// $('#trainer_name').on('change', function() {
//   var typeName = $('#trainer_name').val();
//   $('#staff_specific_interest').show();
//   $('#labelName').html('Specific interest in ' + typeName);
// });

function trainingworkshop() {
    var $form = $('#training_workshop');
    var staff_id = '<?php echo $staff_id ?>';
    if ($form.parsley().validate()) {
        var form = $('#training_workshop')[0];
        var formData = new FormData(form);
        $('#training_workshop').trigger("reset");
        $('#training_workshop_button').html('Please wait..').attr('disabled', 'disabled');
        $.ajax({
            url: '<?php echo site_url('staff/Staff_profile_view_controller/upload_staff_training_workshop_by_user/'); ?>' +
                staff_id,
            type: 'post',
            data: formData,
            // async: false,
            processData: false,
            contentType: false,
            // cache : false,
            success: function(data) {
                if (data) {
                    $(function() {
                        new PNotify({
                            title: 'Success',
                            text: 'Insert succcessful!',
                            type: 'success',
                        });
                    });
                } else {
                    $(function() {
                        new PNotify({
                            title: 'Error',
                            text: 'Something went wrong',
                            type: 'error',
                        });
                    });
                }
                $('#staff_traning').modal('hide');
                $('#training_workshop_button').html('Submit').removeAttr('disabled', 'disabled');
                get_staff_profile_data('workshop');
            }
        });
    }
}
</script>

<script type="text/javascript">
$("#cerificate_img_Id").change(function() {
    var file = document.getElementById('cerificate_img_Id');
    if (!file.files[0]) {
        document.getElementById('resource-file_error').innerHTML = 'Resource is required';
        return false;
    }
    document.getElementById('resource-file_error').innerHTML = '';
    var file_size = parseFloat(file.files[0].size / 1024 / 1024);
    var max_size_string = '5MB';
    var max_file_size = parseInt(max_size_string.replace('MB', ''));
    if (file_size > max_file_size) {
        $("#file-size-error").html('File size exceeded.');
        $("#cerificate_img_Id").val('');
    } else {
        $("#file-size-error").html('');
    }
});
</script>

<script type="text/javascript">
function download_workshop_certificate() {
    var staffId = $('#workshop_staff_id').val();
    var workshop_staff_certificate_id = $('#workshop_staff_certificate_id').val();
    window.location.href =
        '<?php echo site_url('staff/Staff_profile_view_controller/stafftraning_workshop_documents_download_by_user/'); ?>' +
        staffId + '/' + workshop_staff_certificate_id;
}
</script>

<div class="modal fade" id="staff_publicationcircle" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel"
    aria-hidden="true" data-backdrop="static">
    <div class="modal-dialog" role="document">
        <div class="modal-content" style="width:48%;margin: auto;border-radius: .75rem">
            <div class="modal-header" style="border-top-left-radius: .75rem;border-top-right-radius: .75rem;">
                <h4 class="modal-title">Publications / Citations</h4>
                <button type="button" class="close" data-dismiss="modal"><i class="fa fa-times" aria-hidden="true"
                        style="color: #d80403;font-size: 21px;"></i></button>
            </div>
            <form enctype="multipart/form-data" id="publication_citation" class="form-horizontal" data-parsley-validate
                method="post">
                <div class="modal-body" style="overflow: scroll;">
                    <input type="hidden" name="staff_id" value="<?php echo $staff_id ?>">
                    <div class="form-group ">
                        <div>
                            <label class="control-label" for="secondary_staff_list">Publication Type<font color="red">*
                                </font></label>
                            <select id="publication_type" title="Select Publication Type" required=""
                                data-parsley-required-message="Please select publication type" name="publication_type"
                                class="form-control">
                                <option value="">Select Publication Type</option>
                                <option value="Book">Book</option>
                                <option value="Blog">Blog</option>
                                <option value="Vlog">Vlog</option>
                                <option value="Citation">Citation</option>
                                <option value="Others">Others</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-group" id="other_publication_type" style="display: none;">
                        <label for="message-text" class="col-form-label" style="margin-top:0px">Others</label>
                        <input type="text" name="publication_type_other" class="form-control"
                            id="publication_type_other">
                    </div>
                    <div class="form-group">
                        <h5 style="margin-bottom:0px;"><b> Publication details:</b></h5>
                        <label for="message-text" class="col-form-label" style="margin-top:0px">Name<font color="red">*
                            </font></label>
                        <input type="text" name="publication_name" required=""
                            data-parsley-required-message="Please enter publication details" class="form-control"
                            id="publication_name">
                    </div>
                    <div class="form-group">
                        <label for="message-text" class="col-form-label">URL</label>
                        <input type="text" name="publication_url" class="form-control" id="publication_url">
                        <p id="error1" style="color:red;"></p>
                    </div>
                    <div class="form-group">
                        <label for="message-text" class="col-form-label">Published on <font color="red"> *</font>
                        </label>
                        <input type="date" name="publication_on" required class="form-control" id="publication_on">
                    </div>
                    <div class="form-group">
                        <label for="message-text" class="col-form-label">Remarks</label>
                        <textarea class="form-control" name="publication_remarks" id="publication_remarks"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                    <button type="button" onclick="staffpublication()" id="publication_citation_button"
                        class="btn btn-primary">Submit</button>
                </div>
            </form>
        </div>
    </div>
</div>


<script type="text/javascript">
$('#publication_type').on('change', function() {
    var others = $('#publication_type').val();
    if (others == 'Others') {
        $('#other_publication_type').show();
    } else {
        $('#other_publication_type').hide();
    }
});

function staffpublication() {
    var $form = $('#publication_citation');
    var staff_id = '<?php echo $staff_id ?>';
    if ($form.parsley().validate()) {
        var form = $('#publication_citation')[0];
        var formData = new FormData(form);
        $('#publication_citation_button').html('Please wait..').attr('disabled', 'disabled');
        $.ajax({
            url: '<?php echo site_url('staff/Staff_profile_view_controller/upload_staff_publication_by_user/'); ?>' +
                staff_id,
            type: 'post',
            data: formData,
            processData: false,
            contentType: false,
            success: function(data) {
                if (data) {
                    $(function() {
                        new PNotify({
                            title: 'Success',
                            text: 'Insert succcessful!',
                            type: 'success',
                        });
                    });
                } else {
                    $(function() {
                        new PNotify({
                            title: 'Error',
                            text: 'Something went wrong',
                            type: 'error',
                        });
                    });
                }
                $('#staff_publicationcircle').modal('hide');
                $('#publication_citation_button').html('Submit').removeAttr('disabled', 'disabled');
                get_staff_profile_data('publications_citations');
            }
        });
    }
}
</script>

<div id="_add_experience_modal" class="modal fade" tabindex="-1" role="dialog" data-backdrop="static">
    <div class="modal-dialog modal-dialog-scrollable" style="margin:auto;">
        <div class="modal-content" style="width: 48%;border-radius: 8px;">
            <div class="modal-header" style="border-bottom: 2px solid #ccc;">
                <h4 class="modal-title" id="modal-title">Add New Experience</h4>
                <button style="font-size: 32px;font-weight: bold;color: #e04b4a;opacity: 1;padding-top: .5rem;"
                    type="button" class="close" data-dismiss="modal">&times;</button>
            </div>
            <div class="modal-body">
                <form id="updateAddExperienceForm" data-parsley-validate method="POST" enctype="multipart/form-data">
                    <div class="form-group">
                        <label for="experience_type">Experience Type</label><span style="color:red">*</span>
                        <input name="experience_type" type="text" class="form-control" id="experience_type"
                            placeholder="Enter Experience Type" data-parsley-required>
                    </div>
                    <div class="form-group">
                        <label for="worked_for">Company/Institution worked for</label><span style="color:red">*</span>
                        <input name="worked_for" type="text" class="form-control" id="worked_for"
                            placeholder="Enter Company/Institution worked for" data-parsley-required>
                    </div>
                    <div class="" style="display: flex;justify-content: space-around;">
                        <div class="form-group" style="width: 48%;">
                            <label for="duration">Duration</label><span style="color:red">*</span>
                            <input name="duration" type="text" class="form-control" id="duration"
                                placeholder="Enter Duration" data-parsley-required>
                        </div>
                        <div class="form-group" style="width: 48%;">
                            <label for="sub_taught">Location</label><span style="color:red">*</span>
                            <input name="location" type="text" class="form-control" id="Location"
                                placeholder="Enter Location" data-parsley-required>
                        </div>
                    </div>
                    <div class="" style="display: flex;justify-content: space-around;">
                        <div class="form-group" style="width: 48%;">
                            <label for="sub_taught">Subjects Taught</label>
                            <input name="sub_taught" type="text" class="form-control" id="sub_taught"
                                placeholder="Enter Subjects Taught">
                        </div>
                        <div class="form-group" style="width: 48%;">
                            <label for="grades_handle">Grades Handled</label>
                            <input name="grades_handle" type="text" class="form-control" id="grades_handle"
                                placeholder="Enter Grades Handled">
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="supporting_doc">Supporting Document <font color="red">*</font></label>
                        <input required type="file" class="form-control" name="staff_experience_doc"
                            id="supporting_doc2" placeholder="Enter Experience Type" accept="application/pdf">
                        <span class="help-block" id="msges2">Allowed file types - pdf; Allowed size - upto 5Mb</span>
                        <p id="error2" style="color:red;"></p>
                    </div>
                    <div class="form-group">
                        <label for="remarks">Remarks</label>
                        <textarea class="form-control" name="remarks" id="remarks"
                            placeholder="Enter Remarks"></textarea>
                    </div>
            </div>
            </form>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                <button type="button" onclick="submit_staff_experience_by_user()" id="updateAddExperienceBtn"
                    class="btn btn-primary">Submit</button>
            </div>
        </div>
    </div>
</div>

<script type="text/javascript">
$("document").ready(function() {
    $('#edit_experience').on('shown.bs.modal', function(e) {
        let dataset = e.relatedTarget.dataset;
        let e_id = dataset.experience_id;
        $("#experience_id").val(`${e_id}`)
    })

    $('#family_dob').datepicker({
        format: 'dd-mm-yyyy',
        autoclose: true,
        placeholder: 'Date Of Birth',
        endDate: new Date()
    });
})

$("#supporting_doc1").change(function() {
    var file = document.getElementById('supporting_doc1');
    if (!file.files[0]) {
        document.getElementById('error1').innerHTML = 'Resource is required';
        return false;
    }
    document.getElementById('error1').innerHTML = '';
    var file_size = parseFloat(file.files[0].size / 1024 / 1024);
    var max_size_string = '5MB';
    var max_file_size = parseInt(max_size_string.replace('MB', ''));
    if (file_size > max_file_size) {
        $("#msges1").html('File size exceeded.');
        setTimeout(() => {
            $("#msges1").html('');
        }, 2000)
        $("#supporting_doc1").val('');
    } else {
        $("#msges1").html('');
    }
});
$("#supporting_doc2").change(function() {
    var file = document.getElementById('supporting_doc2');
    if (!file.files[0]) {
        document.getElementById('error2').innerHTML = 'Resource is required';
        return false;
    }
    document.getElementById('error2').innerHTML = '';
    var file_size = parseFloat(file.files[0].size / 1024 / 1024);
    var max_size_string = '5MB';
    var max_file_size = parseInt(max_size_string.replace('MB', ''));
    if (file_size > max_file_size) {
        $("#msges2").html('File size exceeded.');
        setTimeout(() => {
            $("#msges2").html('');
        }, 2000)
        $("#supporting_doc2").val('');
    } else {
        $("#msges2").html('');
    }
});

let msg = `
  <div style="color:red;text-align:center;
    color: #000;
    border: 2px solid #fffafa;
    text-align: center;
    border-radius: 6px;
    position: relative;
    margin-left: 14px;
    padding: 10px;
    font-size: 14px;
    margin-top: 15px;
    background: #ebf3ff;">
      No Experiences to show
    </div>
  `;



$('#show_exp_modal').on('shown.bs.modal', function(e) {
    let dataset = e.relatedTarget.dataset;
    let e_id = dataset.experience_id;
})


$("#addExperienceBtn").click(function() {
    const form = $("#addExperienceForm")
    if (form.parsley().validate()) {
        $("#addExperienceBtn").text("Please wait...")
        $("#addExperienceBtn").prop("disabled", true);
        $("#addExperienceForm").submit();
    }
})

function getExperiences() {
    var staff_id = '<?php echo $staff_id ?>';
    $.ajax({
        url: "<?php echo site_url('staff/Staff_controller/get_experiences/'); ?>" + $staff_id,
        type: "POST",
        data: {},
        success: function(data) {
            let experiences = $.parseJSON(data);
            if (experiences.length) {
                let html = construct_experiences_data_list(experiences);
                $("#experiences").html(html);
            } else {
                $("#experiences").html(msg);
            }
        }
    })
}

function construct_experiences_data_list(experiences) {
    let html = `
        <table class="table table-bordered">
        <tr class="bg-light">
        <th>#</th>
        <th>Worked For</th>
        <th>Duration</th>
        <th>Experience type</th>
        <th>Status</th>
        <th>Action</th>
        </tr>
        `

    experiences.forEach((data, i) => {
        // style="opacity:${data.status==0 && "0.5"}"
        html += `
            <tr id="exp_row_${data.id}">
            <td id="id_${data.id}" style="opacity:${data.status==0 && "0.5"}">${++i}</td>
            <td id="w_${data.id}" style="opacity:${data.status==0 && "0.5"}"><a data-toggle="modal" data-target="#show_exp_modal" data-experience_id="${data.id}" onClick="getParticularExperience(${data.id})" style="text-decoration: underline;cursor:pointer;">${data.worked_for}</a></td>
            <td id="d_${data.id}" style="opacity:${data.status==0 && "0.5"}">${data.duration}</td>
            <td id="e_${data.id}" style="opacity:${data.status==0 && "0.5"}">${data.experience_type}</td>
            <td id="s_${data.id}" style="opacity:${data.status==0 && "0.5"}" id="status_${data.id}">${data.status=="1" && "Enabled" || "Disabled"}</td>
            <td>
            `

        if (data.status == 0) {
            html +=
                `<a class="btn btn-danger round" style="margin:5px;" onClick="disableParticularExperience(${data.id},'1')">Disabled</a>`
        } else {
            html +=
                `
                <a class="btn btn-info round" onClick="getParticularExperience(${data.id})" data-toggle="modal" data-target="#edit_experience" data-experience_id="${data.id}">Edit</a>
                <a class="btn btn-warning round" style="margin:5px;" id="disable_btn_${data.id}" onClick="disableParticularExperience(${data.id},'0')">Disable</a>`
        }

        html += ` </td>
            </tr>
            `
    })

    html += `</table>`
    return html;
}
</script>

<script type="text/javascript">
$("#document").change(function() {
    var file = document.getElementById('document');
    if (!file.files[0]) {
        document.getElementById('error1').innerHTML = 'Resource is required';
        return false;
    }
    document.getElementById('error1').innerHTML = '';
    var file_size = parseFloat(file.files[0].size / 1024 / 1024);
    var max_size_string = '5MB';
    var max_file_size = parseInt(max_size_string.replace('MB', ''));
    if (file_size > max_file_size) {
        $("#msges").html('File size exceeded.');
        setTimeout(() => {
            $("#msges").html('');
        }, 100)
        $("#document").val('');
    } else {
        $("#msges").html('');
    }
});

function insertStaffInitiative() {
    var $form = $('#initiative_form');
    var staff_id = '<?php echo $staff_id ?>';
    if ($form.parsley().validate()) {
        var form = $('#initiative_form')[0];
        var formData = new FormData(form);
        $('#sub_btn').html('Please wait..').attr('disabled', 'disabled');
        $.ajax({
            url: "<?php echo site_url('staff/Staff_profile_view_controller/upload_staff_initiative_by_user/'); ?>" +
                staff_id,
            type: 'post',
            data: formData,
            processData: false,
            contentType: false,
            success: function(data) {
                if (data) {
                    $(function() {
                        new PNotify({
                            title: 'Success',
                            text: 'Insert succcessful!',
                            type: 'success',
                        });
                    });
                } else {
                    $(function() {
                        new PNotify({
                            title: 'Error',
                            text: 'Something went wrong',
                            type: 'error',
                        });
                    });
                }
                $('#_staff_inititiative_add_modal').modal('hide');
                $('#sub_btn').html('Submit').removeAttr('disabled', 'disabled');
                get_staff_profile_data('initiative');
            }
        });
    }
}

function clearInputFields() {
    $("#modal-title").text(`Add New Initiative`)
    $("#btn_sub").text(`Submit`)
    $("#sub_type").val(`add`)
    $("#from_date").val(``)
    $("#to_date").val(``)
    $("#registerSubmit").show()
    $("#initiative_name").val(``)
    $("#who_attend").val(``)
    $("#initiative_details").val(``)
    $("#btn").show()
}

function getAllStaffInitiative() {
    $.ajax({
        url: "<?php echo site_url('staff/Staff_controller/get_all_staff_initiative/' . $staff_id) ?>",
        type: "POST",
        data: {},
        success: function(data) {
            let initiativeArray = $.parseJSON(data);
            if (initiativeArray.length) {
                let html = constructStaffInitiativeTable(initiativeArray);
                $("#initiative_table").html(html)
            } else {
                $("#initiative_table").html(msg)
            }

        }
    })
}

function constructStaffInitiativeTable(initiativeArray) {
    let html = ``
    html += `
    <table class="table table-bordered">
      <tr class="bg-light">
        <th>#</th>
        <th>Initiative</th>
        <th>On</th>
        <th>Approved By</th>
        <th>Status</th>
        <th>Action</th>
      </tr>
    `

    initiativeArray.forEach((data, i) => {
        let from_date = data.from_date.split("-").reverse().join("-")
        let to_date = data.to_date.split("-").reverse().join("-")
        html += `
      <tr>
      <td class="${data.status==0 && "opacity"}">${++i}</td>
      <td class="${data.status==0 && "opacity"}"><a class="" style="text-decoration:underline;cursor:pointer;" data-toggle="modal" data-target="#_show_detailed_initiative_modal" onClick="getParticularStaffInitiative(${data.id})">${data.initiative_name}</a></td>
      <td class="${data.status==0 && "opacity"}">${from_date} <sapn style="color:lightgray">to</sapn> ${to_date}</td>
      <td class="${data.status==0 && "opacity"}">${data.approved_by || "NA"}</td>
      <td class="${data.status==0 && "opacity"}">${data.status==1 && "Enabled" || "Disabled"}</td>
      <td>`

        html += ` </td>
      </tr>
      `
    })

    html += `</table>`

    return html;
}

function getParticularStaffInitiative(id) {
    const staffId = '<?php echo $staff_id ?>';
    $.ajax({
        url: "<?php echo site_url('staff/Staff_profile_view_controller/get_particular_staff_initiative_by_user/') ?>" +
            staffId,
        type: "POST",
        data: {
            "id": id
        },
        success: function(data) {
            const initiative = $.parseJSON(data);

            var downloadurl =
                '<?php echo site_url('staff/Staff_profile_view_controller/staff_initiative_documents_download_by_user/') ?>' +
                id + '/' + staffId;
            let download =
                `<a target="_blank" class="btn btn-info " data-placement="top" data-toggle="tooltip" data-original-title="Download" href="${downloadurl}">Download <i class="fa fa-cloud-download"></i></a>`

            if (initiative.document != null) {
                $("#download_link_initiative").html(download);
            } else {
                $("#download_link_initiative").html("No Document Present");
            }

            // confirm modal code
            if (initiative.status == 0) {
                $("#initiative_status").text('Enable')
            } else {
                $("#initiative_status").text('Disable')
            }

            //for showing the initiative code


            // for editing the initiative code
            $('#show_approved_remarks_initiative').hide();
            $('#label_show_approved_remarks_initiative').hide();
            $("#initiatives_name").text(`${initiative.initiative_name}`)
            $("#sub_type").val(`edit`)
            $("#btn_sub").text(`Save Changes`)
            $("#registerSubmit").hide()
            $("#modal-title").text(`Initiative`)
            $("#initiative_name").val(`${initiative.initiative_name}`)
            $("#who_attend").val(`${initiative.who_attend}`)
            $("#from_date").val(`${initiative.from_date}`)
            $("#to_date").val(`${initiative.to_date}`)
            $("#initiative_id").val(`${id}`)
            $("#initiative_details").val(`${initiative.initiative_details}`)

            function formatDate(date) {
                return date.split(" ").splice(0, 1).join(" ").split("-").reverse().join("-")
            }

            let created_on_date = formatDate(initiative.created_at)
            let fromDate = formatDate(initiative.from_date)
            let toDate = formatDate(initiative.to_date)


            $("#details_initiative").text(`${initiative.initiative_name}`)
            $("#show_initiative").val(`${initiative.initiative_name}`)
            $("#show_attend").val(`${initiative.who_attend}`)
            $("#show_initiative_details").val(`${initiative.initiative_details}`)
            $("#show_from_date").val(`${fromDate}`)
            $("#show_to_date").val(`${toDate}`)
            $("#show_created_on_initiative").val(`${created_on_date}`)
            $("#show_created_by_initiative").val(`${initiative.created_by_name}`)
            $("#show_approved_on_").val(
                `${initiative.approved_on.toString().split(" ").splice(0,1).toString().split("-").reverse().join("-")}`
            )
            $("#show_approved_by").val(`${initiative.approved_by || "NA"}`)
            $("#show_initiative_approved_status").val(`${initiative.approved_status }`);

            if (initiative.approved_status == 'Approved') {
                $('#show_approved_remarks_initiative').show();
                $('#label_show_approved_remarks_initiative').show().value(
                    `${initiative.approved_rejected_comments}`);

            }
            $("#show_status_initiative").val(`${initiative.status==1 && "Enabled" || "Disabled"}`)
        }
    })
}
</script>


<div class="modal fade" id="staff_interests" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel"
    aria-hidden="true" data-backdrop="static" Staff Interest>
    <div class="modal-dialog" role="document">
        <div class="modal-content" style="width:48%;margin: auto;border-radius: .75rem">
            <div class="modal-header" style="border-top-left-radius: .75rem;border-top-right-radius: .75rem;">
                <h4 class="modal-title">Interest</h4>
                <button type="button" class="close" data-dismiss="modal"><i class="fa fa-times" aria-hidden="true"
                        style="color: #d80403;font-size: 21px;"></i></button>
            </div>
            <form enctype="multipart/form-data" id="staff_interest_filed" class="form-horizontal" data-parsley-validate
                method="post">
                <div class="modal-body" style="overflow: scroll;">
                    <input type="hidden" name="staff_id" value="<?php echo $staff_id ?>">
                    <div class="form-group ">
                        <div>
                            <label class="control-label" for="secondary_staff_list">Area of Interest<font color="red">*
                                </font></label>
                            <select id="interests_type" title="Select Publication Type" required=""
                                data-parsley-required-message="Please select Area of Interests" name="interests_type"
                                class="form-control">
                                <option value="">Select Area of Interest</option>
                                <option value="Sports">Sports</option>
                                <option value="Dramatics">Dramatics</option>
                                <option value="Literature">Literature</option>
                                <option value="Music">Music</option>
                                <option value="Dance">Dance</option>
                                <option value="Others">Others</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-group" id="staff_specific_interest" style="display: none;">
                        <label for="message-text" id="labelName" class="col-form-label" style="margin-top:0px"></label>
                        <input type="text" name="specify_interest"
                            data-parsley-required-message="Please enter Specific interest" required class="form-control"
                            id="sports_interest">
                    </div>
                    <div class="form-group">
                        <label for="message-text" class="col-form-label">Achievements if any</label>
                        <textarea class="form-control" name="achievements" id="achievements"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                    <button type="button" onclick="staffinterestarea()" id="interest_button_insert"
                        class="btn btn-primary">Submit</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script type="text/javascript">
$('#interests_type').on('change', function() {
    var typeName = $('#interests_type').val();
    $('#staff_specific_interest').show();
    $('#labelName').html('Specific interest in ' + typeName);
});

function staffinterestarea() {
    var $form = $('#staff_interest_filed');
    var staff_id = '<?php echo $staff_id ?>';
    if ($form.parsley().validate()) {
        var form = $('#staff_interest_filed')[0];
        var formData = new FormData(form);
        $('#interest_button_insert').html('Please wait..').attr('disabled', 'disabled');
        $.ajax({
            url: '<?php echo site_url('staff/Staff_profile_view_controller/upload_staff_interest_by_user/'); ?>' +
                staff_id,
            type: 'post',
            data: formData,
            processData: false,
            contentType: false,
            success: function(data) {
                if (data) {
                    $(function() {
                        new PNotify({
                            title: 'Success',
                            text: 'Insert succcessful!',
                            type: 'success',
                        });
                    });
                } else {
                    $(function() {
                        new PNotify({
                            title: 'Error',
                            text: 'Something went wrong',
                            type: 'error',
                        });
                    });
                }
                $('#staff_interests').modal('hide');
                $('#interest_button_insert').html('Submit').removeAttr('disabled', 'disabled');
                get_staff_profile_data('interest');
            }
        });
    }
}
</script>

<div class="modal fade" id="add_staff_document_by_user" role="dialog" data-backdrop="static">
    <div class="modal-dialog">
        <div class="modal-content" style="width:48%;margin: auto;border-radius: .75rem">
            <div class="modal-header" style="border-top-left-radius: .75rem;border-top-right-radius: .75rem;">
                <h4 class="modal-title">Add Document</h4>
                <button type="button" class="close" data-dismiss="modal"><i class="fa fa-times" aria-hidden="true"
                        style="color: #d80403;font-size: 21px;"></i></button>
            </div>
            <div class="modal-body">

                <form enctype="multipart/form-data" method="post" data-parsley-validate="" class="form-horizontal"
                    id="add_staff_document_form">
                    <div class="card-body px-0">
                        <div class="form-group">
                            <label class="control-label col-md-4">Select Document<font color="red">*</font>
                            </label>
                            <div class="col-md-8">
                                <select class="form-control" required=""
                                    data-parsley-required-message="Please select document name" name="document_for"
                                    id="document_for">

                                </select>
                            </div>
                        </div>
                        <div class="form-group" id="documentName" style="display: none">
                            <label class="control-label col-md-4">Document Name</label>
                            <div class="col-md-8">
                                <input type="text" class="form-control" required value="NA" name="document_name"
                                    id="document_name">
                                <span class="help-block">Please specify document name</span>
                            </div>
                        </div>

                        <div id="wrapper" class="form-group">
                            <label class="col-md-4 control-label" for="netAmount">Upload Document<font color="red">*
                                </font></label>
                            <div class="col-md-8">
                                <input class="form-control" required=""
                                    data-parsley-required-message="Please select a document to upload"
                                    name="document_obj" id="documentId" type="file" accept="application/pdf" />
                                <span class="help-block">Allowed file types - pdf; Allowed size - upto 1Mb</span>
                                <p id="error1" style="color:red;"></p>
                            </div>
                        </div>
                    </div>
            </div>

            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                <button type="button" onclick="submit_staff_document_by_user()" id="staffDocumentButton"
                    class="btn btn-primary">Submit</button>
            </div>
        </div>
        </form>
    </div>
</div>

<div class="modal fade" id="add_staff_document_by_user" role="dialog" data-backdrop="static">
    <div class="modal-dialog">
        <div class="modal-content" style="width:48%;margin: auto;border-radius: .75rem">
            <div class="modal-header" style="border-top-left-radius: .75rem;border-top-right-radius: .75rem;">
                <h4 class="modal-title">Add Document</h4>
                <button type="button" class="close" data-dismiss="modal"><i class="fa fa-times" aria-hidden="true"
                        style="color: #d80403;font-size: 21px;"></i></button>
            </div>
            <div class="modal-body">

                <form enctype="multipart/form-data" method="post" data-parsley-validate="" class="form-horizontal"
                    id="add_staff_document_form">
                    <div class="card-body px-0">
                        <div class="form-group">
                            <label class="control-label col-md-4">Select Document<font color="red">*</font>
                            </label>
                            <div class="col-md-8">
                                <select class="form-control" required=""
                                    data-parsley-required-message="Please select document name" name="document_for"
                                    id="document_for">

                                </select>
                            </div>
                        </div>
                        <div class="form-group" id="documentName" style="display: none">
                            <label class="control-label col-md-4">Document Name</label>
                            <div class="col-md-8">
                                <input type="text" class="form-control" required value="NA" name="document_name"
                                    id="document_name">
                                <span class="help-block">Please specify document name</span>
                            </div>
                        </div>

                        <div id="wrapper" class="form-group">
                            <label class="col-md-4 control-label" for="netAmount">Upload Document<font color="red">*
                                </font></label>
                            <div class="col-md-8">
                                <input class="form-control" required=""
                                    data-parsley-required-message="Please select a document to upload"
                                    name="document_obj" id="documentId" type="file" accept="application/pdf" />
                                <span class="help-block">Allowed file types - pdf; Allowed size - upto 1Mb</span>
                                <p id="error1" style="color:red;"></p>
                            </div>
                        </div>
                    </div>
            </div>

            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                <button type="button" onclick="submit_staff_document_by_user()" id="staffDocumentButton"
                    class="btn btn-primary">Submit</button>


            </div>
        </div>
        </form>



    </div>
</div>


<div class="modal fade" id="add_staff_awards_by_user" role="dialog" data-backdrop="static">
    <div class="modal-dialog">
        <div class="modal-content" style="width:48%;margin: auto;border-radius: .75rem">
            <div class="modal-header" style="border-top-left-radius: .75rem;border-top-right-radius: .75rem;">
                <h4 class="modal-title">Add New Award</h4>
                <button type="button" class="close" data-dismiss="modal"><i class="fa fa-times" aria-hidden="true"
                        style="color: #d80403;font-size: 21px;"></i></button>
            </div>
            <div class="modal-body">

                <form enctype="multipart/form-data" method="post" data-parsley-validate="" class="form-horizontal"
                    id="add_staff_awards_form">
                    <div class="card-body px-0">

                        <div class="" style="display: flex;justify-content: space-around;">
                            <div class="form-group" style="width: 48%;">
                                <label for="award_name">Award Name</label><span style="color:red">*</span>
                                <input name="award_name" type="text" class="form-control" id="award_name"
                                    placeholder="Enter Award Name" data-parsley-required>
                            </div>
                            <div class="form-group" style="width: 48%;">
                                <label for="awarded_by">Awarded By</label><span style="color:red">*</span>
                                <input name="awarded_by" type="text" class="form-control" id="awarded_by"
                                    placeholder="Enter Awarded By" data-parsley-required>
                            </div>
                        </div>

                        <div class="" style="display: flex;justify-content: space-around;">
                            <div class="form-group" style="width: 48%;">
                                <label for="awarded_on">Awarded On</label><span style="color:red">*</span>
                                <input name="awarded_on" type="date" class="form-control" id="awarded_on"
                                    placeholder="Enter Awarded On" data-parsley-required>
                            </div>
                            <div class="form-group" style="width: 48%;">
                                <label for="award_cash_value">Award Cash</label>
                                <input name="award_cash_value" type="number" class="form-control" id="award_cash_value"
                                    placeholder="Enter Award Cash Value">
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="remarks">Remarks</label>

                            <textarea rows="4" class="form-control" name="remarks" id="remarks"
                                placeholder="Enter Remarks"></textarea>
                        </div>
                    </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" data-dismiss="modal">Close</button>
                <a type="button" id="staffAwardsButton" class="btn btn-primary"
                    onclick="submit_staff_awards_by_user()">Submit</a>
            </div>

            </form>

        </div>
    </div>
</div>



<div class="modal fade" id="add_qualification_modal" role="dialog" data-backdrop="static">
    <div class="modal-dialog">
        <div class="modal-content" style="width:48%;margin: auto;border-radius: .75rem">
            <div class="modal-header" style="border-top-left-radius: .75rem;border-top-right-radius: .75rem;">
                <h4 class="modal-title">Add New Qualification</h4>
                <button type="button" class="close" data-dismiss="modal"><i class="fa fa-times" aria-hidden="true"
                        style="color: #d80403;font-size: 21px;"></i></button>
            </div>
            <div class="modal-body">

                <form enctype="multipart/form-data" method="post" data-parsley-validate="" class="form-horizontal"
                    id="add_staff_qualification_form">
                    <div class="card-body px-0">
                        <div class="container">
                            <div class="row">
                                <div class="col-6">
                                    <div class="form-group">
                                        <label for="degree_type">Degree Type</label><span style="color:red">*</span>
                                        <select class="form-control" name="degree_type" id="degree_type" required>
                                            <option value="">Select Degree Type</option>
                                            <?php $degree_type = $this->config->item('degree_type');
                      sort($degree_type);
                      foreach ($degree_type as $type) { ?>
                                            <option value="<?php echo $type ?>"><?php echo $type ?></option>
                                            <?php } ?>
                                            <option value="other">Others</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label for="branch">Combination/Branch</label>
                                        <input name="branch" type="text" class="form-control" id="branch"
                                            placeholder="Enter Branch">
                                    </div>

                                    <div class="form-group">
                                        <label for="duration">Duration of the program</label>
                                        <input type="text" name="duration" class="form-control" id="duration"
                                            placeholder="Enter Duration of the program">
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="form-group">
                                        <label for="qualification">Degree/Program</label><span
                                            style="color:red">*</span>
                                        <select class="form-control" name="qualification" id="qualification" required>
                                            <option value="">Select Degree</option>
                                            <?php $qualification_list = $this->config->item('qualification_list');
                      sort($qualification_list);
                      foreach ($qualification_list as $bNames) { ?>
                                            <option value="<?php echo $bNames ?>"><?php echo $bNames ?></option>
                                            <?php } ?>
                                            <option value="other">Others</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label for="specialization">Specialization(if any)</label>
                                        <input name="specialization" type="text" class="form-control"
                                            id="specialization" placeholder="Enter Specialization">
                                    </div>

                                    <div class="form-group">
                                        <label for="compition_date">Completed Month/Year</label>
                                        <input name="compition_date" type="text" class="form-control"
                                            id="compition_date" placeholder="Enter Completed Month/Year">
                                    </div>
                                </div>
                            </div>
                            <div class="form-group row" style="margin-top: 5px;">
                                <label for="university">University/Institute</label><span style="color:red">*</span>
                                <input name="university" type="text" class="form-control" id="university"
                                    placeholder="Enter University/Institute" required>
                            </div>
                            <div class="form-group row">
                                <label for="document">Supporting document</label>
                                <input required name="staff_qualification_doc" type="file" class="form-control"
                                    id="qualification_document" placeholder="document" accept="application/pdf">
                                <span class="help-block" id="msges">Allowed file types - pdf; Allowed size - upto
                                    5Mb</span>
                                <p id="error1" style="color:red;"></p>
                            </div>

                            <div class="form-group row">
                                <label for="remarks">Additional Remarks</label>
                                <textarea class="form-control" name="remarks" id="remarks"
                                    placeholder="Enter Remarks"></textarea>
                            </div>
                        </div>
                    </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" data-dismiss="modal">Close</button>
                <a type="button" id="staffQualificationButton" class="btn btn-primary"
                    onclick="submit_staff_qualification_by_user()">Submit</a>
                </form>
            </div>
        </div>
    </div>
</div>

<script type="text/javascript">
function get_docs_data() {
    $.ajax({
        url: '<?php echo site_url('staff/Staff_profile_view_controller/get_visible_staff_document_types'); ?>',
        type: "post",
        success: function(data) {
            var p_data = JSON.parse(data);
            var html = ``;
            html += `<option value="">Select</option>`;
            for (var index in p_data) {
                html +=
                    `<option value="${p_data[index].document_name}">${p_data[index].document_name}</option>`;
            }
            // html += `<option value="Others">Others</option>`;

            $("#document_for").html(html);
        }
    });
}



function edit_each_row_staff_data(column) {
    $('#save_get_column_value').val(column);
    // var get_data =$('#edit-staff-'+column).data();
    var get_data = event.currentTarget.dataset
    // $('#staff_edit_by_user').on('shown.bs.modal',function(event){

    //         var get_data = event.relatedTarget.dataset;
    $('#old_value').val(get_data[column]);
    $('#edit_columnName').html(get_data['labe_name']);
    if (get_data['input'] == 'text') {
        $('#edit_staff_form_group').html(construct_text(column, get_data));
    }
    if (get_data['input'] == 'dropdown') {
        $('#edit_staff_form_group').html(construct_dropdown(column, get_data));
    }
    if (get_data['input'] == 'date') {
        $('#edit_staff_form_group').html(construct_date(column, get_data));
    }
    if (get_data['input'] == 'multiple') {
        $('#edit_staff_form_group').html(construct_multiple(column, get_data));
    }
    if (get_data['input'] == 'address') {
        construct_address(column, get_data);
    }

    // });


}

function construct_text(column, targetdata) {
    // console.log(targetdata);

    var textvalue = ''
    if (targetdata[column] != undefined) {
        textvalue = targetdata[column];
    }

    var html = ` <div class="form-group">
            <label class="control-label col-md-3">${targetdata['labe_name']}</label>
            <div class="col-md-6">
                <input type="text" required class="form-control" value="${textvalue}" name="${targetdata['input_name']}" placeholder="${targetdata['input_name']}" id="${column}">
            </div>
            
        </div>`;
    return html;
}

function construct_address_form(address, address_type) {
    var Address_line1 = '';
    var Address_line2 = '';
    var area = '';
    var district = '';
    var state = '';
    var country = '';
    var pin_code = '';
    if (address != null) {
        Address_line1 = address.Address_line1;
        Address_line2 = address.Address_line2;
        area = address.area;
        district = address.district;
        state = address.state;
        country = address.country;
        pin_code = address.pin_code;
    }
    var countrylistItem = '<?php echo json_encode($this->config->item('country')) ?>';
    var countrylist = $.parseJSON(countrylistItem);
    var html = '';
    var addressType = 1;
    if (address_type == 'present_address') {
        addressType = 0;
    }
    html += `<input type="hidden" name="address_type" value="${addressType}">`;
    html += `<input type="hidden" name="avatar_type" value="4">`;
    html += `<input type="hidden" name="update_address">`;
    html += `<div class="form-group">
                    <label class="col-md-2 control-label" for="address_line1">Line 1</label>  
                    <div class="col-md-8">
                        <input type="text" class="form-control" value="${Address_line1}" name="Address_line1" id="address_line1">
                        <input type="hidden" id="old_line1_value" name="old_line1_value" value="${Address_line1}">
                    </div>
                </div>

                <div class="form-group">
                    <label class="col-md-2 control-label" for="address_line2">Line 2</label>  
                    <div class="col-md-8">
                        <input type="text" class="form-control" value="${Address_line2}" name="Address_line2" id="address_line2">
                        <input type="hidden" id="old_line2_value" name="old_line2_value" value="${Address_line2}">
                    </div>
                </div>

                <div class="form-group">
                    <label class="col-md-2 control-label" for="area"> Area</label>  
                    <div class="col-md-8">     
                        <input type="text" class="form-control" value="${area}" name="area" id="area" data-parsley-error-message="Only alphabets and spaces allowed" data-parsley-pattern="^[a-zA-Z ]+$">
                        <input type="hidden" id="old_area_value" name="old_area_value" value="${area}">
                    </div>
                </div>
                <!-- Textarea -->
                <div class="form-group">
                    <label class="col-md-2 control-label" for="district">District</label>  
                    <div class="col-md-8">
                        <input id="district" name="district" type="text" value="${district}" class="form-control input-md" data-parsley-error-message="Only alphabets and spaces allowed" data-parsley-pattern="^[a-zA-Z ]+$">
                        <input type="hidden" id="old_district_value" name="old_district_value" value="${district}">
                    </div>
                </div>

                <div class="form-group">
                    <label class="col-md-2 control-label" for="state">State</label>  
                    <div class="col-md-8"> 
                        <input type="text" id="state" name="state" value="${state}" class="form-control input-md" data-parsley-error-message="Only alphabets and spaces allowed" data-parsley-pattern="^[a-zA-Z ]+$" >
                        <input type="hidden" id="old_state_value" name="old_state_value" value="${state}">
                    </div>
                </div>

                <div class="form-group">
                    <label class="col-md-2 control-label" for="country">Country</label>
                    <div class="col-md-8">
                        <select id="country" name="country" class="form-control input-md">`;
    for (var cn = 0; cn < countrylist.length; cn++) {
        var countrySelected = '';
        if (country == countrylist[cn]) {
            countrySelected = 'selected';
        } else {
            countrySelected = '';
        }
        html += '<option ' + countrySelected + ' value="' + countrylist[cn] + '">' + countrylist[cn] + '</option>';
    }
    html += `</select>
    <input type="hidden" id="old_country_value" name="old_country_value" value="${country}">
                    </div>
                </div>

                <div class="form-group">
                    <label class="col-md-2 control-label" for="pin_code">Pin Code</label>
                    <div class="col-md-8"> 
                        <input id="pin_code" name="pin_code" type="text" value="${pin_code}" class="form-control input-md" data-parsley-type="digits" data-parsley-length="[5, 8]" data-parsley-error-message="Valid Pincode,Digits only">
                    </div>
                    <input type="hidden" id="old_pincode_value" name="old_pincode_value" value="${pin_code}">
                </div>`;
    return html;
}

function construct_address(column, targetdata) {
    $.ajax({
        url: '<?php echo site_url('staff/Staff_profile_view_controller/get_staff_address_by_user'); ?>',
        type: 'post',
        data: {
            'input_name': targetdata['input_name']
        },
        success: function(data) {
            var ret_data = $.parseJSON(data);
            // console.log(ret_data);
            $('#edit_staff_form_group').html(construct_address_form(ret_data, targetdata['input_name']));
        }
    });
}

function confirmSubmit() {
    var form = $('#transport_form');
    console.log(form.parsley().validate())
    if (!form.parsley().validate()) {
        return 0;
    }
    var form = $('#transport_form')[0];
    var formData = new FormData(form);
    
    bootbox.confirm({
        title: "Confirm Submission",
        message: "Are you sure you want to submit this form? Once you submit it, you won't be able to edit it.",
        buttons: {
            confirm: {
                label: "Yes",
                className: "btn-success"
            },
            cancel: {
                label: "No",
                className: "btn-danger"
            }
        },
        callback: function(result) {
            if (result) {
                $('#transport_sbt_btn').html('Please wait')
                $('#transport_sbt_btn').attr('disabled','disabled');
                $.ajax({
                    url: '<?php echo site_url('staff/Staff_profile_view_controller/submit_staff_transport'); ?>',
                    type: 'post',
                    data: formData,
                    processData: false,
                    contentType: false,
                    async: false,
                    success: function(data) {
                        parsed_data = $.parseJSON(data);
                        $('#transport_sbt_btn').removeAttr('disabled');
                        $('#transport_sbt_btn').html('Submit');
                        get_staff_profile_data('transport_request');
                    }
                });
            }
        }
    });
}

function construct_dropdown(column, targetdata) {
    if (column == 'gender') {
        var html = `<div class="form-group">
                <label class="control-label col-md-2">${targetdata['labe_name']}</label>
                <div class="col-md-6">
                    <select class="form-control" name="${targetdata['input_name']}" id="${column}">
                        <option value="">Select Gender</option>`;
        var mselected = '';
        if (targetdata[column] == 'Male') {
            mselected = 'selected';
        } else {
            mselected = '';
        }
        var fselected = '';
        if (targetdata[column] == 'Female') {
            fselected = 'selected';
        } else {
            fselected = '';
        }
        html += '<option ' + mselected + ' value="M">Male</option>';
        html += '<option ' + fselected + ' value="F">Female</option>'
        html += `</select>
                </div>
            </div>`;

        return html;
    }
    if (column == 'marital_status') {

        // console.log(targetdata);
        var html = `<div class="form-group">
                <label class="control-label col-md-2">${targetdata['labe_name']}</label>
                <div class="col-md-6">
                    <select class="form-control" name="${targetdata['input_name']}" id="${column}">`;
        var mselected = '';
        if (targetdata[column] == 'Married') {
            mselected = 'selected';
        }
        var sselected = '';
        if (targetdata[column] == 'Single') {
            sselected = 'selected';
        }

        html += '<option  value="-1">Not Specified</option>';
        html += '<option ' + mselected + ' value="1">Married</option>';
        html += '<option ' + sselected + ' value="0">Single</option>'
        html += `</select>
                </div>
            </div>`;

        return html;
    }

    var nationalityItem = '<?php echo json_encode($this->config->item('nationality')) ?>';
    var bloodGroupItem = '<?php echo json_encode($this->config->item('blood_groups')) ?>';
    var religionItem = '<?php echo json_encode($this->config->item('religions')) ?>';
    var category = '<?php echo json_encode($this->settings->getSetting('category')) ?>';
    var nationality = $.parseJSON(nationalityItem);
    var bloodgroups = $.parseJSON(bloodGroupItem);
    var religions = $.parseJSON(religionItem);
    var category = $.parseJSON(category);
    if (column == 'nationality') {
        var html = `<div class="form-group">
                <label class="control-label col-md-2">${targetdata['labe_name']}</label>
                <div class="col-md-6">
                    <select class="form-control" name="${targetdata['input_name']}" id="${column}">
                        <option value="">Select Nationality</option>`;
        for (var n = 0; n < nationality.length; n++) {
            var nationalitySelected = '';
            if (targetdata[column] == nationality[n]) {
                nationalitySelected = 'selected';
            } else {
                nationalitySelected = '';
            }
            html += '<option ' + nationalitySelected + ' value="' + nationality[n] + '">' + nationality[n] +
                '</option>';
        }
        html += `</select>
                </div>
            </div>`;

        return html;
    }
    if (column == 'blood_group') {
        var html = `<div class="form-group">
                <label class="control-label col-md-2">${targetdata['labe_name']}</label>
                <div class="col-md-6">
                    <select class="form-control" name="${targetdata['input_name']}" id="${column}">
                        <option value="">Select Blood Group</option>`;
        for (var n = 0; n < bloodgroups.length; n++) {
            var bloogGroupSelected = '';
            if (targetdata[column] == bloodgroups[n]) {
                bloogGroupSelected = 'selected';
            } else {
                bloogGroupSelected = '';
            }
            html += '<option ' + bloogGroupSelected + ' value="' + bloodgroups[n] + '">' + bloodgroups[n] + '</option>';
        }
        html += `</select>
                </div>
            </div>`;

        return html;
    }
    if (column == 'religion') {
        var html = `<div class="form-group">
                <label class="control-label col-md-2">${targetdata['labe_name']}</label>
                <div class="col-md-6">
                    <select class="form-control" name="${targetdata['input_name']}" id="${column}">
                        <option value="">Select Religion</option>`;
        for (var n = 0; n < religions.length; n++) {
            var religionsSelected = '';
            if (targetdata[column] == religions[n]) {
                religionsSelected = 'selected';
            } else {
                religionsSelected = '';
            }
            html += '<option ' + religionsSelected + ' value="' + religions[n] + '">' + religions[n] + '</option>';
        }
        html += `</select>
                </div>
            </div>`;

        return html;
    }
    if (column == 'person_with_disability') {
        var html = `<div class="form-group">
                <label class="control-label col-md-2">${targetdata['labe_name']}</label>
                <div class="col-md-6">
                    <select class="form-control" name="${targetdata['input_name']}" id="${column}">
                        <option value="">Select </option>`;
        var selectedYes = '';
        if (targetdata[column] == 'Yes') {
            selectedYes = 'selected';
        }
        var selectedNo = '';
        if (targetdata[column] == 'No') {
            selectedNo = 'selected';
        }
        html += '<option  value="-1">Not Specified</option>';
        html += '<option ' + selectedNo + ' value="No">No</option>';
        html += '<option ' + selectedYes + ' value="Yes">Yes</option>';
        html += `</select>
                </div>
            </div>`;

        return html;
    }

    if (column == 'category') {
        // console.log(targetdata[column]);
        var html = `<div class="form-group">
                <label class="control-label col-md-2">${targetdata['labe_name']}</label>
                <div class="col-md-6">
                    <select class="form-control" name="${targetdata['input_name']}" id="${column}">
                      <option value="">Select </option>`;
        for (var cat in category) {

            var catSelected = '';
            if (targetdata[column] == category[cat]) {
                catSelected = 'selected';
            } else {
                catSelected = '';
            }
            html += '<option ' + catSelected + ' value="' + cat + '">' + category[cat] + '</option>';
        }

        html += `</select>
                </div>
            </div>`;

        return html;
    }

}

function construct_multiple(column, targetdata) {
    var father_f_name = '';
    var father_l_name = '';
    var mother_f_name = '';
    var mother_l_name = '';
    if (targetdata.father_first_name != undefined) {
        father_f_name = targetdata.father_first_name;
    }
    if (targetdata.father_last_name != undefined) {
        father_l_name = targetdata.father_last_name;
    }
    if (targetdata.mother_first_name != undefined) {
        mother_f_name = targetdata.mother_first_name;
    }
    if (targetdata.mother_last_name != undefined) {
        mother_l_name = targetdata.mother_last_name;
    }




    var html = '';
    if (column == 'father_name') {
        html += `<div class="form-group"><label class="control-label col-md-2">First Name</label>
                <div class="col-md-6">
                    <input type="text" class="form-control" required value="${father_f_name}" name="father_first_name" placeholder="Enter first name" id="father_first_name">
                </div>
            </div>
            <div class="form-group">
                <label class="control-label col-md-2">Last Name</label>
                <div class="col-md-6">
                    <input type="text" class="form-control" value="${father_l_name}" name="father_last_name" placeholder="Enter last name" id="father_last_name">
                </div></div>`;
    }

    if (column == 'mother_name') {
        html += `<div class="form-group"><label class="control-label col-md-4">First Name</label>
                <div class="col-md-6">
                    <input type="text" class="form-control" required value="${mother_f_name}" name="mother_first_name" placeholder="Enter first name" id="mother_first_name">
                </div>
            </div>
            <div class="form-group">
                <label class="control-label col-md-4">Last Name</label>
                <div class="col-md-6">
                    <input type="text" class="form-control" value="${mother_l_name}" name="mother_last_name" placeholder="Enter last name" id="mother_last_name">
                </div></div>`;
    }

    return html;
}

function construct_date(column, targetdata) {
    var date = moment(targetdata[column], 'DD-MM-YYYY');
    var dateValue = date.format('YYYY-MM-DD');
    var html = '';
    html += `<div class="form-group">
            <label class="col-md-3 control-label">${targetdata['labe_name']}</label>
            <div class="col-md-8">
              <div class='input-group date'>
                <input value="${dateValue}" type='date' id='${column}' name="${targetdata['input_name']}" class="form-control" />
              </div>
            </div>
          </div>`;
    return html;
}



$(document).keypress(function(e) {
    if ($("#staff_edit_by_user").hasClass('show') && (e.keycode == 13 || e.which == 13)) {
        save_staff_profile_by_user();
    }
});

function save_staff_profile_by_user() {
    var columnName = $('#save_get_column_value').val();
    var new_val = $('#' + columnName).val();
    $('#new_value').val(new_val);
    // return false
    var profileSelectionTab = $('#profile_selection_tab').val();
    // var get_data = $('#edit-staff-'+columnName).data();
    var $form = $('#save_form_staff_data_by_user');
    if ($form.parsley().validate()) {
        var form = $('#save_form_staff_data_by_user')[0];
        var formData = new FormData(form);
        $('#staffProfileSaveButton').text('Please wait ...').attr('disabled', 'disabled');
        $.ajax({
            url: '<?php echo site_url('staff/Staff_profile_view_controller/save_staff_profile_by_user'); ?>',
            type: 'post',
            data: formData,
            // async: false,
            processData: false,
            contentType: false,
            // cache : false,
            success: function(data) {
                // console.log(data);
                // return false;
                $('#staff_edit_by_user').modal('hide');
                if (data.trim()) {
                    // $('#show-edit-staff-' + columnName).append('<span class="label label-success label-form">Saved</span>');
                    $('#savedLabel-' + columnName).show();
                    setTimeout(function() {
                        $('#savedLabel-' + columnName).fadeIn('slow');
                        $('#savedLabel-' + columnName).fadeOut();
                    }, 2000);
                } else {
                    $('#unsavedLabel-' + columnName).show();
                    // $('#show-edit-staff-' + columnName).append('<span class="label label-danger label-form">Un-Success</span>');
                    setTimeout(function() {
                        $('#unsavedLabel-' + columnName).fadeIn('slow');
                        $('#unsavedLabel-' + columnName).fadeOut();
                    }, 2000);
                }
                $('#staffProfileSaveButton').val('Save').removeAttr('disabled');
                get_staff_profile_data(profileSelectionTab);
            }
        });
    }
}

$(document).ready(function() {
    get_docs_data();
    $('#date_of_birth').datepicker({
        viewMode: 'years',
        format: 'DD-MM-YYYY'
    });
    $('.loadingIcon').css('opacity', '0.5');
    $('.opacity').css('opacity', '0.5');
    get_staff_profile_data('personal_info');
});

function get_staff_profile_data(staff_tab = 'personal_info') {
    $('#profile_selection_tab').val(staff_tab);
    $('#loader').show();
    $('.opacity').css('opacity', '0.5');

    $('.no_data_found_hide').show();
    switch_tabwise_data(staff_tab);
    $.ajax({
        url: '<?php echo site_url('staff/Staff_profile_view_controller/get_staff_data_tab_wise') ?>',
        type: 'post',
        data: {
            'staff_tab': staff_tab
        },
        success: function(data) {
            var res = $.parseJSON(data);
            $('#loader').hide();

            $('.loadingIcon').css('opacity', '5');
            $('.opacity').css('opacity', '5');
            console.log(res)
            if (res.length == 0) {
                $('.no_data_found').hide();
                switch (staff_tab) {
                    case 'document':
                        $('#document_data').html(
                            '<tr class="no-data-display"><td colspan="5" style="text-align:center;">No Data Found</td></tr>'
                        );
                        break;
                    case 'awards':
                        $('#awards_data').html(
                            '<tr class="no-data-display"><td colspan="8" style="text-align:center;">No Data Found</td></tr>'
                        );
                        break;
                    case 'qualification':
                        $('#qualification_data').html(
                            '<tr class="no-data-display"><td colspan="7" style="text-align:center;">No Data Found</td></tr>'
                        );
                        break;
                    case 'experience':
                        $('#experience_data').html(
                            '<tr class="no-data-display"><td colspan="6" style="text-align:center;">No Data Found</td></tr>'
                        );
                        break;
                    case 'workshop':
                        $('#workshop_data').html(
                            '<tr class="no-data-display"><td colspan="7" style="text-align:center;">No Data Found</td></tr>'
                        );
                        break;
                    case 'publications_citations':
                        $('#publication_data').html(
                            '<tr class="no-data-display"><td colspan="7" style="text-align:center;">No Data Found</td></tr>'
                        );
                        break;
                    case 'interest':
                        $('#interest_data').html(
                            '<tr class="no-data-display"><td colspan="7" style="text-align:center;">No Data Found</td></tr>'
                        );
                        break;
                    case 'initiative':
                        $('#initiative_data').html(
                            '<tr class="no-data-display"><td colspan="6" style="text-align:center;">No Data Found</td></tr>'
                        );
                        break;
                    case 'transport_request':
                        $('#transport_details').html(collect_staff_transport_request_details());
                        break;
                }
            } else {
                $('.no_data_found_hide').show();
                switch (staff_tab) {
                    case 'personal_info':
                    case 'school_info':
                    case 'address':
                        // case 'payroll_details':
                        //case 'family_info':
                        get_staff_profile_data_view(res);
                        break;
                    case 'document':
                        get_staff_document_data_view(res);
                        break;
                    case 'awards':
                        get_staff_awards_data_view(res);
                        break;
                    case 'qualification':
                        get_staff_qualification_data_view(res);
                        break;
                    case 'experience':
                        get_staff_experience_data_view(res);
                        break;
                    case 'workshop':
                        get_staff_workshop_data_view(res);
                        break;
                    case 'publications_citations':
                        get_staff_publication_data_view(res);
                        break;
                    case 'interest':
                        get_staff_interest_data_view(res);
                        break;
                    case 'initiative':
                        get_staff_initiative_data_view(res);
                        break;
                    case 'payroll_details':
                        get_staff_payroll_data_view(res);
                        break;

                    case 'family_info':
                        get_staff_family_info_view(res);
                        break;
                    case 'transport_request':
                        get_staff_transportation_request_details(res);
                        break;
                    default:
                        get_staff_profile_data_view(res);
                        break;
                }
            }

        }
    });

}

function get_staff_profile_data_view(res) {
    // console.log(res);
    var columns = Object.keys(res);
    // console.log(columns);
    if (res['staff_img'] == '') {
        var image = "<?php echo base_url() . 'assets/img/sample_boy_image.png' ?>";
        $('#staff_profile_img').attr("src", image);
        $('#old_img').val(image);
    } else {
        $('#staff_profile_img').attr("src", res['staff_img']);
        $('#old_img').val(res['staff_img']);
    }


    $('#edit-staff-father_name').attr({
        "data-father_first_name": res['father_first_name'],
        "data-father_last_name": res['father_last_name']
    });
    $('#edit-staff-mother_name').attr({
        "data-mother_first_name": res['mother_first_name'],
        "data-mother_last_name": res['mother_last_name']
    });

    for (var c = 0; c < columns.length; c++) {
        // console.log(res[columns[c]]);
        $('#stafftopProfile-' + columns[c]).html(res[columns[c]]);
        $('#staff-' + columns[c]).html(res[columns[c]]);
        // console.log($('#staff-' + columns[c]));
        // console.log(res[columns[c]]);
        $('#edit-staff-' + columns[c]).attr("data-" + columns[c], res[columns[c]]);
    }
}

function get_staff_payroll_data_view(res) {
    var columns = Object.keys(res);
    for (var c = 0; c < columns.length; c++) {
        $('#staff-' + columns[c]).html(res[columns[c]] ? res[columns[c]] : '-');
        $('#edit-staff-' + columns[c]).attr("data-" + columns[c], res[columns[c]]);
    }
}

function get_staff_awards_data_view(res) {
    var other_html = '';
    for (var o = 0; o < res.length; o++) {
        other_html += '<tr>';
        other_html += '<td>' + (o + 1) + '</td>';
        other_html += '<td>' + res[o].created_on + '</td>';
        other_html += '<td>' + res[o].award_name + '</td>';
        other_html += '<td>' + res[o].awarded_by + '</td>';
        other_html += '<td>' + res[o].awarded_on + '</td>';
        other_html += '<td>' + res[o].award_cash_value + '</td>';
        other_html += '<td>' + res[o].approved_status + '</td>';
        other_html +=
            '<td><a data-toggle="modal" class="btn btn-info" data-target="#show_award_modal" onClick="getParticularAward(' +
            res[o].id + ')">Details</a></td>';
        other_html += '</tr>';
    }
    $('#awards_data').html(other_html);
}

function get_staff_qualification_data_view(res) {
    var other_html = '';
    for (var o = 0; o < res.length; o++) {
        other_html += '<tr>';
        other_html += '<td>' + (o + 1) + '</td>';
        other_html += '<td>' + res[o].qualification + '</td>';
        other_html += '<td>' + res[o].combination_branch + '</td>';
        other_html += '<td>' + res[o].Specialization + '</td>';
        other_html += '<td>' + res[o].University_Institute + '</td>';
        other_html += '<td>' + res[o].approved_status + '</td>';
        other_html +=
            '<td><a data-toggle="modal" class="btn btn-info" data-target="#show_detailed_qualification_modal" onClick="view_qualification_full_details(' +
            res[o].id + ')">Details</a></td>';
        other_html += '</tr>';
    }
    $('#qualification_data').html(other_html);
}

function get_staff_experience_data_view(res) {
    var other_html = '';
    for (var o = 0; o < res.length; o++) {
        other_html += '<tr>';
        other_html += '<td>' + (o + 1) + '</td>';
        other_html += '<td>' + res[o].worked_for + '</td>';
        other_html += '<td>' + res[o].duration + '</td>';
        other_html += '<td>' + res[o].experience_type + '</td>';
        other_html += '<td>' + res[o].approved_status + '</td>';
        other_html +=
            '<td><a data-toggle="modal" class="btn btn-info" data-target="#show_exp_modal" onClick="getParticularExperience(' +
            res[o].id + ')">Details</a></td>';
        other_html += '</tr>';
    }
    $('#experience_data').html(other_html);
}

function get_staff_workshop_data_view(res) {
    var other_html = '';
    for (var o = 0; o < res.length; o++) {
        other_html += '<tr>';
        other_html += '<td>' + (o + 1) + '</td>';
        other_html += '<td>' + res[o].training_name + '</td>';
        other_html += '<td>' + res[o].institute_name + '</td>';
        other_html += '<td>' + res[o].traning_related_to + '</td>';
        other_html += '<td>' + res[o].duration + '</td>';
        other_html += '<td>' + res[o].approved_status + '</td>';
        other_html +=
            '<td><a data-toggle="modal" class="btn btn-info" data-target="#view_training_details" onClick="view_trainingDetailsbyName(' +
            res[o].id + ')">Details</a></td>';
        other_html += '</tr>';
    }
    $('#workshop_data').html(other_html);
}

function get_staff_publication_data_view(res) {
    var other_html = '';
    for (var o = 0; o < res.length; o++) {
        other_html += '<tr>';
        other_html += '<td>' + (o + 1) + '</td>';
        other_html += '<td>' + res[o].publication_type + '</td>';
        other_html += '<td>' + res[o].publication_name + '</td>';
        other_html += '<td>' + res[o].publication_url + '</td>';
        other_html += '<td>' + res[o].publication_on + '</td>';
        other_html += '<td>' + res[o].approved_status + '</td>';
        other_html +=
            '<td><a data-toggle="modal" class="btn btn-info" data-target="#view_publication_details" onClick="view_publicationbyName(' +
            res[o].id + ')">Details</a></td>';
        other_html += '</tr>';
    }
    $('#publication_data').html(other_html);
}

function get_staff_initiative_data_view(res) {
    var other_html = '';
    for (var o = 0; o < res.length; o++) {
        other_html += '<tr>';
        other_html += '<td>' + (o + 1) + '</td>';
        other_html += '<td>' + res[o].initiative_name + '</td>';
        other_html += '<td>' + res[o].from_date + '  <sapn style="color:lightgray">to</sapn>    ' + res[o].to_date +
            '</td>';
        other_html += '<td>' + res[o].who_attend + '</td>';
        other_html += '<td>' + res[o].approved_status + '</td>';
        other_html +=
            '<td><a data-toggle="modal" class="btn btn-info" data-target="#_show_detailed_initiative_modal" onClick="getParticularStaffInitiative(' +
            res[o].id + ')">Details</a></td>';
        other_html += '</tr>';
    }
    $('#initiative_data').html(other_html);
}

function get_staff_interest_data_view(res) {
    var other_html = '';
    for (var o = 0; o < res.length; o++) {
        other_html += '<tr>';
        other_html += '<td>' + (o + 1) + '</td>';
        other_html += '<td>' + res[o].area_of_interest + '</td>';
        other_html += '<td>' + res[o].specify_interest + '</td>';
        other_html += '<td>' + res[o].approved_status + '</td>';
        other_html +=
            '<td><a data-toggle="modal" class="btn btn-info" data-target="#view_interest_details" onClick="view_staffinterest(' +
            res[o].id + ')">Details</a></td>';
        other_html += '</tr>';
    }
    $('#interest_data').html(other_html);
}

function get_staff_document_data_view(res) {
    var document_html = '';
    for (var d = 0; d < res.length; d++) {
        document_html += '<tr>';
        document_html += '<td>' + (d + 1) + '</td>';
        document_html += '<td>' + res[d].created_date + '</td>';
        document_html += '<td>' + res[d].document_name + '</td>';
        document_html += '<td>' + res[d].approved_status + '</td>';
        var doc_download_url =
            '<?php echo site_url('staff/Staff_profile_view_controller/staff_documents_download_by_user/') ?>' + res[d]
            .doc_id + '/' + res[d].staff_id
        document_html +=
            '<td><a target="_blank" class="btn btn-info " data-placement="top" data-toggle="tooltip" data-original-title="Download" href="' +
            doc_download_url + '">Download <i class="fa fa-cloud-download"></i></a></td>';
        document_html += '</tr>';
    }

    $('#document_data').html(document_html);
}

function switch_tabwise_data(staff_tab) {
    // console.log(staff_tab);
    switch (staff_tab) {
        case 'personal_info':
            $('.personal_info').show();
            $('.school_info').hide();
            $('.document').hide();
            $('.address').hide();
            $('.awards').hide();
            $('.qualification').hide();
            $('.experience').hide();
            $('.workshop').hide();
            $('.publications_citations').hide();
            $('.interest').hide();
            $('.initiative').hide();
            $('.payroll_details').hide();
            $('.family_info').hide();
            $('.transport_request').hide();
            break;
        case 'school_info':
            $('.personal_info').hide();
            $('.school_info').show();
            $('.document').hide();
            $('.address').hide();
            $('.awards').hide();
            $('.qualification').hide();
            $('.experience').hide();
            $('.workshop').hide();
            $('.publications_citations').hide();
            $('.initiative').hide();
            $('.interest').hide();
            $('.payroll_details').hide();
            $('.family_info').hide();
            $('.transport_request').hide();
            break;
        case 'document':
            $('.personal_info').hide();
            $('.school_info').hide();
            $('.document').show();
            $('.address').hide();
            $('.awards').hide();
            $('.qualification').hide();
            $('.experience').hide();
            $('.workshop').hide();
            $('.publications_citations').hide();
            $('.initiative').hide();
            $('.interest').hide();
            $('.payroll_details').hide();
            $('.family_info').hide();
            $('.transport_request').hide();
            break;
        case 'address':
            $('.personal_info').hide();
            $('.school_info').hide();
            $('.document').hide();
            $('.address').show();
            $('.awards').hide();
            $('.qualification').hide();
            $('.experience').hide();
            $('.workshop').hide();
            $('.publications_citations').hide();
            $('.initiative').hide();
            $('.interest').hide();
            $('.payroll_details').hide();
            $('.family_info').hide();
            $('.transport_request').hide();
            break;
        case 'awards':
            $('.personal_info').hide();
            $('.school_info').hide();
            $('.document').hide();
            $('.address').hide();
            $('.qualification').hide();
            $('.awards').show();
            $('.experience').hide();
            $('.workshop').hide();
            $('.publications_citations').hide();
            $('.initiative').hide();
            $('.interest').hide();
            $('.payroll_details').hide();
            $('.family_info').hide();
            $('.transport_request').hide();
            break;
        case 'qualification':
            $('.personal_info').hide();
            $('.school_info').hide();
            $('.document').hide();
            $('.address').hide();
            $('.awards').hide();
            $('.qualification').show();
            $('.experience').hide();
            $('.workshop').hide();
            $('.publications_citations').hide();
            $('.initiative').hide();
            $('.interest').hide();
            $('.payroll_details').hide();
            $('.family_info').hide();
            $('.transport_request').hide();
            break;
        case 'experience':
            $('.personal_info').hide();
            $('.school_info').hide();
            $('.document').hide();
            $('.address').hide();
            $('.awards').hide();
            $('.qualification').hide();
            $('.experience').show();
            $('.workshop').hide();
            $('.publications_citations').hide();
            $('.initiative').hide();
            $('.interest').hide();
            $('.payroll_details').hide();
            $('.family_info').hide();
            $('.transport_request').hide();
            break;
        case 'workshop':
            $('.personal_info').hide();
            $('.school_info').hide();
            $('.document').hide();
            $('.address').hide();
            $('.awards').hide();
            $('.qualification').hide();
            $('.experience').hide();
            $('.workshop').show();
            $('.publications_citations').hide();
            $('.initiative').hide();
            $('.interest').hide();
            $('.payroll_details').hide();
            $('.family_info').hide();
            $('.transport_request').hide();
            break;
        case 'publications_citations':
            $('.personal_info').hide();
            $('.school_info').hide();
            $('.document').hide();
            $('.address').hide();
            $('.awards').hide();
            $('.qualification').hide();
            $('.experience').hide();
            $('.workshop').hide();
            $('.publications_citations').show();
            $('.initiative').hide();
            $('.interest').hide();
            $('.payroll_details').hide();
            $('.family_info').hide();
            $('.transport_request').hide();
            break;
        case 'interest':
            $('.personal_info').hide();
            $('.school_info').hide();
            $('.document').hide();
            $('.address').hide();
            $('.awards').hide();
            $('.qualification').hide();
            $('.experience').hide();
            $('.workshop').hide();
            $('.publications_citations').hide();
            $('.initiative').hide();
            $('.interest').show();
            $('.payroll_details').hide();
            $('.family_info').hide();
            $('.transport_request').hide();
            break;
        case 'initiative':
            $('.personal_info').hide();
            $('.school_info').hide();
            $('.document').hide();
            $('.address').hide();
            $('.awards').hide();
            $('.qualification').hide();
            $('.experience').hide();
            $('.workshop').hide();
            $('.publications_citations').hide();
            $('.initiative').show();
            $('.interest').hide();
            $('.payroll_details').hide();
            $('.family_info').hide();
            $('.transport_request').hide();
            break;
        case 'payroll_details':
            $('.personal_info').hide();
            $('.school_info').hide();
            $('.document').hide();
            $('.address').hide();
            $('.awards').hide();
            $('.qualification').hide();
            $('.experience').hide();
            $('.workshop').hide();
            $('.publications_citations').hide();
            $('.initiative').hide();
            $('.interest').hide();
            $('.payroll_details').show();
            $('.family_info').hide();
            $('.transport_request').hide();
            break;
        case 'family_info':
            $('.personal_info').hide();
            $('.school_info').hide();
            $('.document').hide();
            $('.address').hide();
            $('.awards').hide();
            $('.qualification').hide();
            $('.experience').hide();
            $('.workshop').hide();
            $('.publications_citations').hide();
            $('.initiative').hide();
            $('.interest').hide();
            $('.payroll_details').hide();
            $('.family_info').show();
            $('.transport_request').hide();
            break;
        case 'transport_request':
            $('.personal_info').hide();
            $('.school_info').hide();
            $('.document').hide();
            $('.address').hide();
            $('.awards').hide();
            $('.qualification').hide();
            $('.experience').hide();
            $('.workshop').hide();
            $('.publications_citations').hide();
            $('.initiative').hide();
            $('.interest').hide();
            $('.payroll_details').hide();
            $('.family_info').hide();
            $('.transport_request').show();
            break;
        default:
            $('.personal_info').hide();
            $('.school_info').hide();
            $('.document').hide();
            $('.address').hide();
            $('.awards').hide();
            $('.qualification').hide();
            $('.experience').hide();
            $('.workshop').hide();
            $('.publications_citations').hide();
            $('.initiative').hide();
            $('.interest').hide();
            $('.payroll_details').hide();
            $('.transport_request').hide();
            break;


    }
}

function submit_staff_document_by_user() {

    var staff_id = '<?php echo $staff_id ?>';
    var $form = $('#add_staff_document_form');
    if ($form.parsley().validate()) {
        var form = $('#add_staff_document_form')[0];
        var formData = new FormData(form);
        $('#staffDocumentButton').text('Please wait ...').attr('disabled', 'disabled');
        $.ajax({
            url: '<?php echo site_url('staff/Staff_profile_view_controller/upload_staff_documents_by_user/'); ?>' +
                staff_id,
            type: 'post',
            data: formData,
            // async: false,
            processData: false,
            contentType: false,
            cache: false,
            success: function(data) {
                // console.log(data);
                if (data) {
                    $(function() {
                        new PNotify({
                            title: 'Success',
                            text: 'Insert succcessful!',
                            type: 'success',
                        });
                    });
                } else {
                    $(function() {
                        new PNotify({
                            title: 'Error',
                            text: 'Something went wrong',
                            type: 'error',
                        });
                    });
                }
                $('#staffDocumentButton').html('Upload Document').removeAttr('disabled');
                $('#add_staff_document_by_user').modal('hide');
                get_staff_profile_data('document');

            }
        });
    }
}

function submit_staff_awards_by_user() {

    var staff_id = '<?php echo $staff_id ?>';
    var $form = $('#add_staff_awards_form');
    if ($form.parsley().validate()) {
        var form = $('#add_staff_awards_form')[0];
        var formData = new FormData(form);
        $('#staffAwardsButton').text('Please wait ...').attr('disabled', 'disabled');
        $.ajax({
            url: '<?php echo site_url('staff/Staff_profile_view_controller/upload_staff_awards_by_user/'); ?>' +
                staff_id,
            type: 'post',
            data: formData,
            // async: false,
            processData: false,
            contentType: false,
            cache: false,
            success: function(data) {
                // console.log(data);
                if (data) {
                    $(function() {
                        new PNotify({
                            title: 'Success',
                            text: 'Insert succcessful!',
                            type: 'success',
                        });
                    });
                } else {
                    $(function() {
                        new PNotify({
                            title: 'Error',
                            text: 'Something went wrong',
                            type: 'error',
                        });
                    });
                }
                $('#staffAwardsButton').html('Upload').removeAttr('disabled');
                $('#add_staff_awards_by_user').modal('hide');
                get_staff_profile_data('awards');

            }
        });
    }
}

function submit_staff_qualification_by_user() {
    var staff_id = '<?php echo $staff_id ?>';
    var $form = $('#add_staff_qualification_form');
    if ($form.parsley().validate()) {
        var form = $('#add_staff_qualification_form')[0];
        var formData = new FormData(form);
        $('#staffQualificationButton').text('Please wait ...').attr('disabled', 'disabled');
        $.ajax({
            url: '<?php echo site_url('staff/Staff_profile_view_controller/upload_staff_qualification_by_user/'); ?>' +
                staff_id,
            type: 'post',
            data: formData,
            // async: false,
            processData: false,
            contentType: false,
            cache: false,
            success: function(data) {
                // console.log(data);
                if (data) {
                    $(function() {
                        new PNotify({
                            title: 'Success',
                            text: 'Insert succcessful!',
                            type: 'success',
                        });
                    });
                } else {
                    $(function() {
                        new PNotify({
                            title: 'Error',
                            text: 'Something went wrong',
                            type: 'error',
                        });
                    });
                }
                $('#staffQualificationButton').html('Upload').removeAttr('disabled');
                $('#add_qualification_modal').modal('hide');
                get_staff_profile_data('qualification');

            }
        });
    }
}

function submit_staff_experience_by_user() {
    var staff_id = '<?php echo $staff_id ?>';
    var $form = $('#updateAddExperienceForm');
    if ($form.parsley().validate()) {
        var form = $('#updateAddExperienceForm')[0];
        var formData = new FormData(form);
        $('#updateAddExperienceBtn').text('Please wait ...').attr('disabled', 'disabled');
        $.ajax({
            url: '<?php echo site_url('staff/Staff_profile_view_controller/upload_staff_experienceby_user/'); ?>' +
                staff_id,
            type: 'post',
            data: formData,
            // async: false,
            processData: false,
            contentType: false,
            cache: false,
            success: function(data) {
                // console.log(data);
                if (data) {
                    $(function() {
                        new PNotify({
                            title: 'Success',
                            text: 'Insert succcessful!',
                            type: 'success',
                        });
                    });
                } else {
                    $(function() {
                        new PNotify({
                            title: 'Error',
                            text: 'Something went wrong',
                            type: 'error',
                        });
                    });
                }
                $('#updateAddExperienceBtn').html('Upload').removeAttr('disabled');
                $('#_add_experience_modal').modal('hide');
                get_staff_profile_data('experience');

            }
        });
    }
}
$('#document_for').on('change', function() {
    var others = $('#document_for').val();
    if (others == 'Others') {
        $('#documentName').show();
    } else {
        $('#documentName').hide();
    }
});
$(".list-group-item").click(function() {
    // Select all list items
    var listItems = $(".list-group-item");
    // Remove 'active' tag for all list items
    for (let i = 0; i < listItems.length; i++) {
        listItems[i].classList.remove("active");
    }
    // Add 'active' tag for currently selected item
    this.classList.add("active");
});
</script>

<script type="text/javascript">
$('#fileupload').change(function() {
    var src = $(this).val();
    // var isFileOk = validatePhoto(this.files[0])
    if (src && validatePhoto(this.files[0], 'fileupload')) {
        $("#fileuploadError").html("");
        readURL(this);
        $("#photo_staff_profile").show();
    } else {
        this.value = null;
        $('#photo_staff_profile').hide();
    }
});

function validatePhoto(file, errorId) {
    if (file.size > 10000000 || file.fileSize > 10000000) {
        $("#" + errorId + "Error").html("Allowed file size exceeded. (Max. 10 MB)")
        return false;
    }
    if (file.type != 'image/jpeg' && file.type != 'image/jpg' && file.type != 'image/png') {
        $("#" + errorId + "Error").html("Allowed file types are jpeg, jpg and png");
        return false;
    }
    return true;
}

function readURL(input) {
    if (input.files && input.files[0]) {
        var reader = new FileReader();
        reader.onload = function(e) {
            $('#staff_profile_img').attr('src', e.target.result);
            $('#staff_profile_img').css('opacity: 0.5');
        }
        reader.readAsDataURL(input.files[0]);
    }
}

function save_profile_photos(staff_id) {
    $('#percentage-completed_staff').show();

    var file_data = $('#fileupload').prop('files')[0];
    $('#staff_profile_img').css('opacity', '0.3');

    $("#photo_staff_profile").prop('disabled', true).html(
        '<i class="fa fa-spinner fa-spin" style="font-size:20px"></i>');
    completed_promises = 0;
    current_percentage = 0;
    total_promises = 1;
    in_progress_promises = total_promises;
    saveFileToStorage(file_data, staff_id);
}

function saveFileToStorage(file, staff_id) {
    $.ajax({
        url: '<?php echo site_url("S3_controller/getSignedUrl"); ?>',
        type: 'post',
        data: {
            'filename': file.name,
            'file_type': file.type,
            'folder': 'profile'
        },
        success: function(response) {
            // console.log('Response: ',response)
            single_file_progress(0);
            response = $.parseJSON(response);
            var path = response.path;
            var signedUrl = response.signedUrl;
            $.ajax({
                url: signedUrl,
                type: 'PUT',
                headers: {
                    "Content-Type": file.type,
                    "x-amz-acl": "public-read"
                },
                processData: false,
                data: file,
                xhr: function() {
                    var xhr = $.ajaxSettings.xhr();
                    xhr.upload.onprogress = function(e) {
                        // For uploads
                        if (e.lengthComputable) {
                            single_file_progress(e.loaded / e.total * 100 | 0);
                        }
                    };
                    return xhr;
                },
                success: function(response) {
                    savePhoto(path, staff_id, file);
                    $('#percentage-completed_staff').hide();
                    $('#staff_profile_img').css('opacity', '1');
                },
                error: function(err) {
                    // console.log(err);
                    reject(err);
                }
            });
        },
        error: function(err) {
            reject(err);
        }
    });
}

function savePhoto(orginalsizepath, staff_id, file_data) {
    var old_path = $('#old_img').val();
    var form_data = new FormData();
    form_data.append('file', file_data);
    form_data.append('staff_id', staff_id);
    form_data.append('high_quality', orginalsizepath);
    form_data.append('old_path', old_path);
    $.ajax({
        url: '<?php echo site_url('staff/Staff_profile_view_controller/save_staff_profile_photo') ?>',
        type: 'post',
        data: form_data,
        cache: false,
        contentType: false,
        processData: false,
        success: function(data) {
            $("#photo_staff_profile").html('Saved');
            setTimeout(function() {
                $("#photo_staff_profile").prop('disabled', false).html('Save');
                $("#photo_staff_profile").hide('500');
            }, 2000);
        }
    });
}

function single_file_progress(percentage) {
    if (percentage == 100) {
        in_progress_promises--;
        if (in_progress_promises == 0) {
            current_percentage = percentage;
        }
    } else {
        if (current_percentage < percentage) {
            current_percentage = percentage;
        }
    }
    // var progress = document.getElementById('single-file-percentage');
    // progress.style.width = current_percentage+'%';
    $("#percentage-completed_staff").html(`${current_percentage} %`);
    return false;
}

$("#qualification_document").change(function() {
    var file = document.getElementById('qualification_document');
    if (!file.files[0]) {
        document.getElementById('error1').innerHTML = 'Resource is required';
        return false;
    }
    document.getElementById('error1').innerHTML = '';
    var file_size = parseFloat(file.files[0].size / 1024 / 1024);
    var max_size_string = '5MB';
    var max_file_size = parseInt(max_size_string.replace('MB', ''));
    if (file_size > max_file_size) {
        $("#msges").html('File size exceeded.');
        setTimeout(() => {
            $("#msges1").html('');
        }, 100)
        $("#qualification_document").val('');
    } else {
        $("#msges").html('');
    }
});

function view_qualification_full_details(id) {
    $('#approved_remarks_for_qualification').hide();
    $('#approved_label_remarks_for_qualification').hide();
    var staffId = '<?php echo $staff_id ?>';
    $.ajax({
        url: "<?php echo site_url('staff/Staff_profile_view_controller/get_particular_qualification_by_user/'); ?>",
        type: "POST",
        data: {
            "qualification_id": id
        },
        success: function(data) {
            let qualification = $.parseJSON(data);
            // console.log(qualification);
            let date = qualification.created_on
            date = date.split(" ").splice(0, 1).join(" ").split("-").reverse().join("-")

            var downloadurl =
                '<?php echo site_url('staff/Staff_profile_view_controller/staff_qualifications_documents_download_by_user/') ?>' +
                id + '/' + staffId;
            let download =
                `<a target="_blank" class="btn btn-info " data-placement="top" data-toggle="tooltip" data-original-title="Download" href="${downloadurl}">Download <i class="fa fa-cloud-download"></i></a>`
            $("#download_link").html(download)
            // alert(qualification.supporting_document);


            $("#details_degree").text(`${qualification.qualification}`)
            $("#show_degree").val(`${qualification.qualification}`)
            $("#show_branch").val(`${qualification.combination_branch}`)
            $("#show_spec").val(`${qualification.Specialization}`)
            $("#show_uni").val(`${qualification.University_Institute}`)
            $("#show_disabled_by").val(`${qualification.disabled_by_name || "NA"}`)
            $("#show_status").val(`${qualification.status==1 && "Enabled" || "Disabled"}`)
            $("#show_duration").val(`${qualification.duration_of_complete}`)
            $("#show_month").val(`${qualification.completed_month_year}`)
            $("#show_degree_type").val(`${qualification.degree_type}`)
            $("#show_remarks").val(`${qualification.remarks}`)
            $("#show_remarks").val(`${qualification.approved_status}`)
            $("#show_created_by").val(`${qualification.created_by_name}`)
            $("#show_created_on").val(`${date}`)
            $("#show_doc").val(`${qualification.supporting_document || "NA"}`)

            $("#update_duration").val(qualification.duration_of_complete)
            $("#update_branch").val(qualification.combination_branch)
            $("#update_compition_date").val(qualification.completed_month_year)
            $("#update_qualification").val(qualification.qualification)
            $("#update_remarks").val(qualification.remarks)
            $("#update_degree_type").val(qualification.degree_type)
            $("#update_university").val(qualification.University_Institute)
            // console.log(qualification.supporting_document)
            $("#update_specialization").val(qualification.Specialization)
            $("#update_document").val(`${qualification.supporting_document}`)

            $('#show_qualification_approved_status').val(`${qualification.approved_status}`);
            if (qualification.approved_status == 'Approved') {
                $('#approved_remarks_for_qualification').show().val(
                    `${qualification.approved_rejected_comments}`);
            }
        }
    })
}

function getParticularExperience(exp_id) {
    $('#show_approved_remarks_expe').hide();
    $('#label_show_approved_remarks_expe').hide();
    var staffId = '<?php echo $staff_id ?>';
    $.ajax({
        url: "<?php echo site_url('staff/Staff_profile_view_controller/get_particular_experience_by_user/'); ?>" +
            staffId,
        type: "POST",
        data: {
            "experience_id": exp_id
        },
        success: function(data) {
            let experience = $.parseJSON(data);
            //console.log(experience)
            let date = experience.created_on
            date = date.split(" ").splice(0, 1).join(" ").split("-").reverse().join("-")

            var downloadurl =
                '<?php echo site_url('staff/Staff_profile_view_controller/staff_experiences_documents_download_by_user/') ?>' +
                exp_id + '/' + staffId;


            let download =
                `<a target="_blank" class="btn btn-info " data-placement="top" data-toggle="tooltip" data-original-title="Download" href="${downloadurl}">Download <i class="fa fa-cloud-download"></i></a>`
            if (experience.supporting_document != null) {
                $("#download_link_expe").html(download);
            } else {
                $("#download_link_expe").html("No Document Present");
            }

            $("#show_created_on_expe").val(`${date}`);
            $("#show_created_by_expe").val(`${experience.created_by_name}`);
            $("#show_location").val(`${experience.location}`);
            $("#show_sub_taught").val(`${experience.sub_taught}`);
            $("#show_grades_handle").val(`${experience.grades_handle}`);
            $("#show_remarks_expe").val(`${experience.remarks}`);
            $("#show_worked_for").val(`${experience.worked_for}`);
            $("#show_duration_expe").val(`${experience.duration}`);
            $("#show_exp_type").val(`${experience.experience_type}`);
            //$("#approved_status").val(`${experience.approved_status}`);
            $("#show_dis_by").val(`${experience.disabled_by_name || "NA"}`);
            $("#show_status_expe").val(`${experience.status==1 && "Enabled" || "Disabled"}`);
            // $("#show_doc").val(`${experience.supporting_document || "NA"}`);

            // $('#update_download_link').html(download)
            // $("#supporting_doc2").val(`${experience.supporting_document}`)
            $('#approved_status_expe').val(`${experience.approved_status}`);

            if (experience.approved_status == 'Approved') {
                $('#label_show_approved_remarks_expe').show();
                $('#show_approved_remarks_expe').show().val(`${experience.approved_rejected_comments}`);


            }

        }
    })

}

function view_staffinterest(trainId) {
    $('#interestsId').val(trainId);
    $('#area_of_staff_interest').val('');
    $('#view_specify_interst').val('');
    $('#created_by').val('');
    $('#created_on').val('');
    $('#achievements').val('');
    $('#approved_status').val('');
    $('#label_approved_interest_remarks').hide();
    $('#approved_interest_remarks').hide();
    $.ajax({
        url: '<?php echo site_url('staff/Staff_profile_view_controller/staffinterest_view_data_by_user'); ?>',
        type: 'post',
        data: {
            'trainId': trainId
        },
        success: function(data) {
            var resData = $.parseJSON(data);
            $('#area_of_staff_interest').val(resData.area_of_interest);
            $('#view_specify_interst').val(resData.specify_interest);
            $('#created_by').val(resData.created_by);
            $('#created_on').val(resData.created_on);
            $('#achievements_view').val(resData.achievements);
            $('#approved_status_interest').val(resData.approved_status);


            if (resData.approved_status == 'Approved') {
                $('#label_approved_interest_remarks').show();
                $('#approved_interest_remarks').show().val(`${resData.approved_rejected_comments}`);
            }
        }
    });
}

function view_trainingDetailsbyName(trainId, view_edit) {
    $('#interestsId2').val(trainId);
    $('#trainingName').val('');
    $('#instituteName').val('');
    $('#traningRelatedTo').val('');
    $('#traningduration').val('');
    $('#traningaddedby').val('');
    $('#traningaddedon').val('');
    $('#Remarks').val('');
    $('#approved_training_status').val('');
    $('#training_approved_remarks').hide();
    $('#label_training_approved_remarks').hide();
    $.ajax({
        url: '<?php echo site_url('staff/Staff_profile_view_controller/stafftraning_view_data_by_user'); ?>',
        type: 'post',
        data: {
            'trainId': trainId
        },
        success: function(data) {
            var resData = $.parseJSON(data);
            //console.log(resData);
            $('#training_doc').hide();
            $('#training_doc_download').show();
            if (resData.certificate_path == '') {
                $('#training_doc').show();
                $('#training_doc_download').hide();
            }
            $('#trainingName').val(resData.training_name);
            $('#instituteName').val(resData.institute_name);
            $('#traningRelatedTo').val(resData.traning_related_to);
            $('#traningduration').val(resData.duration);
            $('#traningaddedby').val(resData.added_by);
            $('#traningaddedon').val(resData.added_on);
            $('#Remarks').val(resData.remarks);
            $('#workshop_staff_id').val(resData.staff_id);
            $('#workshop_staff_certificate_id').val(resData.id);
            $('#approved_training_status').val(resData.approved_status);

            if (resData.approved_status == 'Approved') {
                $('#training_approved_remarks').show().val(`${resData.approved_rejected_comments}`);

            }

        }
    })
}

function view_publicationbyName(trainId, view_edit) {
    $('#interestsId1').val(trainId);
    $('#publicationtype').val('');
    $('#publicationothers').val('');
    $('#publicationurl').val('');
    $('#publicationname').val('');
    $('#publishedon').val('');
    $('#createdby').val('');
    $('#createdon').val('');
    $('#publicationremarks').val('');
    $('#approved_status_publication').val('');
    $(`#approved_publication_remarks`).hide();
    $(`#label_approved_publication_remarks`).hide();
    $.ajax({
        url: '<?php echo site_url('staff/Staff_profile_view_controller/staffpublication_view_data_by_user'); ?>',
        type: 'post',
        data: {
            'trainId': trainId
        },
        success: function(data) {
            var resData = $.parseJSON(data);
            //console.log(resData);
            $('#publicationtype').val(resData.publicationType);
            $('#publicationurl').val(resData.publication_url);
            $('#publicationname').val(resData.publication_name);
            $('#publishedon').val(resData.publication_on);
            $('#createdby').val(resData.published_by);
            $('#createdon').val(resData.publication_added_on);
            $('#publicationremarks').val(resData.publication_remarks);
            $('#publication_staff_id').val(resData.staff_id);
            $('#approved_status_publication').val(resData.approved_status);

            if (resData.approved_status == 'Approved') {

                $(`#label_approved_publication_remarks`).show();
                $(`#approved_publication_remarks`).show().val(`${resData.approved_rejected_comments}`);

            }

        }
    })
}

function getParticularAward(id) {
    $(`#show_exit_remarks_awards`).hide();
    $(`#show_label_exit_remarks_awards`).hide();
    $.ajax({
        url: "<?php echo site_url('staff/Staff_profile_view_controller/get_particular_award_by_user/' . $staff_id); ?>",
        type: "POST",
        data: {
            "award_id": id
        },
        success: function(data) {
            let award = $.parseJSON(data);
            let createdDate = award.created_on
            createdDate = createdDate.split(" ").splice(0, 1).join(" ").split("-").reverse().join("-")
            // console.log(createdDate);

            let date = award.awarded_on
            date = date.split(" ").splice(0, 1).join(" ").split("-").reverse().join("-")

            $(`#details_award_name`).text(`${award.award_name}`);
            $(`#show_created_on_awards`).val(createdDate);
            $(`#show_created_by_awards`).val(`${award.created_by_name}`);
            $(`#show_awarded_by`).val(`${award.awarded_by}`);
            $(`#show_remarks_awards`).val(`${award.remarks}`);
            $(`#show_award_name`).val(`${award.award_name}`);
            $(`#show_awarded_on`).val(`${date}`);
            $(`#show_cash`).val(`${award.award_cash_value}`);
            $(`#show_awarded_by`).val(`${award.awarded_by}`);
            $(`#show_disabled_by_awards`).val(`${award.disabled_by_name || "NA"}`);
            $(`#show_status_awards`).val(`${award.status=="1" && "Enabled" || "Disabled"}`);
            $(`#show_approved_awards_status`).val(`${award.approved_status}`);

            if (award.approved_status == 'Approved') {
                $(`#show_exit_remarks_awards`).show().val(`${award.approved_rejected_comments}`);
                $(`#show_label_exit_remarks_awards`).show();
            }


            $("#update_award_name").val(award.award_name)
            $("#update_awarded_by").val(award.awarded_by)
            $("#update_awarded_on").val(date)
            $("#update_award_cash_value").val(award.award_cash_value)
            $("#update_remarks").val(award.remarks)

        }
    })
}

// function get_staff_payroll_data_view(res) {

//   var pay_html = '';

//     pay_html += '<tr>';
//     pay_html += '<td>' + (1) + '</td>';
//     pay_html += '<td>' + res.account_number  + '</td>';
//     pay_html += '<td>' + res.ifsc_code + '</td>';
//     pay_html += '<td>' + res.branch_name + '</td>';
//     pay_html += '<td><a style="display:" href="javascript:void(0)"  class="btn btn-warning btn-sm"><span class="fa fa-edit" data-toggle="modal" data-target="#update_payroll_details" onclick="edit_payroll_details(' + res.id + ')" ></span></a></td>';
//     pay_html += '</tr>';

//   $('#payroll_data_view').html(pay_html);

// }
function collect_staff_transport_request_details() {
    var html = `<form enctype="multipart/form-data" method="post" id="transport_form"
                                    action=""
                                    data-parsley-validate="" class="form-horizontal">
                                    <div class="col-md-12">
                                        <div class="form-group">
                                            <label class="control-label col-md-3" style="text-align: right;">Transportation
                                                Required? <font color="red"> *
                                                </font></label>
                                            <div class="col-md-6">
                                                <select class="form-control" onchange="transportation()" name="need_transport"
                                                    id="transport" required="">
                                                    <option value="-1">Select</option>
                                                    <option value="Yes">Yes</option>
                                                    <option value="No">No</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    <div id="no_transport" style="display: none;">
                                        <div class="col-md-12" style="margin-top: 15px;">
                                            <div class="form-group">
                                                <label class="control-label col-md-3"
                                                    style="text-align: right;">Transportation Mode <font color="red"> *
                                                    </font></label>
                                                <div class="col-md-6">
                                                    <select class="form-control" name="transportation_mode"
                                                        id="transportation_mode">
                                                        <option value="">Select Mode</option>
                                                        <option value="Private Transport">
                                                            Private Transport</option>
                                                        <option value="Personal pickup / Drop">
                                                            Personal pickup / Drop</option>
                                                        <option value="Private Van">
                                                            Private Van</option>
                                                        <option value="Own Vehicle (Parent Drop/Pick-up)">
                                                            Own Vehicle (Parent Drop/Pick-up)</option>
                                                        <option value="Walking (Walker)">
                                                            Walking (Walker)</option>
                                                        <option value="Bicycle">
                                                            Bicycle</option>
                                                        <option value="Public Transport (Auto/Bus/Train)">
                                                            Public Transport (Auto/Bus/Train)</option>
                                                        <option value="Other">
                                                            Other</option>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-12">
                                            <div class="form-group">
                                                <label class="control-label col-md-3"
                                                    style="margin-top:15px;text-align:right">Additional
                                                    Details</label>
                                                <div class="col-md-6">
                                                    <textarea name="transport_addition_details" id="" class="form-control"
                                                        style="margin-top:15px;"
                                                        placeholder="Enter Additional Details"></textarea>
                                                    <span class="help-block">Additional details like pickup/drop person name
                                                        and
                                                        contact
                                                        number</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div id="transportation" style="display: none;">
                                        <div class="col-md-12" style="margin-top: 15px;">
                                            <div class="form-group">
                                                <label class="control-label col-md-3" style="text-align: right;">Route /
                                                    Area <font color="red"> *</font>
                                                </label>
                                                <div class="col-md-6">
                                                    <select class="form-control" onchange="get_route_wise_area()"
                                                        name="route" id="routearea">
                                                        <option value="">Select Route/Area</option>
                                                        <?php foreach ($route_area as $key => $area) {?>
                                                        <option value="<?php echo $area ?>"><?php echo $area ?>
                                                        </option>
                                                        <?php } ?>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="col-md-12" style="margin-top: 15px;">
                                            <div class="form-group">
                                                <label class="control-label col-md-3" style="text-align: right;">Stop <font
                                                        color="red"> *</font></label>
                                                <div class="col-md-6">
                                                    <select class="form-control" onchange="stage_viewby_stop()" name="pickup_stop"
                                                        id="stops">
                                                        <option value="">Select Stop</option>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-12" style="margin-top: 15px;">
                                            <div class="form-group">
                                                <label class="control-label col-md-3" style="text-align: right;">Nearest
                                                    Land Mark</label>
                                                <div class="col-md-6">
                                                    <input type="text" class="form-control" id="nearest_land_mark"
                                                        name="nearest_land_mark"
                                                        style="display: block; margin-bottom: 10px;">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-12" style="margin-top: 15px;">
                                            <?php $display_slab_in_transport = $this->settings->getSetting('display_slab_in_transport');
                                        if ($display_slab_in_transport == 1) { ?>
                                            <div class="form-group">
                                                <label class="control-label col-md-3" style="text-align: right;">Slab
                                                </label>
                                                <div class="col-md-6">
                                                    <select class="form-control" readonly name="kilometer" id="kilometerId">
                                                        <option value="">Select Slab</option>
                                                    </select>
                                                </div>
                                            </div>
                                            <?php } ?>
                                        </div>
                                    </div>
                                    <div class="col-md-10" id="transport_btn" style="margin-top: 20px;display:none">
                                        <center>
                                            <button type="button" class="btn btn-primary"
                                                style="border-radius: 0.2rem;margin-top:10px"
                                                onclick="confirmSubmit()" id="transport_sbt_btn">Submit</button>
                                        </center>
                                    </div>
                                </form>`;
                                return html;
}

function get_staff_transportation_request_details(transport_details) {
    console.log(transport_details[0].transport_required);
    var html = '';
    html += `<div class="col-md-12 jContainer" style="width:55%;margin:20px 155px;">
                <div class="jHead"><h4><strong>Your Transportation Request</strong></h4></div>
					<table class="table">
						<tr>
							<th style="width: 50%;">Transport Required </th>
							<td>${transport_details[0].is_transport_required}</td>
						</tr>`;
    if (transport_details[0].transportation_mode != 'School Bus') {
            html += `<tr>
                        <th style="width: 50%;">Transportation Mode</th>
                        <td>${transport_details[0].transportation_mode || '-'}</td>
                    </tr>
                    <tr>
                        <th style="width: 50%;">Transportation Additional Details</th>
                        <td>${transport_details[0].transportation_additional_details || '-'}</td>
                    </tr>`;
    } else {

        html += `<tr>
							<th style="width: 50%;">Route / Area</th>
							<td>${transport_details[0].route || '-'}</td>
						</tr>
                        <tr>
							<th style="width: 50%;">Stop</th>
							<td>${transport_details[0].name || '-'}</td>
						</tr>
                        <tr>
							<th style="width: 50%;">Nearest Land Mark</th>
							<td>${transport_details[0].nearest_land_mark || '-'}</td>
						</tr>
                        `;
                        
         } 
        html += `<tr>
							<th style="width: 50%;">Status</th>
							<td>${transport_details[0].status || '-'}</td>
						</tr>
                        <tr>
							<th style="width: 50%;">Transport Request Submitted By</th>
							<td>${transport_details[0].created_by || '-'}</td>
						</tr>
                        <tr>
							<th style="width: 50%;">Transport Request Submitted On</th>
							<td>${transport_details[0].created_on || '-'}</td>
						</tr>
                        <tr>
							<th style="width: 50%;">Transport Details Add/Edited By</th>
							<td>${transport_details[0].edited_by || '-'}</td>
						</tr>
                        <tr>
							<th style="width: 50%;">Transport Details Add/Edited On</th>
							<td>${transport_details[0].edited_on || '-'}</td>
						</tr>
                        <tr>
							<th style="width: 50%;">Transport Requested Academic year</th>
							<td>${transport_details[0].academic_year}</td>
						</tr>
                        </table>
                </div>`;

    $('#transport_details').html(html);
}

function get_staff_family_info_view(res) {
    if (!res || Object.keys(res).length === 0) {
        document.getElementById('family_info_no_data').style.display = 'block';
        return;
    }
    document.getElementById('family_info_no_data').style.display = 'none';
    var family_instruction = '<?php echo empty($family_instruction) ? '' : $family_instruction ?>';
    let display = family_instruction == '' ? 'd-none' : '';
    let tableHtml = `
        <div class="col-md-12 pl-0 mb-2 ${display}">${family_instruction}</div>
        <table class="table table-bordered">
            <thead>
                <tr>
                    <th>Relation</th>
                    <th>First Name</th>
                    <th>Last Name</th>
                    <th>Gender</th>
                    <th>Date of Birth</th>
                    <th>Contact No</th>
                    <th>Occupation</th>
                    <th>Is Dependent</th>
                    <th>Include Insurance</th>
                    <th>Action</th>
                </tr>
            </thead>
            <tbody>
                ${generateFamilyRow('Father', res.father_first_name, res.father_last_name, res.father_gender, res.father_dob, res.father_contact_no, res.father_occupation, res.father_is_dependent, 1, res.marital_status, res.include_father_insurance)}
                ${generateFamilyRow('Mother', res.mother_first_name, res.mother_last_name, res.mother_gender, res.mother_dob, res.mother_contact_no, res.mother_occupation, res.mother_is_dependent, 2, res.marital_status, res.include_mother_insurance)}
                ${generateFamilyRow('Spouse', res.spouse_name, res.spouse_last_name, res.spouse_gender, res.spouse_dob, res.spouse_contact_no, res.spouse_occupation, res.spouse_is_dependent, 3, res.marital_status, res.include_spouse_insurance)}
                ${generateFamilyRow('Child 1', res.child1_first_name, res.child1_last_name, res.child1_gender, res.child1_dob, res.child1_contact_no, '-', res.child1_is_dependent, 4, res.marital_status, res.include_child1_insurance)}
                ${generateFamilyRow('Child 2', res.child2_first_name, res.child2_last_name, res.child2_gender, res.child2_dob, res.child2_contact_no, '-', res.child2_is_dependent, 5, res.marital_status, res.include_child2_insurance)}
                ${generateFamilyRow('Child 3', res.child3_first_name, res.child3_last_name, res.child3_gender, res.child3_dob, res.child3_contact_no, '-', res.child3_is_dependent, 6, res.marital_status, res.include_child3_insurance)}
            </tbody>
        </table>
    `;

    // Update the table container with the generated HTML
    document.getElementById('family_info_table_container').innerHTML = tableHtml;
    document.getElementById('family_info_table_container').style.display = 'block';
}

function generateFamilyRow(relation, first_name, last_name, gender, dob, contact, occupation, isDependent, i,
    marital_status, include_insurance) {
    let showEditFields =
        '<?php echo isset($showEditfields['family_info']) ? json_encode($showEditfields['family_info']) : "[]"; ?>';
    let edit_enabled = JSON.parse(showEditFields);
    // console.log(showEditFields);
    // return false;

    let memberDetails = {};
    let disable_edit = 'disabled';
    if (edit_enabled.length > 0) {
        edit_enabled.forEach(field => {
            let [memberType] = field.split('_');
            let formattedMemberType = memberType.toLowerCase();

            if (!memberDetails[formattedMemberType]) {
                memberDetails[formattedMemberType] = [];
            }

            memberDetails[formattedMemberType].push(field);
        });

        var formattedRelation = relation.toLowerCase().replace(/ /g, '');
        const isRelationPresent = memberDetails.hasOwnProperty(formattedRelation);
        disable_edit = <?php echo $profile_lock_unlock_status->profile_status !='unlocked' ? 'false' : 'true'; ?> &&
            isRelationPresent ? '' : 'disabled';
    }

    var btn_id = relation.toLowerCase().replace(/ /g, '_');

    var genderDisplay = gender === 'M' ? 'Male' : (gender === 'F' ? 'Female' : '-');

    const rowClass = i % 2 == 0 ? '#f2f2f2' : '';
    var display_settings = (marital_status == 0 && (relation.toLowerCase() === 'spouse' || relation.toLowerCase() ===
        'child 1' || relation.toLowerCase() === 'child 2' || relation.toLowerCase() === 'child 3')) ? 'd-none' : '';
    return `
          <tr style="background:${rowClass}" class="${display_settings}">
              <td>${relation}</td>
              <td>${first_name || '-'}</td>
              <td>${last_name || '-'}</td>
              <td>${genderDisplay}</td>
              <td>${dob || '-'}</td>
              <td>${contact || '-'}</td>
              <td>${occupation || '-'}</td>
              <td>${isDependent == 0 ? 'No' : isDependent == 1 ? 'Yes' : '-'}</td>
              <td>${include_insurance == 0 ? 'No' : include_insurance == 1 ? 'Yes' : '-'}</td>
              <td><button class="btn btn-warning" ${disable_edit} id="edit_btn_${btn_id.toLowerCase()}" onclick="edit('${relation.toLowerCase()}')"><span class="fa fa-edit"></span></button></td>
          </tr>
      `;
}

function edit(relation) {
    let tableRows = document.querySelectorAll("#family_info_table_container tbody tr");
    let targetRow;

    tableRows.forEach((row) => {
        let relationCell = row.querySelector("td");
        if (relationCell.innerText.toLowerCase() == relation) {
            targetRow = row;
        }
    });

    if (!targetRow) {
        console.error(`Relation ${relation} not found.`);
        return;
    }

    // Extract data from the row
    let cells = targetRow.querySelectorAll("td");
    let first_name = cells[1].innerText;
    let last_name = cells[2].innerText;
    let gender = cells[3].innerText;
    let dob = cells[4].innerText;
    let contact = cells[5].innerText;
    let occupation = cells[6].innerText;
    let isDependent = cells[7].innerText.toLowerCase() === "yes" ? "1" : "0";
    let includeInsurance = cells[8].innerText.toLowerCase() === "yes" ? "1" : "0";

    let genderDisplay = gender === 'Male' ? 'M' : (gender === 'Female' ? 'F' : '-');

    document.getElementById("relation").value = relation.toUpperCase();
    document.getElementById("first_name").value = first_name != '-' ? first_name : '';
    document.getElementById("last_name").value = last_name != '-' ? last_name : '';
    document.getElementById("gender").value = genderDisplay;
    document.getElementById("family_dob").value = dob != '-' ? formatDate(dob) : "";
    document.getElementById("contact").value = contact != '-' ? contact : "";
    document.getElementById("occupation").value = occupation != '-' ? occupation : '';
    document.getElementById("isDependent").value = isDependent;
    document.getElementById("includeInsurance").value = includeInsurance;
    $('#member_name').html(relation.toUpperCase());
    $('#editForm :input').each(function () {
        var name = $(this).attr('name');
        if (name) {
            if ($(this).is('select')) {
                // Store selected text for dropdowns
                initialData[name] = $(this).find('option:selected').text().trim();
            } else {
                initialData[name] = $(this).val();
            }
        }
    });
    document.querySelectorAll(".mb-3").forEach(container => {
        container.style.display = "block";
        let input = container.querySelector("input, select");
        if (input) input.removeAttribute("disabled");
    });
    handleFieldRestrictions(relation);
    $('#edit_family_info_modal').show();
}

var changedData = {};
var initial_val = {};
var initialData ={};
    // ✅ Properly collect initial values for all input fields
$(document).ready(function () {
    // Track changes
    $('#editForm :input').on('change input', function () {
        var name = $(this).attr('name');
        if (!name) return;

        var currentVal = $(this).is('select')
            ? $(this).find('option:selected').text().trim()
            : $(this).val();

        var originalVal = initialData[name];

        if (originalVal !== currentVal) {
            initial_val[name] = originalVal;
            changedData[name] = currentVal;
        } else {
            delete initial_val[name];
            delete changedData[name];
        }

        $('#family_old_value').val(JSON.stringify(initial_val));
        $('#family_new_value').val(JSON.stringify(changedData));
    });
});

function handleFieldRestrictions(relation) {
    var btn_id = relation.toLowerCase().replace(/ /g, '_');
    // relation-container, first-name-container, last-name-container, gender-container, dob-container, contact-container, occupation-container, isDependent-container
    let showEditFields =
        '<?php echo isset($showEditfields['family_info']) ? json_encode($showEditfields['family_info']) : "[]"; ?>';
    let edit_enabled = JSON.parse(showEditFields);

    let memberDetails = {};
    edit_enabled.forEach(field => {
        let memberType;
        if (field.startsWith("include_")) {
            memberType = field.split('_')[1];
        } else {
            [memberType] = field.split('_');
        }

        let formattedMemberType = memberType.toLowerCase();

        if (!memberDetails[formattedMemberType]) {
            memberDetails[formattedMemberType] = [];
        }

        memberDetails[formattedMemberType].push(field);
    });

    let containerMapping = {
        "first-name-container": "first_name",
        "last-name-container": "last_name",
        "contact-container": "contact_no",
        "dob-container": "dob",
        "occupation-container": "occupation",
        "gender-container": "gender",
        "isDependent-container": "is_dependent",
        "includeInsurance-container": "include_insurance",
    };

    let formattedRelation = relation.toLowerCase().replace(/ /g, '');
    let fields = memberDetails[formattedRelation] || [];

    document.querySelectorAll(".mb-3").forEach(container => {
        let id = container.id;
        let fieldName = containerMapping[id];

        if (fieldName) {
            let fieldKey = `${formattedRelation}_${fieldName}`;
            if (fieldKey.includes('insurance')) {
                fieldKey = `include_${relation.replace(/ /g, '')}_insurance`;
            }

            if (['spouse_first_name'].includes(fieldKey)) {
                fieldKey = fieldKey.replace('_first', '');
            }

            if (fields.includes(fieldKey) && edit_enabled.includes(fieldKey)) {
                container.style.display = "block";
                let input = container.querySelector("input, select");
                if (input) input.removeAttribute("disabled");
            } else {
                container.style.display = "none";
                let input = container.querySelector("input, select");
                if (input) input.setAttribute("disabled", true);
            }
        } else {
            container.style.display = "none";
            let input = container.querySelector("input, select");
            if (input) input.setAttribute("disabled", true);
        }
    });
}

function close_modal() {
    $('#edit_family_info_modal').hide();
}

function formatDate(dateStr) {
    const parts = dateStr.split("-");
    return `${parts[0]}-${parts[1]}-${parts[2]}`;
}

function save_family_member_details() {
    const form = document.getElementById("editForm");
    const formData = new FormData(form);

    formData.forEach((value, key) => {
        const field = document.querySelector(`[name="${key}"]`);
        if (field && (field.hasAttribute('disabled'))) {
            formData.delete(key);
        }
    });

    // const data = {};
    // formData.forEach((value, key) => {
    //     data[key] = value;
    // });
    // console.log(data);

    $.ajax({
        url: "<?php echo site_url('staff/Staff_profile_view_controller/save_family_member_details'); ?>",
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        success: function(response) {
            // console.log(response);
            var result = JSON.parse(response);
            if (result == 1) {
                $('#edit_family_info_modal').hide();
                Swal.fire({
                    title: 'Success!',
                    text: 'Family Member Details Updated Successfully.',
                    icon: 'success',
                    confirmButtonText: 'OK'
                }).then((result) => {
                    if (result.isConfirmed) {
                        get_staff_profile_data('family_info');
                        form.reset();
                    }
                });
            } else {
                $('#edit_family_info_modal').hide();
                Swal.fire({
                    title: 'Error!',
                    text: 'There was an issue updating the details. Please try again later.',
                    icon: 'error',
                    confirmButtonText: 'OK'
                }).then((result) => {
                    if (result.isConfirmed) {
                        get_staff_profile_data('family_info');
                        form.reset();
                    }
                });
            }
        },
        error: function(error) {
            console.error(error);
            $('#edit_family_info_modal').hide();
            Swal.fire({
                title: 'Error!',
                text: 'There was an issue updating the details. Please try again later.',
                icon: 'error',
                confirmButtonText: 'OK'
            });
            get_staff_profile_data('family_info');
        }
    });

}

function edit_payroll_details(res) {
    $('#update_payroll_details').modal('show');
    $('#payroll_id').val(res);
}

function savePayrollDetails() {
    var form = document.getElementById('payroll_update_form');
    var formData = new FormData(form);
    var accountNumber = document.getElementById('account_number').value.trim();
    var ifscCode = document.getElementById('ifsc_code').value.trim();
    var branchName = document.getElementById('branch_name').value.trim();

    if (!accountNumber) {
        Swal.fire({
            icon: 'error',
            title: 'Validation Error',
            text: 'Account Number is required.',
        });
        return;
    }
    if (!ifscCode) {
        Swal.fire({
            icon: 'error',
            title: 'Validation Error',
            text: 'IFSC Code is required.',
        });
        return;
    }
    if (!branchName) {
        Swal.fire({
            icon: 'error',
            title: 'Validation Error',
            text: 'Branch Name is required.',
        });
        return;
    }
    $.ajax({
        url: "<?php echo site_url('staff/Staff_profile_view_controller/savePayrollDetails'); ?>",
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        success: function(response) {
            $('#update_payroll_details').modal('hide');
            Swal.fire({
                title: 'Success!',
                text: 'Payroll details updated successfully.',
                icon: 'success',
                confirmButtonText: 'OK'
            }).then((result) => {
                if (result.isConfirmed) {
                    get_staff_profile_data('payroll_details');
                    form.reset();
                }
            });
        },
        error: function(error) {
            console.error(error);
            Swal.fire({
                title: 'Error!',
                text: 'There was an issue updating the payroll details. Please try again later.',
                icon: 'error',
                confirmButtonText: 'OK'
            });
        }
    });
}

function update_profile_confirmedbyuser(staff_id) {
    bootbox.confirm({
        title: "Confirm",
        message: "Are you sure that the profile information is correct ?",
        className: "medium",
        buttons: {
            confirm: {
                label: 'Yes',
                className: 'btn-success'
            },
            cancel: {
                label: 'No',
                className: 'btn-danger'
            }
        },
        callback: function(result) {
            if (result) {
                $.ajax({
                    url: '<?php echo site_url('staff/Staff_profile_view_controller/update_profile_confirmedbyuser'); ?>',
                    type: 'post',
                    data: {
                        'staff_id': staff_id
                    },
                    success: function(data) {
                        var response = JSON.parse(data);
                        if (response != 'done') {
                            var count_response = response.length;
                            html =
                                '<div style="height: 200px; overflow-y: auto;"><table class="table table-bordered" style="text-align:left"><thead style="position: sticky; top: -2;"><tr><th>Missing fields</th></tr></thead><tbody>';

                            for (let i = 0; i < response.length; i++) {
                                const element = response[i];
                                // html+='<tr><td>' + element + '</td></tr>';
                                if (element.toLowerCase() === "picture url") {
                                    html += '<tr><td>' + "Picture" + '</td></tr>';
                                } else {
                                    // Convert other fields to sentence case
                                    html += '<tr><td>' + element.charAt(0).toUpperCase() +
                                        element.slice(1).toLowerCase() + '</td></tr>';
                                }
                            }
                            html += '</tbody></table></div>';
                            var message_html = count_response == 1 ? '1 field is missing' :
                                count_response + ' fields are missing';
                            Swal.fire({
                                title: 'Missing Profile Data',
                                html: '<strong>' + message_html + '</strong>',
                                icon: 'warning',
                                showCancelButton: true,
                                confirmButtonText: 'View'
                            }).then((result) => {
                                if (result.isConfirmed) {
                                    Swal.fire({
                                        title: 'Missing Profile Data',
                                        html: '<P>Note! Please fill these mandatory fields.</P>' +
                                            html,
                                        heightAuto: true,
                                        showCancelButton: true,
                                        confirmButtonText: 'Ok'
                                    })
                                }
                            });
                        } else {
                            location.reload();
                        }
                    }
                });
            }
        }
    });
}

function transportation() {
    var transport = $('#transport').val();
    if (transport == 'Yes') {
        $('#transportation').show();
        $('#no_transport').hide();
        $('#pickupMode').show();
        $('#dropPoint').show();
        $('#routearea').attr('required', 'required');
        $('#stops').attr('required', 'required');
        $('#transport_btn').show();
        $('#transportation_mode').removeAttr('required', 'required');
    } else if (transport == 'No') {
        $('#transportation').hide();
        $('#no_transport').show();
        $('#pickupMode').hide();
        $('#dropPoint').hide();
        $('#routearea').removeAttr('required');
        $('#stops').removeAttr('required');
        $('#transportation_mode').attr('required', 'required');
        $('#transport_btn').show();
    } else if (transport == '-1') {
        $('#transportation').hide();
        $('#no_transport').hide();
        $('#pickupMode').hide();
        $('#dropPoint').hide();
        $('#routearea').removeAttr('required');
        $('#stops').removeAttr('required');
        $('#transportation_mode').removeAttr('required');
        $('#transport_btn').hide();
    }
}

function get_route_wise_area() {
    var routearea = $('#routearea').val();
    var editStopId = '<?php echo (isset($edit_transport->pickup->id)) ? $edit_transport->pickup->id : 0 ?>';
    $.ajax({
        url: '<?php echo site_url('feesv2/fees_transport/get_route_wise_stop') ?>',
        type: 'post',
        data: {
            'routearea': routearea
        },
        success: function(data) {
            var stops = $.parseJSON(data);
            var output = '<option value="">Select Stop</option>';
            for (var i = 0; i < stops.length; i++) {
                var selected = '';
                if (editStopId == stops[i].id) {
                    selected = 'selected';
                }
                output += '<option ' + selected + ' value="' + stops[i].id + '">' + stops[i].name +
                    ' </option>';
            }
            $("#stops").html(output);

            // stage_viewby_stop();
        }
    });
}

function stage_viewby_stop() {
    var stops = $("#stops").val();
    if (stops == '') {
        return false;
    }

    $.ajax({
        url: '<?php echo site_url('feesv2/fees_transport/get_stop_wise_stage') ?>',
        type: 'post',
        data: {
            'stops': stops
        },
        success: function(data) {
            var stage = $.parseJSON(data);
            var output = '';

            output += '<option value="' + stage.km_name + '">' + stage.km_name +
                ' </option>';
            $("#kilometerId").html(output);
        }
    });
}
</script>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<div class="modal fade" id="update_payroll_details" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel"
    aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="exampleModalLabel">Update Bank Details</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form enctype="multipart/form-data" id="payroll_update_form" class="form-horizontal"
                    data-parsley-validate method="post">
                    <div class="form-group">
                        <label for="account_number">Account Number</label>
                        <input type="text" class="form-control" id="account_number" name="account_number" required
                            placeholder="Enter Account Number" data-parsley-type="number"
                            data-parsley-error-message="This value is required">
                    </div>
                    <div class="form-group">
                        <label for="ifsc_code">IFSC Code</label>
                        <input type="text" class="form-control" id="ifsc_code" name="ifsc_code" required
                            placeholder="Enter IFSC Code" data-parsley-error-message="This value is required">
                    </div>
                    <div class="form-group">
                        <label for="branch_name">Branch Name</label>
                        <input type="text" class="form-control" id="branch_name" name="branch_name" required
                            placeholder="Enter Branch Name" data-parsley-error-message="This value is required">
                    </div>
                    <div class="pull-right">
                        <input type="hidden" id="payroll_id" name="payroll_id">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                        <button type="button" class="btn btn-primary" onclick="savePayrollDetails()">Save
                            changes</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>



<div class="modal fade" id="_show_detailed_initiative_modal" tabindex="-1" role="dialog"
    aria-labelledby="exampleModalLabel1" aria-hidden="true">
    <div class="modal-dialog" role="document" style="margin: auto;border-radius: .75rem">
        <div class="modal-content" style="width: 50%;">
            <div class="modal-header" style="border-top-left-radius: .75rem;border-top-right-radius: .75rem;">
                <h4 class="modal-title">Initiative</h4>
                <button type="button" class="close" data-dismiss="modal"><i class="fa fa-times" aria-hidden="true"
                        style="color: #d80403;font-size: 21px;"></i></button>
            </div>
            <form>
                <div class="modal-body" style="overflow: scroll;">
                    <div class="col-sm-12" style="margin-bottom: 10px; padding-left:0px;
            padding-right:0px;">
                        <div class="form-group col-sm-6">
                            <div class="form-group">
                                <label class="control-label" style="margin-bottom: 0px;"> Initiative</label>
                                <input type="text" readonly class="form-control" id="show_initiative"
                                    name="show_initiative">
                            </div>
                        </div>
                        <div class="form-group col-sm-6">
                            <div class="form-group">
                                <label class="control-label" style="margin-bottom: 0px;"> Attended By</label>
                                <input type="text" readonly class="form-control" id="show_attend" name="show_attend">
                            </div>
                        </div>
                    </div><br>
                    <div class="col-sm-12" style="padding: 0px 15px 10px 15px; ">
                        <div class="form-group col-sm-12" style="border: 1px solid #d3d3d3;padding:7px 0 7px 8px;">
                            <label class="control-label" style="margin-bottom: 0px;"> Supporting Document </label>
                            <div id="download_link_initiative">

                            </div>
                        </div>
                    </div><br>
                    <div class="col-sm-12" style="padding: 0px 15px 10px 5px; ">
                        <div class="form-group col-sm-12" style="padding:7px 0 7px 8px;">
                            <label class="control-label" style="margin-bottom: 0px;"> Initiative Details</label>
                            <textarea readonly rows="4" cols="500" class="form-control" id="show_initiative_details"
                                name="show_initiative_details">
              </textarea>
                        </div>
                    </div>

                    <div class="col-sm-12" style="margin-bottom: 10px; padding-left:0px;
            padding-right:0px;">
                        <div class="form-group col-sm-6">
                            <div class="form-group">
                                <label class="control-label" style="margin-bottom: 0px;"> From Date</label>
                                <input type="text" readonly class="form-control" id="show_from_date"
                                    name="show_from_date">
                            </div>
                        </div>
                        <div class="form-group col-sm-6">
                            <div class="form-group">
                                <label class="control-label" style="margin-bottom: 0px;"> To Date</label>
                                <input type="text" readonly class="form-control" id="show_to_date" name="show_to_date">
                            </div>
                        </div>
                    </div><br>

                    <div class="form-group col-sm-6">
                        <div class="form-group">
                            <label class="control-label" style="margin-bottom: 0px;"> Created On</label>
                            <input type="text" readonly class="form-control" id="show_created_on_initiative"
                                name="show_created_on_initiative">
                        </div>
                        <div class="form-group">
                            <label class="control-label" style="margin-bottom: 0px;"> Created By</label>
                            <input type="text" readonly class="form-control" id="show_created_by_initiative"
                                name="show_created_by_initiative">
                        </div>
                    </div>
                    <div class="form-group col-sm-6">
                        <div class="form-group">
                            <label class="control-label" style="margin-bottom: 0px;"> Status</label>
                            <input type="text" readonly class="form-control" id="show_status_initiative"
                                name="show_status_initiative">
                        </div>
                        <div class="form-group">
                            <label class="control-label" style="margin-bottom: 0px;"> Approved Status</label>
                            <input type="text" readonly class="form-control" id="show_initiative_approved_status"
                                name="show_initiative_approved_status">
                        </div>
                    </div>
                    <div class="col-sm-12" style="padding: 0px 15px 10px 5px; ">
                        <div class="form-group col-sm-12" style="padding:7px 0 7px 8px;">
                            <label class="control-label" id="label_show_approved_remarks_initiative"
                                style="margin-bottom: 0px;">Approved Remarks</label>
                            <textarea readonly rows="4" cols="500" class="form-control"
                                id="show_approved_remarks_initiative" name="show_approved_remarks_initiative">
              </textarea>
                        </div>
                    </div>
                </div><br>
            </form>
        </div>
    </div>
</div>

<div class="modal fade" id="show_exp_modal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel1"
    aria-hidden="true">
    <div class="modal-dialog" role="document" style="margin: auto;border-radius: .75rem">
        <div class="modal-content" style="width: 50%;">
            <div class="modal-header" style="border-top-left-radius: .75rem;border-top-right-radius: .75rem;">
                <h4 class="modal-title">Experience</h4>
                <button type="button" class="close" data-dismiss="modal"><i class="fa fa-times" aria-hidden="true"
                        style="color: #d80403;font-size: 21px;"></i></button>
            </div>
            <form>
                <div class="modal-body" style="overflow: scroll;">
                    <div class="col-sm-12" style="margin-bottom: 10px; padding-left:0px;
            padding-right:0px;">
                        <div class="form-group col-sm-6">
                            <div class="form-group">
                                <label class="control-label" style="margin-bottom: 0px;"> Worked For</label>
                                <input type="text" readonly class="form-control" id="show_worked_for"
                                    name="show_worked_for">
                            </div>
                            <div class="form-group">
                                <label class="control-label" style="margin-bottom: 0px;"> Duration</label>
                                <input type="text" readonly class="form-control" id="show_duration_expe"
                                    name="show_duration_expe">
                            </div>
                            <div class="form-group">
                                <label class="control-label" style="margin-bottom: 0px;"> Grades Handled</label>
                                <input type="text" readonly class="form-control" id="show_grades_handle"
                                    name="show_grades_handle">
                            </div>
                        </div>
                        <div class="form-group col-sm-6">
                            <div class="form-group">
                                <label class="control-label" style="margin-bottom: 0px;"> Experience type</label>
                                <input type="text" readonly class="form-control" id="show_exp_type"
                                    name="show_exp_type">
                            </div>
                            <div class="form-group">
                                <label class="control-label" style="margin-bottom: 0px;"> Subjects Taught</label>
                                <input type="text" readonly class="form-control" id="show_sub_taught"
                                    name="show_sub_taught">
                            </div>
                            <div class="form-group">
                                <label class="control-label" style="margin-bottom: 0px;"> Location</label>
                                <input type="text" readonly class="form-control" id="show_location"
                                    name="show_location">
                            </div>
                        </div>
                    </div><br>
                    <div class="col-sm-12" style="padding: 0px 15px 10px 15px; ">
                        <div class="form-group col-sm-12" style="border: 1px solid #d3d3d3;padding:7px 0 7px 8px;">
                            <label class="control-label" style="margin-bottom: 0px;"> Supporting Document </label>
                            <div id="download_link_expe">

                            </div>
                        </div>
                    </div><br>

                    <div class="form-group col-sm-6">
                        <div class="form-group">
                            <label class="control-label" style="margin-bottom: 0px;"> Created On</label>
                            <input type="text" readonly class="form-control" id="show_created_on_expe"
                                name="show_created_on_expe">
                        </div>
                        <div class="form-group">
                            <label class="control-label" style="margin-bottom: 0px;"> Created By</label>
                            <input type="text" readonly class="form-control" id="show_created_by_expe"
                                name="show_created_by_expe">
                        </div>
                    </div>
                    <div class="form-group col-sm-6">
                        <div class="form-group">
                            <label class="control-label" style="margin-bottom: 0px;"> Disabled By</label>
                            <input type="text" readonly class="form-control" id="show_dis_by" name="show_dis_by">
                        </div>
                        <div class="form-group">
                            <label class="control-label" style="margin-bottom: 0px;"> Status</label>
                            <input type="text" readonly class="form-control" id="show_status_expe"
                                name="show_status_expe">
                        </div>
                    </div>
                    <div class="form-group col-sm-6">
                        <div class="form-group">
                            <label class="control-label" style="margin-bottom: 0px;">Approved Status</label>
                            <input type="text" readonly class="form-control" id="approved_status_expe"
                                name="approved_status_expe">
                        </div>
                    </div>
                    <div class="col-sm-12" style="padding: 0px 15px 10px 5px; ">
                        <div class="form-group col-sm-12" style="padding:7px 0 7px 8px;">
                            <label class="control-label" id="label_show_approved_remarks_expe"
                                style="margin-bottom: 0px;">Approved Remarks</label>
                            <textarea readonly rows="4" cols="500" class="form-control" id="show_approved_remarks_expe"
                                name="show_approved_remarks_expe">
              </textarea>
                        </div>
                    </div>
                </div><br>
            </form>
        </div>
    </div>
</div>



<style type="text/css">
.photo_btn {
    background: #6893ca !important;
    border-color: #6893ca !important;
    color: #000 !important;
}

.new_circleShape {
    /* padding: 8%; */
    border-radius: 50%;
    color: white;
    font-size: 18px;
    height: 3rem;
    width: 3rem;
    text-align: center;
    vertical-align: middle;
}

.table-properties th {
    font-weight: normal;
    width: 20rem;
}

.table-properties td {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.table-properties tr:first-child td,
.table-properties tr:first-child th {
    border-top: none !important;
}

.profile .img-lg {
    width: 9rem;
    height: 9rem;
}

.loaderclass {
    border: 8px solid #eee;
    border-top: 8px solid #7193be;
    border-radius: 50%;
    width: 48px;
    height: 48px;
    position: fixed;
    z-index: 1;
    animation: spin 2s linear infinite;
    margin-top: 30%;
    margin-left: 40%;
    position: absolute;
    z-index: 99999;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

.list-group-item.active,
.list-group-item.active:hover,
.list-group-item.active:focus {
    color: #fff;
    background-color: #007bff;
    border-color: #007bff;
}

.modal-content {
    /* background-color: #fefefe; */
    margin: 5% auto;
    /* padding: 20px; */
    border: 1px solid #888;
    width: 30%;
}

.modal-footer>button {
    margin: 0px .25rem !important;
}

#staff_profile_img {
    border-radius: 20%;
    width: 120px;
    height: 120px;
}
.jContainer {
    padding: 0px;
    border: solid 1px #ccc;
    margin-bottom: 10px;
    border-radius: 6px;
}

.jHead {
    padding: 1%;
    background: #DAE6FA;
    border-top-left-radius: 6px;
    border-top-right-radius: 6px;
}

.jHead>h5 {
    color: #000 !important;
}

.col-md-6:has(select) {
    margin-top: 9px;
}
</style>