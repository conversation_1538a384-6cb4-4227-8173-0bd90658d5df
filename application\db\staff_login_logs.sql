-- Staff Login Logging System
-- This script creates tables and procedures for comprehensive staff login tracking
-- without affecting existing CodeIgniter functionality

-- Drop table if exists (for development/testing)
DROP TABLE IF EXISTS `staff_login_logs`;

-- Create enhanced staff login logs table
CREATE TABLE `staff_login_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT 'User ID from users table',
  `username` varchar(100) NOT NULL COMMENT 'Username used for login',
  `session_id` varchar(128) DEFAULT NULL COMMENT 'CodeIgniter session ID',
  `ip_address` varchar(45) NOT NULL COMMENT 'IP address of the user',
  `user_agent` text COMMENT 'Browser user agent string',
  `login_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Login timestamp',
  `logout_time` timestamp NULL DEFAULT NULL COMMENT 'Logout timestamp',
  `session_duration` int(11) DEFAULT NULL COMMENT 'Session duration in seconds',
  `login_method` enum('web','mobile','api') NOT NULL DEFAULT 'web' COMMENT 'Login method used',
  `device_type` varchar(50) DEFAULT NULL COMMENT 'Device type (desktop, mobile, tablet)',
  `browser_name` varchar(100) DEFAULT NULL COMMENT 'Browser name',
  `browser_version` varchar(50) DEFAULT NULL COMMENT 'Browser version',
  `operating_system` varchar(100) DEFAULT NULL COMMENT 'Operating system',
  `country` varchar(100) DEFAULT NULL COMMENT 'Country based on IP',
  `city` varchar(100) DEFAULT NULL COMMENT 'City based on IP',
  `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT 'Is session currently active',
  `logout_reason` enum('manual','timeout','forced','system') DEFAULT NULL COMMENT 'Reason for logout',
  `additional_data` json DEFAULT NULL COMMENT 'Additional metadata in JSON format',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_session_id` (`session_id`),
  KEY `idx_ip_address` (`ip_address`),
  KEY `idx_login_time` (`login_time`),
  KEY `idx_is_active` (`is_active`),
  KEY `idx_username` (`username`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='Comprehensive staff login activity logs';

-- Create index for performance on common queries
CREATE INDEX `idx_user_session_active` ON `staff_login_logs` (`user_id`, `session_id`, `is_active`);
CREATE INDEX `idx_login_date_range` ON `staff_login_logs` (`login_time`, `logout_time`);

-- Create a view for active sessions (only staff members)
CREATE OR REPLACE VIEW `active_staff_sessions` AS
SELECT
    sll.id,
    sll.user_id,
    sll.username,
    sll.session_id,
    sll.ip_address,
    sll.login_time,
    sll.device_type,
    sll.browser_name,
    TIMESTAMPDIFF(MINUTE, sll.login_time, NOW()) as session_minutes,
    u.email,
    u.username as full_name,
    a.stakeholder_id as staff_id
FROM staff_login_logs sll
LEFT JOIN users u ON sll.user_id = u.id
LEFT JOIN avatar a ON u.id = a.user_id AND a.avatar_type = 4
WHERE sll.is_active = 1 AND a.id IS NOT NULL
ORDER BY sll.login_time DESC;

-- Create a view for login statistics
CREATE OR REPLACE VIEW `staff_login_stats` AS
SELECT 
    DATE(login_time) as login_date,
    COUNT(*) as total_logins,
    COUNT(DISTINCT user_id) as unique_users,
    COUNT(DISTINCT ip_address) as unique_ips,
    AVG(session_duration) as avg_session_duration,
    MAX(session_duration) as max_session_duration,
    MIN(session_duration) as min_session_duration
FROM staff_login_logs
WHERE login_time >= DATE_SUB(NOW(), INTERVAL 30 DAY)
GROUP BY DATE(login_time)
ORDER BY login_date DESC;

-- Stored procedure to close active sessions for a user
DELIMITER $$
CREATE PROCEDURE `CloseUserActiveSessions`(
    IN p_user_id INT,
    IN p_logout_reason ENUM('manual','timeout','forced','system')
)
BEGIN
    UPDATE staff_login_logs 
    SET 
        logout_time = NOW(),
        session_duration = TIMESTAMPDIFF(SECOND, login_time, NOW()),
        is_active = 0,
        logout_reason = p_logout_reason,
        updated_at = NOW()
    WHERE user_id = p_user_id 
    AND is_active = 1;
END$$
DELIMITER ;

-- Stored procedure to close session by session_id
DELIMITER $$
CREATE PROCEDURE `CloseSessionById`(
    IN p_session_id VARCHAR(128),
    IN p_logout_reason ENUM('manual','timeout','forced','system')
)
BEGIN
    UPDATE staff_login_logs 
    SET 
        logout_time = NOW(),
        session_duration = TIMESTAMPDIFF(SECOND, login_time, NOW()),
        is_active = 0,
        logout_reason = p_logout_reason,
        updated_at = NOW()
    WHERE session_id = p_session_id 
    AND is_active = 1;
END$$
DELIMITER ;

-- Stored procedure to get user login history
DELIMITER $$
CREATE PROCEDURE `GetUserLoginHistory`(
    IN p_user_id INT,
    IN p_limit INT DEFAULT 50
)
BEGIN
    SELECT 
        id,
        username,
        ip_address,
        login_time,
        logout_time,
        session_duration,
        device_type,
        browser_name,
        operating_system,
        logout_reason,
        is_active
    FROM staff_login_logs
    WHERE user_id = p_user_id
    ORDER BY login_time DESC
    LIMIT p_limit;
END$$
DELIMITER ;

-- Insert sample configuration data (optional)
-- This can be used to store configuration for the logging system
CREATE TABLE IF NOT EXISTS `staff_login_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `config_key` varchar(100) NOT NULL,
  `config_value` text NOT NULL,
  `description` text,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `config_key` (`config_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- Insert default configuration
INSERT INTO `staff_login_config` (`config_key`, `config_value`, `description`) VALUES
('log_retention_days', '90', 'Number of days to retain login logs'),
('track_ip_location', '1', 'Whether to track IP location (1=yes, 0=no)'),
('log_user_agent_details', '1', 'Whether to parse and log user agent details'),
('auto_close_inactive_sessions', '1', 'Auto close sessions after timeout'),
('session_timeout_minutes', '1440', 'Session timeout in minutes (24 hours)'),
('enable_concurrent_session_limit', '0', 'Limit concurrent sessions per user'),
('max_concurrent_sessions', '3', 'Maximum concurrent sessions per user')
ON DUPLICATE KEY UPDATE 
config_value = VALUES(config_value),
updated_at = CURRENT_TIMESTAMP;
