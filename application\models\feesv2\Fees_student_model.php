<?php
defined('BASEPATH') OR exit('No direct script access allowed');
            
	class Fees_student_model extends CI_Model {
  private $yearId;
  private $current_branch;
  public function __construct() {
    parent::__construct();
    $this->yearId =  $this->acad_year->getAcadYearId();
    $this->current_branch = $this->authorization->getCurrentBranch();
  }

  public function Kolkata_datetime(){
    $timezone = new DateTimeZone("Asia/Kolkata" );
    $date = new DateTime();
    $date->setTimezone($timezone );
    $dtobj = $date->format('Y-m-d H:i:s');
    return $dtobj;
  }

  public function get_blueprints($stdId=''){
    // if (!empty($stdId)) {
    //   $studentCheck_lifeTimeFee = $this->db->select("sd.life_time_fee_mode")
    //   ->from('student_admission sd')
    //   ->where('sd.id',$stdId)
    //   ->where('life_time_fee_mode',1)
    //   ->get()->row();
    //   if (!empty($studentCheck_lifeTimeFee)) {
    //     $this->db->select('fb.*');
    //     $this->db->from('student_admission sd');
    //     $this->db->where('sd.id',$stdId);
    //     $this->db->join('feev2_blueprint fb','sd.admission_acad_year_id=fb.acad_year_id');
    //     if($this->current_branch) {
    //       $this->db->where('branches',$this->current_branch);
    //     }
    //    return $this->db->get()->result();
    //   }
    // }
   
    $this->db->select('*');
    $this->db->from('feev2_blueprint');
      $this->db->where('acad_year_id',$this->yearId);
    if($this->current_branch) {
      $this->db->where('branches',$this->current_branch);
    }
    return $this->db->get()->result();
  }

  public function get_all_blueprints_collection_summary(){
    $this->db->select('id, name');
    $this->db->from('feev2_blueprint');
    if($this->current_branch) {
      $this->db->where('branches',$this->current_branch);
    }
    $this->db->order_by('acad_year_id','desc');
    return $this->db->get()->result();
  }

  public function get_blueprintsv1(){
    $years =array($this->yearId, ($this->yearId-1));
    $this->db->select('*');
    $this->db->from('feev2_blueprint');
    if($this->current_branch) {
      $this->db->where('branches',$this->current_branch);
    }
    $this->db->order_by('acad_year_id');
    $this->db->where_in('acad_year_id',$years);
    return $this->db->get()->result();
  }


  public function get_blueprint_components($fb_id){
    return $this->db_readonly->query("select fbc.id,fbc.name from feev2_blueprint_components fbc
    join feev2_blueprint fb on fb.id=fbc.feev2_blueprint_id
    where fb.id=$fb_id")->result();
  }

  public function get_blueprints_daily_transactions(){
    $this->db->select('*');
    $this->db->from('feev2_blueprint');
    // $this->db->where('acad_year_id',$this->yearId);
    if($this->current_branch) {
      $this->db->where('branches',$this->current_branch);
    }
    return $this->db->get()->result();
  }
  
  public function get_blueprints_all_branches(){
    $this->db->select('*');
    $this->db->from('feev2_blueprint');
    $this->db->where('acad_year_id',$this->yearId);
    return $this->db->get()->result();
  }
  public function get_std_detailsbyId($stdId, $fee_acad_year_id){
    $school_name = $this->settings->getSetting('school_short_name');
    $collectFeeIfAdmissionStatusOther = $this->settings->getSetting('enable_collect_fee_if_student_all_the_status');
     $this->db->select("sd.id as std_admission_id, sy.id as std_year_id, concat(ifnull(sd.first_name,''),' ',ifnull(sd.last_name,'')) as stdName, sd.admission_no, c.class_name as className, sy.board, sy.boarding, sy.medium, sd.category, sy.is_rte, sd.admission_acad_year_id as academic_year_of_joining, sy.admission_type, sy.class_id as class, sd.has_staff as has_staff, sd.sibling_type as has_sibling, c.type as class_type, sy.acad_year_id, donor, has_transport, sd.staff_id, has_transport_km, stop, pickup_mode, sd.gender, if(sh.physical_disability is null or sh.physical_disability='','0',sh.physical_disability) as physical_disability,sd.life_time_fee_mode as is_lifetime_student, sy.combination, sd.attempt, sd.quota, concat(ifnull(p1.first_name,''),' ',ifnull(p1.last_name,'')) as father_name, concat(ifnull(p2.first_name,''),' ',ifnull(p2.last_name,'')) as mother_name, ifnull(p1.mobile_no,'')  as father_phone, ifnull(p2.mobile_no,'') as mother_phone, p1.email as father_email, p2.email as mother_email,admission_status,sd.transfer,af.application_no, concat(ifnull(c.class_name,''),' ',ifnull(cs.section_name,'')) as class_section_name")
    ->from('student_year sy')
    ->where('sd.id',$stdId)
    ->where('sy.acad_year_id',$fee_acad_year_id)
    ->join('student_admission sd','sy.student_admission_id=sd.id')
    ->join('admission_forms af','sd.admission_form_id=af.id','left')
    // ->where('sd.admission_status','2')
    ->join('student_health sh','sh.student_id=sd.id','left')
    ->join('class c','sy.class_id=c.id')
    ->join('class_section cs','c.id=cs.class_id')
    ->join('student_relation sr1', "sr1.std_id=sd.id and sr1.relation_type='Father'")
    ->join('parent p1', 'p1.id=sr1.relation_id')
    ->join('student_relation sr2', "sr2.std_id=sd.id and sr2.relation_type='mother'")
    ->join('parent p2', 'p2.id=sr2.relation_id');
      if($school_name != 'iisb' && $collectFeeIfAdmissionStatusOther != 1){
        $this->db->where('sy.promotion_status!=', '4');
        $this->db->where('sy.promotion_status!=', '5');
      }
    return $this->db->get()->row();
  }

  public function determine_cohort($blueprint_id, $std_data){    
    $std_arr = (array)$std_data;
    $filters = $this->fee_library->construct_filter($blueprint_id, $std_arr);
    $result = $this->db->select('fc.id as cohort_id')
      ->from('feev2_cohorts fc')
      ->where('fc.filter',$filters)
      ->where('blueprint_id',$blueprint_id)
      ->get()->row();
    if (!empty($result)) {
      return $result->cohort_id;
    }else{
      return 0;
    }
  }

  public function is_fee_cohort_assigned($blueprint_id, $stdId) {

    $result = $this->db->select('fcs.fee_cohort_status')
      ->from('feev2_cohort_student fcs')
      ->where('student_id',$stdId)
      ->where('blueprint_id',$blueprint_id)
      ->get()->row();
    if (empty($result))
      return 0;
    else
      return 1;
    // return ($result->fee_cohort_status === 'STANDARD');
  }

  private function __determine_cohort($blueprint_id, $std_data) {
    $std_arr = (array)$std_data;
    $filters = $this->fee_library->construct_filter($blueprint_id, $std_arr);
    $result = $this->db->select('fc.id')
      ->from('feev2_cohorts fc')
      ->where('fc.filter',$filters)
      ->where('blueprint_id',$blueprint_id)
      ->get()->row();
    return $result;
  }

  private function __insert_std_fee_cohort($std_id, $blueprint_id, $cohort_id, $cohort_status) {
    $cohort_data = array(
      'student_id' => $std_id,
      'blueprint_id' => $blueprint_id,
      'feev2_cohort_id' => $cohort_id,
      'fee_cohort_status' => $cohort_status
    );
    return $this->db->insert('feev2_cohort_student', $cohort_data);
  }

  public function assign_std_fee_cohort_publish($blueprint_id, $std_id, $std_data){
    $fee_cohort = $this->__determine_cohort($blueprint_id, $std_data);
    if (!empty($fee_cohort)) {
        if (!$this->is_fee_cohort_assigned($blueprint_id, $std_id)) {
          return $this->__insert_std_fee_cohort($std_id, $blueprint_id, $fee_cohort->id, 'STANDARD');
        }else{          
          return 2;  // already assigned;
        }
    }else{
      return 0; // No Cohort Defined
    }
  }

  public function assign_std_fee_cohort($blueprint_id, $std_id, $std_data){
    //Assign Fee Cohort if not already assigned
    if (!$this->is_fee_cohort_assigned($blueprint_id, $std_id)) {
      $fee_cohort = $this->__determine_cohort($blueprint_id, $std_data);
      if (!empty($fee_cohort)) {
        return $this->__insert_std_fee_cohort($std_id, $blueprint_id, $fee_cohort->id, 'STANDARD');
      }
      else 
        return 0; //No Cohort Defined
    }
    return 1;
  }


  public function get_std_fee_cohort($std_id, $blueprint_id) {
    $result =  $this->db->select("fcs.id as cohort_student_id, ifnull(fss.total_fee,0) as total_fee, fcs.id as feev2_cohort_student_id, fcs.fee_collect_status, fcs.fee_cohort_status, fss.id as std_sch_id, ifnull(fss.total_fee_paid,0) as total_fee_paid, fss.total_concession_amount_paid, fss.total_adjustment_amount_paid, fss.total_concession_amount, fss.total_adjustment_amount, fss.payment_status, case fss.payment_status when 'FULL' then 1 else 0 end as fee_status, fss.discount, fss.total_fine_amount_paid as total_fine_amount, fss.total_card_charge_amount, (ifnull(fss.total_fee,0) - ifnull(fss.total_fee_paid,0) - ifnull(fss.total_concession_amount,0)  - ifnull(fss.total_concession_amount_paid,0)) as balance, fb.name as blueprint_name, date_format(fcs.pay_date,'%d-%m-%Y') as pay_date_format, fcs.pay_date, sum(ifnull(fsi.installment_amount,0) - ifnull(fsi.installment_amount_paid,0) - ifnull(fsi.total_concession_amount,0)  - ifnull(fsi.total_concession_amount_paid,0)) as balance_new,fb.id as blueprint_id,ifnull(fcs.payment_link,'') as payment_link")
      ->from('feev2_cohort_student fcs')
      ->where('fcs.student_id',$std_id)
      ->where('fcs.blueprint_id',$blueprint_id)
      ->join('feev2_student_schedule fss','fcs.id=fss.feev2_cohort_student_id')
      ->join('feev2_student_installments fsi','fss.id=fsi.fee_student_schedule_id')
      ->join('feev2_blueprint fb','fcs.blueprint_id=fb.id')
      ->group_by('fss.id')
      ->get()->row();
      return $result;
  }

  public function get_std_fee_for_invoice($std_id) {
    $result =  $this->db->select("fcs.id as cohort_student_id, fcs.id as feev2_cohort_student_id, fcs.fee_collect_status, fcs.fee_cohort_status, fss.id as std_sch_id,  fbc.name as blueprint_name, sum(fsic.component_amount) as component_amount, sum(fsic.concession_amount) as concession_amount, sum(component_amount_paid) as component_paid")
      ->from('feev2_blueprint fb')
      ->join('feev2_cohort_student fcs','fcs.blueprint_id=fb.id')
      ->where('fcs.student_id',$std_id)
      ->where('fb.acad_year_id',$this->yearId)
      ->join('feev2_student_schedule fss','fcs.id=fss.feev2_cohort_student_id')
      ->join('feev2_student_installments fsi','fss.id=fsi.fee_student_schedule_id')
      ->join('feev2_student_installments_components fsic','fsic.fee_student_installment_id=fsi.id')
      ->join('feev2_blueprint_components fbc','fbc.id=fsic.blueprint_component_id')
      ->group_by('fbc.id')
      ->get()->result();
      return $result;
  }
  function get_std_detailsNextyearbyId($stdId, $fee_acad_year_id){
    return $this->db->select("sd.id as std_admission_id, sy.id as std_year_id, concat(ifnull(sd.first_name,''),' ',ifnull(sd.last_name,'')) as stdName, sd.admission_no, c.class_name as className, sy.board, sy.boarding, sy.medium, sd.category, sy.is_rte, sd.admission_acad_year_id as academic_year_of_joining, sy.admission_type, sy.class_id as class, sd.has_staff as has_staff, sd.sibling_type as has_sibling, c.type as class_type, sy.acad_year_id, donor, has_transport, sd.staff_id, has_transport_km, stop, pickup_mode, sd.gender, if(sh.physical_disability is null or sh.physical_disability='','0',sh.physical_disability) as physical_disability,sd.life_time_fee_mode as is_lifetime_student, sy.combination, sd.attempt, sd.quota, p1.first_name as father_name, p2.first_name as mother_name, ifnull(p1.mobile_no,'')  as father_phone, ifnull(p2.mobile_no,'') as mother_phone, p1.email as father_email, p2.email as mother_email")
    ->from('student_year sy')
    ->where('sy.promotion_status!=', '4')
    ->where('sy.promotion_status!=', '5')
    ->where('sd.id',$stdId)
    ->where('sy.acad_year_id >',$fee_acad_year_id)
    ->join('student_admission sd','sy.student_admission_id=sd.id')
    // ->where('sd.admission_status','2')
    ->join('student_health sh','sh.student_id=sd.id','left')
    ->join('class c','sy.class_id=c.id')
    ->join('student_relation sr1', "sr1.std_id=sd.id and sr1.relation_type='Father'")
    ->join('parent p1', 'p1.id=sr1.relation_id')
    ->join('student_relation sr2', "sr2.std_id=sd.id and sr2.relation_type='mother'")
    ->join('parent p2', 'p2.id=sr2.relation_id')
    ->get()->row();
  }

  public function get_std_fee_pre_defined_conessionfor_invoice($std_id){
    $result =  $this->db->select("fcs.id as cohort_student_id, fcpdc.feev2_predefined_name, fcpdc.concession_amount, fb.acad_year_id")
      ->from('feev2_blueprint fb')
      ->where('fb.acad_year_id',$this->yearId)
      ->join('feev2_cohort_student fcs','fcs.blueprint_id=fb.id')
      ->join('feev2_concessiontype2_pre_defined_concession fcpdc','fcs.id=fcpdc.cohort_student_id')
      ->where('fcs.student_id',$std_id)
      ->get()->result();
    return $result;
    // $concessionArry = [];
    // foreach ($result as $key => $val) {
    //   $concessionArry[$val->acad_year_id][] = $val;
    // }
    // return $concessionArry;
  }

  public function get_assigned_student_fee_details($std_id, $blueprint_id) {
    $result =  $this->db->select("fcs.id as cohort_student_id, fcs.fee_collect_status, fcs.publish_status, fcs.online_payment, ifnull(fss.total_fee,0) as total_fee,  fss.id as std_sch_id, ifnull(fss.total_fee_paid,0) as total_fee_paid, fss.payment_status, fss.total_card_charge_amount, sum(ifnull(fsi.total_concession_amount,0)) as total_concession_amount, sum(ifnull(fsi.total_concession_amount_paid,0)) as total_concession_amount_paid, sum(ifnull(fsi.total_adjustment_amount,0)) as total_adjustment_amount, sum(ifnull(fsi.total_adjustment_amount_paid,0)) as total_adjustment_amount_paid, sum(ifnull(fsi.total_fine_amount,0)) as total_fine_amount, sum(ifnull(fsi.total_fine_amount_paid,0)) as total_fine_amount_paid, sum(ifnull(fsi.total_fine_waived,0)) as total_fine_waived, sum(ifnull(fsi.refund_amount,0)) as refund_amount,  (ifnull(fss.total_fee,0) - ifnull(fss.total_fee_paid,0) - ifnull(fss.total_concession_amount,0)  - ifnull(fss.total_concession_amount_paid,0) - ifnull(fss.total_adjustment_amount,0) - ifnull(fss.total_adjustment_amount_paid,0)  ) as balance, ifnull(fss.discount,0) as discount")
      ->from('feev2_cohort_student fcs')
      ->where('fcs.student_id',$std_id)
      ->where('fcs.blueprint_id',$blueprint_id)
      ->join('feev2_student_schedule fss','fcs.id=fss.feev2_cohort_student_id')
      ->join('feev2_student_installments fsi','fss.id=fsi.fee_student_schedule_id')
      ->get()->row();
      return $result;
  }


  public function get_concession_adjustment_amount($std_id, $blueprint_id){
    $result =  $this->db->select("sum(ifnull(fsi.total_concession_amount,0)) as total_concession_amount, sum(ifnull(fsi.total_concession_amount_paid,0)) as total_concession_amount_paid, sum(ifnull(fsi.total_adjustment_amount,0)) as total_adjustment_amount, sum(ifnull(fsi.total_adjustment_amount_paid,0)) as total_adjustment_amount_paid")
      ->from('feev2_cohort_student fcs')
      ->where('fcs.student_id',$std_id)
      ->where('fcs.blueprint_id',$blueprint_id)
      ->join('feev2_student_schedule fss','fcs.id=fss.feev2_cohort_student_id')
      ->join('feev2_student_installments fsi','fss.id=fsi.fee_student_schedule_id')
      ->get()->row();
      return $result;
  }

  public function get_fee_types_all(){

    $this->db->select('*');
    $this->db->from('feev2_blueprint');
    $this->db->where('enable_fee_cohort_check',1);
    $this->db->where('acad_year_id',$this->yearId);
     if($this->current_branch) {
      $this->db->where('branches',$this->current_branch);
    }
    return $this->db->get()->result();
  }

  public function get_fee_types_all_fine(){

    $this->db_readonly->select('fb.*');
    $this->db_readonly->from('feev2_blueprint fb');
    $this->db_readonly->join('feev2_blueprint_installment_types fbit','fb.id=fbit.feev2_blueprint_id');
    $this->db_readonly->where('fbit.fine_amount_algo!=','none');
    $this->db_readonly->where('fbit.fine_amount_algo','manually_enter');
    $this->db_readonly->group_by('fbit.feev2_blueprint_id');
    $this->db_readonly->where('acad_year_id',$this->yearId);
     if($this->current_branch) {
      $this->db_readonly->where('branches',$this->current_branch);
    }
    return $this->db_readonly->get()->result();

  }

  public function get_fee_types_all_waiver(){

    $this->db_readonly->select('fb.*');
    $this->db_readonly->from('feev2_blueprint fb');
    $this->db_readonly->join('feev2_blueprint_installment_types fbit','fb.id=fbit.feev2_blueprint_id');
    $this->db_readonly->where('fbit.fine_amount_algo!=','none');
    // $this->db_readonly->where('fbit.fine_amount_algo!=','manually_enter');
    $this->db_readonly->group_by('fbit.feev2_blueprint_id');
    $this->db_readonly->where('acad_year_id',$this->yearId);
     if($this->current_branch) {
      $this->db_readonly->where('branches',$this->current_branch);
    }
    return $this->db_readonly->get()->result();

  }
  
  public function get_all_class_section(){
    return $this->db->select("c.id as cId, c.class_name as cName")
    ->from('class c')
    ->where('c.acad_year_id',$this->yearId)
    ->get()->result();
  }


  public function search_std_class_wise_fee($clsId, $blueprint_id){
    return  $this->db->select("sd.id, sy.id as stdId, concat(ifnull(sd.first_name,''), ' ' ,ifnull(sd.last_name,'')) as stdName, c.class_name as clsName, publish_status, sd.admission_no, fcs.fee_cohort_status, fcs.id as cohort_student_id, fcs.fee_collect_status, c.id as class_id, fcs.online_payment")
    ->from('student_year sy')
    ->join('student_admission sd','sy.student_admission_id=sd.id')
    ->join('class c','sy.class_id=c.id')
    ->join('feev2_cohort_student fcs',"sd.id=fcs.student_id and fcs.blueprint_id = $blueprint_id",'left')
    ->where('sy.class_id',$clsId)
    ->where('sy.acad_year_id',$this->yearId)
    // ->where('sd.admission_status',2)
    // ->where('sy.promotion_status!=', '4')
    // ->where('sy.promotion_status!=', '5')
    ->order_by('sd.first_name')
    ->get()->result();
  }


  public function search_std_name_wise_fee($name, $blueprint_id){
    return $this->db->select("sd.id, sy.id as stdId, concat(ifnull(sd.first_name,''), ' ' ,ifnull(sd.last_name,'')) as stdName, c.class_name as clsName, publish_status, sd.admission_no, fcs.fee_cohort_status, fcs.id as cohort_student_id, fcs.fee_collect_status, c.id as class_id, fcs.online_payment")
    ->from('student_year sy')
    ->join('student_admission sd','sy.student_admission_id=sd.id')
    ->join('class c','sy.class_id=c.id')
    ->join('feev2_cohort_student fcs',"sd.id=fcs.student_id and fcs.blueprint_id = $blueprint_id",'left')
    ->where("(LOWER(sd.first_name) like '%$name%' OR (LOWER(sd.last_name) like '%$name%'))")
    // ->like('sd.first_name', $name,'both')
    ->where('sy.acad_year_id',$this->yearId)
    // ->where('sd.admission_status',2)
    // ->where('sy.promotion_status!=', '4')
    // ->where('sy.promotion_status!=', '5')
    ->order_by('sd.first_name') 
    ->get()->result();
  }

  public function search_std_adm_no_wise_fee($admission_no, $blueprint_id){
    return $this->db->select("sd.id, sy.id as stdId, concat(ifnull(sd.first_name,''), ' ' ,ifnull(sd.last_name,'')) as stdName, c.class_name as clsName, publish_status, sd.admission_no, fcs.fee_cohort_status, fcs.id as cohort_student_id, fcs.fee_collect_status,c.id as class_id, fcs.online_payment")
    ->from('student_year sy')
    ->join('student_admission sd','sy.student_admission_id=sd.id')
    ->join('class c','sy.class_id=c.id')
    ->join('feev2_cohort_student fcs',"sd.id=fcs.student_id and fcs.blueprint_id = $blueprint_id",'left')
    ->where("sd.admission_no", $admission_no)
    ->where('sy.acad_year_id',$this->yearId)
    // ->where('sd.admission_status',2)
    // ->where('sy.promotion_status!=', '4')
    // ->where('sy.promotion_status!=', '5')
    ->order_by('sd.first_name')
    ->get()->result();
  }

  public function publshed_student_fee_structure($input){
    $this->db->where_in('id',$input['cohort_student_ids']);
    return $this->db->update('feev2_cohort_student', array('publish_status'=>'PUBLISHED'));
  }

  public function un_publshed_student_fee_structure($cohort_student_id)
  {
    $this->db->where('id',$cohort_student_id);
    return $this->db->update('feev2_cohort_student', array('publish_status'=>'NOT_PUBLISHED'));
  }

  public function publshed_student_fee_structure_student_details($cohort_student_id){
    $this->db->where('id',$cohort_student_id);
    return $this->db->update('feev2_cohort_student', array('publish_status'=>'PUBLISHED'));
  }
  
  public function online_payment_enabled_parent_cohorts($cohort_student_id){
    $this->db->where('id',$cohort_student_id);
    return $this->db->update('feev2_cohort_student', array('online_payment'=>'PUBLISHED'));
  }

  public function online_payment_disabled_parent_cohorts($cohort_student_id){
    $this->db->where('id',$cohort_student_id);
    return $this->db->update('feev2_cohort_student', array('online_payment'=>'NOT_PUBLISHED'));
  }

  public function get_insTypesALl(){
    return $this->db->get('feev2_installment_types')->result();
  }

  public function get_filter_blueprint($filter_typesId){
    $this->db->select('*');
    $this->db->from('feev2_blueprint');
    if ($filter_typesId) {
    $this->db->where('id', $filter_typesId);
    }
    return $this->db->get()->row();
  }

  public function get_installmenttypebyblueprintId($blueprintId){
    return $this->db->select('fbit.id as instypeId, fit.name as type_name')
    ->from('feev2_blueprint_installment_types fbit')
    ->where('fbit.feev2_blueprint_id',$blueprintId)
    ->join('feev2_installment_types fit','fbit.feev2_installment_type_id=fit.id')
    ->get()->result();
  }

  //  public function get_installments_types($blueprintId)
  // {
  //   return $this->db->select('fbit.feev2_installment_type_id, fit.name as type_name')
  //   ->from('feev2_blueprint_installment_types fbit')
  //   ->where('feev2_blueprint_id', $blueprintId)
  //   ->join('feev2_installment_types fit','fbit.feev2_installment_type_id=fit.id')
  //   ->get()->result();
  // }

  
  // private function _student_fee_cohorts_details($blueprintId,$cohort_id){
  //   return $this->db->select('fc.id as cohortId, fcic.feev2_blueprint_component_id, fcic.amount as compAmount, fcic.feev2_installment_id, fbc.name as compName, fi.name as insName, fbit.feev2_installment_type_id,fbit.id as feev2_blueprint_installment_types_id')
  //   ->from('feev2_cohorts fc')
  //   ->where('fc.id',$cohort_id)
  //   ->join('feev2_cohort_installment_components fcic','fc.id=fcic.feev2_cohort_id')
  //   ->join('feev2_blueprint_installment_types fbit','fcic.feev2_blueprint_installment_types_id=fbit.id')
  //   ->where('fbit.feev2_blueprint_id',$blueprintId)
  //   ->join('feev2_installments fi','fcic.feev2_installment_id=fi.id')
  //   ->join('feev2_blueprint_components fbc','fcic.feev2_blueprint_component_id=fbc.id')
  //   ->get()->result();
  // }
 
 
  // public function _get_fee_installments_byInstypeId($blueprintId){
  //   return $this->db->select('fi.*,fbit.feev2_installment_type_id')
  //   ->from('feev2_blueprint_installment_types fbit')
  //   ->where('feev2_blueprint_id', $blueprintId)
  //   ->join('feev2_installments fi','fbit.feev2_installment_type_id=fi.feev2_installment_type_id')
  //   ->get()->result();
  // }

  // public function get_fee_structurebyStdId($blueprintId, $cohort_id){

  //   $cohortData = $this->_student_fee_cohorts_details($blueprintId, $cohort_id);

  //   $insData = $this->_get_fee_installments_byInstypeId($blueprintId);

  //   // echo "<pre>"; print_r($cohortData); 
  //   // echo "<pre>"; print_r($insData); 
  //   // die();
  //   $compData = [];
  //   foreach ($cohortData as $key => $val) {
  //     $compData[$val->compName][$val->feev2_installment_id] = $val;
  //   }
  //   return array('ins'=>$insData, 'comp'=>$compData);
  // }

  public function custom_std_cohorts_assingedcheck($blueprintId, $stdId, $insTypeId){
     $result = $this->db->select('fcs.id as cohortstdId')
    ->from('feev2_cohort_student fcs')
    ->where('fcs.student_id',$stdId)
    ->where('fcs.blueprint_id',$blueprintId)
    ->where('fcs.fee_structure_status','Custom')
    ->where('fss.feev2_blueprint_installment_types_id',$insTypeId)
    ->join('feev2_student_schedule fss','fcs.id=fss.feev2_cohort_student_id')
    ->get()->row();
    if (empty($result)) {
      return array();
    }
     return $result;
  }

  public function _get_studentwise_fee_std_cohorts_customData($cohortstdId){
    return $this->db->select('fcs.feev2_cohort_id as cohortId, fsic.blueprint_component_id as feev2_blueprint_component_id, fsic.component_amount as compAmount, fsi.feev2_installments_id as feev2_installment_id, fbc.name as compName, fi.name as insName,fbit.feev2_installment_type_id,fbit.id as feev2_blueprint_installment_types_id')
    ->from('feev2_cohort_student fcs')
    ->where('fcs.id',$cohortstdId)
    ->join('feev2_student_schedule fss','fcs.id=fss.feev2_cohort_student_id')
    ->join('feev2_student_installments fsi','fss.id=fsi.fee_student_schedule_id')
    ->join('feev2_student_installments_components fsic','fsi.id=fsic.fee_student_installment_id')
    ->join('feev2_blueprint_components fbc','fsic.blueprint_component_id=fbc.id')
    ->join('feev2_installments fi','fsi.feev2_installments_id=fi.id')
    ->join('feev2_blueprint_installment_types fbit','fss.feev2_blueprint_installment_types_id=fbit.id')
    ->get()->result();
  }

  public function custom_std_cohorts_get_Data($cohortstdId, $instypeId){
    $cohortData = $this->_get_studentwise_fee_std_cohorts_customData($cohortstdId);
    $insData = $this->_get_fee_installments_byInstypeId($instypeId);
    $compData = [];
    foreach ($cohortData as $key => $val) {
      $compData[$val->compName][$val->feev2_installment_id] = $val;
    }
    return array('ins'=>$insData, 'comp'=>$compData);
  }

  // public function get_feeStructStatusbyStdId($std_id){
  //   $this->db->select('fee_collect_status');
  //   $this->db->where('student_id',$std_id);
  //   $status = $this->db->get('feev2_cohort_student')->row();
  //   if (!empty($status)) {
  //    if ($status->fee_collect_status == 'NOT_STARTED') {
  //       return 0;
  //     }else{
  //       return 1;
  //     }
  //   }else{
  //     return 0;
  //   }
    

  // }

 

  // public function is_fee_student_cohort_assgined($cohort_id,$student_id, $blueprint_id)
  // {
  //   $result = $this->db->select('fss .*')
  //   ->from('feev2_cohort_student fcs')
  //   ->where('fcs.student_id',$student_id)
  //   ->where('fcs.blueprint_id',$blueprint_id)
  //   ->where('fcs.feev2_cohort_id',$cohort_id)
  //   ->join('feev2_student_schedule fss','fcs.id=fss.feev2_cohort_student_id','left')
  //   ->get()->row();
  //   if (!empty($result->id)) {
  //     return TRUE;
  //   }else {
  //     return FALSE;
  //   }
  // }

  // public function get_cohort_students_details($student_id, $blueprint_id, $cohort_id){
  //   $result = $this->db->select('fcs.id as fee_cohort_student_id, fss.id as std_sch_id')
  //   ->from('feev2_cohort_student fcs')
  //   ->where('fcs.student_id',$student_id)
  //   ->where('fcs.blueprint_id',$blueprint_id)
  //   ->where('fcs.feev2_cohort_id',$cohort_id)
  //   ->join('feev2_student_schedule fss','fcs.id=fss.feev2_cohort_student_id','left')
  //   ->get()->row();
  //   if (empty($result)) {
  //     return 0;
  //   }else{
  //     return $result;
  //   }
  // }

  private function _insert_feev2_student_schedule_table($cohort_student_id, $isntallment_type_id, $total_fee, $total_con, $total_fine){

    $fstdSchd = array(
      'feev2_cohort_student_id' => $cohort_student_id,
      'total_fee' => $total_fee,
      'created_by' => $this->authorization->getAvatarId(),
      'payment_status' => 'NOT_STARTED',
      'acad_year_id' => $this->yearId,
      'feev2_blueprint_installment_types_id'=>$isntallment_type_id,
      'total_concession_amount'=>$total_con,
      'total_fine_amount'=>array_sum($total_fine),
    );
    $this->db->insert('feev2_student_schedule',$fstdSchd);
    return $this->db->insert_id();

  }

  private function _insert_feev2_student_installments_table($fee_student_schedule_id, $comp_amount, $concession_amount, $fine_amount){
    $fstdins = array();
    foreach ($comp_amount as $insId => $val) {
      $insAmount = array_sum($val);
      $conAmount = array_sum($concession_amount[$insId]);
       $fstdins[] = array(
        'fee_student_schedule_id' => $fee_student_schedule_id,
        'feev2_installments_id' =>$insId,
        'installment_amount' => $insAmount,
        'total_concession_amount' => $conAmount,
        'status' => 'NOT_STARTED',
        'total_fine_amount' => $fine_amount[$insId]
      );
    }
    $this->db->insert_batch('feev2_student_installments',$fstdins);
    $this->db->where('fee_student_schedule_id', $fee_student_schedule_id);
    return $this->db->get('feev2_student_installments')->result();

  }

  private function _insert_feev2_student_installments_components($fee_student_schedule_id, $fee_ins_query, $comp_amount, $concession_amount){
    $fstdinsComp = array();
    foreach ($fee_ins_query as  $db) {
      foreach ($comp_amount as $insId => $val) {
        foreach ($val as $compId => $amount) {
          if($insId == $db->feev2_installments_id && $db->fee_student_schedule_id == $fee_student_schedule_id) {
            if(!empty($amount)){
              $fstdinsComp[] = array(
                'fee_student_installment_id' => $db->id,
                'blueprint_component_id' => $compId,
                'component_amount' => $amount,
                'concession_amount' => $concession_amount[$insId][$compId],
              );
            }
           
          }
        }
      }
    }
    return $this->db->insert_batch('feev2_student_installments_components',$fstdinsComp);  
  }

  private function _insert_confirm_std_fee_cohort($student_id, $blueprint_id, $cohort_id, $cohort_status, $collect_status, $pay_date)
  {
    $cohort_data = array(
        'student_id' => $student_id,
        'blueprint_id' => $blueprint_id,
        'feev2_cohort_id' => $cohort_id,
        'fee_cohort_status' => $cohort_status,
        'fee_collect_status' => $collect_status,
        'pay_date' => $pay_date
    );
    $this->db->insert('feev2_cohort_student', $cohort_data);
    return $this->db->insert_id();

  }

  public function _insert_feev2_concessions($cohort_student_id, $concession_name)
  {
    $this->db->where('cohort_student_id', $cohort_student_id);
    $this->db->where('is_applied', 0);
    $query = $this->db->get('feev2_concessions')->row();
    if (!empty($query)) {
      $sql1 = "DELETE fc, fcic from feev2_concessions fc
        LEFT JOIN feev2_concessions_installment_components fcic ON fc.id=fcic.feev2_concession_id
        WHERE fc.cohort_student_id=$cohort_student_id AND is_applied = 0";
      $this->db->query($sql1);
    }
    $concession_data = array(
      'cohort_student_id' => $cohort_student_id,
      'concession_by' => $this->authorization->getAvatarId(),
      'concession_name' => $concession_name,
      'is_applied' => 0,
    );
      $this->db->insert('feev2_concessions', $concession_data);
      return $this->db->insert_id();
  }

  public function _insert_feev2_concessions_installment_components($fee_concessionId, $isntallment_type_id, $concession_amount)
  {
    $concInsComp = array();
    foreach ($concession_amount as $insId => $val) {
      foreach ($val as $compId => $amount) {
        $concInsComp[] = array(
          'feev2_concession_id' => $fee_concessionId,
          'feev2_blueprint_installment_types_id' => $isntallment_type_id,
          'feev2_installments_id' => $insId,
          'feev2_blueprint_components_id' => $compId,
          'amount' => $amount,
        );
      }
    }    
    return $this->db->insert_batch('feev2_concessions_installment_components',$concInsComp);
  }

  public function insert_cohort_details($blueprint_id, $cohort_id, $cohort_status, $isntallment_type_id, $comp_amount=[], $concession_amount=[], $student_id, $concession_name, $fine_amount, $insert_into_concession = 1, $pay_date =''){

    $query = $this->db->query("select * from feev2_cohort_student where student_id = $student_id and blueprint_id = $blueprint_id");

    if($query->num_rows() >= 1){
      return FALSE;
    }
  
    $total_fee = 0;
    foreach ($comp_amount as $key => $val) {
      $total_fee += array_sum($val);
    }
    $total_con = 0;
    foreach ($concession_amount as $key => $val) {
      $total_con += array_sum($val);
    }

    $this->db->trans_start();
    //TODO: Make the following 3 inserts as stored procedure
    $cohort_student_id = $this->_insert_confirm_std_fee_cohort($student_id, $blueprint_id, $cohort_id, $cohort_status, 'COHORT_CONFIRM', $pay_date);

    $fee_student_schedule_id = $this->_insert_feev2_student_schedule_table($cohort_student_id, $isntallment_type_id, $total_fee,$total_con, $fine_amount);

    $fee_ins_query = $this->_insert_feev2_student_installments_table($fee_student_schedule_id, $comp_amount, $concession_amount, $fine_amount);

    $fee_ins_ids = $this->_insert_feev2_student_installments_components($fee_student_schedule_id, $fee_ins_query, $comp_amount, $concession_amount);

    if ($insert_into_concession == 1) {
      $fee_concessionId = $this->_insert_feev2_concessions($cohort_student_id, $concession_name);
      $fee_concession_ins = $this->_insert_feev2_concessions_installment_components($fee_concessionId, $isntallment_type_id, $concession_amount);
    }
    
    $this->db->trans_complete();
    if ($this->db->trans_status()) {
      return array('std_sch_id'=>$fee_student_schedule_id, 'cohort_student_id'=>$cohort_student_id);
    }else{
      return FALSE;
    }
  }

  // public function _get_fees_studentDetailsby_ids($stdIds){
  //     return $this->db->select("sy.id as stdYearId, concat(ifnull(sd.first_name,''),' ',ifnull(sd.last_name,'')) as stdName, sd.admission_no, c.id as class, sy.admission_type, sy.board, c.class_name")
  //   ->from('student_year sy')
  //   ->where_in('sy.id',$stdIds)
  //   ->join('student_admission sd','sy.student_admission_id=sd.id')
  //   ->join('class c','sy.class_id=c.id')
  //   ->get()->result();
  // }

  // public function _get_fees_cohorts_ids($blueprintId, $stdData){
  //   foreach ($stdData as $key => $data) {
  //     $stdArr = (array)$data;
  //     $filters = $this->fee_library->construct_filter($blueprintId, $stdArr);
  //     $fee_master_data[$stdArr['stdYearId']] = $this->db->select('fc.id as cohortId')
  //     ->from('feev2_cohorts fc')
  //     ->where('fc.filter',$filters)
  //     ->get()->result();
  //   }
  //   $feev2_cohortsData = [];
  //   foreach ($fee_master_data as $stdId => $data) {
  //     foreach ($data as $key => $val) {
  //       $feev2_cohortsData[$stdId] = $val;
  //     }
  //   }
  //   return $feev2_cohortsData;
  // }


  public function check_fee_cohorts_student_exits($stdIds){
    $this->db->select('student_id');
    $this->db->where_in('student_id',$stdIds);
    $query = $this->db->get('feev2_cohort_student')->result();
    $dbStd = [];
    foreach ($query as $key => $valdb) {
      array_push($dbStd, $valdb->student_id);
    }
    $rStdIds = array_diff($stdIds, $dbStd);
    return $rStdIds;
  }

  public function publish_std_fee_cohort($blueprint_id,$std_data, $std_id){
    $fee_cohort = $this->__determine_cohort($blueprint_id, $std_data);
    if (!empty($fee_cohort)) {
      return $this->__insert_std_fee_cohort_publish($std_id, $blueprint_id, $fee_cohort->id, 'STANDARD');
    }else{
      return 0;
    }
  }

  private function __insert_std_fee_cohort_publish($std_id, $blueprint_id, $cohort_id, $cohort_status) {
    $cohort_data = array(
      'student_id' => $std_id,
      'blueprint_id' => $blueprint_id,
      'feev2_cohort_id' => $cohort_id,
      'publish_status' => 'PUBLISHED',
      'fee_cohort_status' => $cohort_status
    );
    return $this->db->insert('feev2_cohort_student', $cohort_data);
  }


  public function publish_feestructure_to_parent($blueprintId){
    $stdIds = $this->input->post('stdIds');
    $stdData = $this->_get_fees_studentDetailsby_ids($stdIds);
    $cohortsIds = $this->_get_fees_cohorts_ids($blueprintId, $stdData);
    $stdIds = [];
    foreach ($cohortsIds as $stdId => $val) {
      array_push($stdIds, $stdId);
    }

    
    if (empty($rStdIds)) {
      return 2;
    }
    $fCohortStdData =[];
    foreach ($rStdIds as $key => $stdId) {
      $fCohortStdData[] = array(
        'student_id' => $stdId,
        'blueprint_id' => $blueprintId,
        'feev2_cohort_id' => $cohortsIds[$stdId]->cohortId,
        'publish_status' => 'Published',
      );
    }
    return $this->db->insert_batch('feev2_cohort_student',$fCohortStdData);
  }

  public function publish_custom_feestructure_to_parent($stdId){
      $this->db->where('student_id',$stdId);
    return $this->db->update('feev2_cohort_student',array('publish_status'=>'Published'));
  }

 
  public function change_std_data_update_cohorts($std_id, $blueprint_id, $std_data){
    $fee_cohort = $this->__determine_cohort($blueprint_id, $std_data);
    if (!empty($fee_cohort)) {
      $std_cohort_id = $this->_is_cohort_already_exits($std_id, $blueprint_id, $fee_cohort->id);
      if(empty($std_cohort_id)){
        $result = $this->__update_std_fee_cohort($std_id, $blueprint_id, $fee_cohort->id, 'STANDARD','NOT_STARTED');
        if ($result) {
          $this->db->where('student_id',$std_id);
          $std_cohort_id = $this->db->get('feev2_cohort_student')->row()->id;
          return $std_cohort_id;
        }
      }else{
        return $std_cohort_id; // already exit student cohor id 
      }
    }else{
      return FALSE; //No Cohort Defined
    }
  }

  private function _is_cohort_already_exits($std_id, $blueprint_id, $cohort_id){
    $this->db->where('student_id',$std_id);
    $this->db->where('blueprint_id',$blueprint_id);
    $this->db->where('feev2_cohort_id',$cohort_id);
    $result = $this->db->get('feev2_cohort_student')->row();
    if (empty($result)) {
      return FALSE;
    }else{
      return $result->id;
    }
  }

  private function __update_std_fee_cohort($std_id, $blueprint_id, $cohort_id, $cohort_status)
  {
     $cohort_data = array(
      'student_id' => $std_id,
      'blueprint_id' => $blueprint_id,
      'feev2_cohort_id' => $cohort_id,
      'fee_cohort_status' => $cohort_status,
    );
    $this->db->where('student_id',$std_id);
    $this->db->where('blueprint_id',$blueprint_id);
    return $this->db->update('feev2_cohort_student', $cohort_data);
  }

 
  public function get_installments_types($cohort_student_id)
  {
    return $this->db->select('fit.id as feev2_installment_type_id, fit.name as type_name, default_ins')
    ->from('feev2_cohort_student fcs')
    ->where('fcs.id',$cohort_student_id)
    ->join('feev2_cohorts fc','fcs.feev2_cohort_id=fc.id')
    ->join('feev2_blueprint_installment_types fbit','fcs.blueprint_id=fbit.feev2_blueprint_id')
    ->join('feev2_installment_types fit','fbit.feev2_installment_type_id=fit.id')
    ->get()->result();
  }

  public function get_next_studentid($std_id, $class_id){
    $result = $this->db->select('sa.id as student_id')
    ->from('student_admission sa')
    ->join('student_year sy','sa.id = sy.student_admission_id')
    ->join('feev2_cohort_student fcs',"sa.id=fcs.student_id and fcs.fee_collect_status='NOT_STARTED'",'left')
    ->limit(1)
    ->where('sa.id >',$std_id)
    ->where('sy.class_id',$class_id)
    ->order_by('sa.id','asc')
    ->get()->row();
   if (empty($result)) {
     return 0;
   }else{
    return $result->student_id;
   }
  }

  public function update_concession_amount($std_sch_id, $concession_amount=[], $cohort_student_id, $feev2_blueprint_installment_types_id, $concession_name, $concession_amount_add =[]){

    $total_con = 0;
    foreach ($concession_amount as $key => $val) {
      $total_con += array_sum($val);
    }

    $this->db->trans_start();
    $fee_student_schedule_id = $this->_update_concession_amount_student_sch_tb($std_sch_id, $total_con);

    $fee_ins_query = $this->_update_con_feev2_student_installments_table($concession_amount);

    $fee_ins_ids = $this->_update_con_feev2_student_installments_components($concession_amount);

    $fee_concessionId = $this->_insert_feev2_concessions($cohort_student_id, $concession_name);

    $concession = $this->_insert_feev2_concessions_installment_components($fee_concessionId, $feev2_blueprint_installment_types_id, $concession_amount_add);

    $this->db->trans_complete();
    return $this->db->trans_status();
    // if ($this->db->trans_status() === TRUE) {
    //   return TRUE;
    // }else{
    //   return FALSE;
    // }
  }

  public function update_concession_amount_v2($std_sch_id, $concession_amount=[], $cohort_student_id, $feev2_blueprint_installment_types_id, $concession_name, $concession_amount_add =[], $pre_defined_name, $pre_defined_con_amount, $remove_pre_definedIds){

    $total_con = 0;
    foreach ($concession_amount as $key => $val) {
      $total_con += array_sum($val);
    }

    $this->db->trans_start();
    $fee_student_schedule_id = $this->_update_concession_amount_student_sch_tb($std_sch_id, $total_con);

    $fee_ins_query = $this->_update_con_feev2_student_installments_table($concession_amount);

    $fee_ins_ids = $this->_update_con_feev2_student_installments_components($concession_amount);

    $fee_concessionId = $this->_insert_feev2_concessions($cohort_student_id, $concession_name);

    if (!empty($remove_pre_definedIds)){
      $sql1 = 'delete from  feev2_concessiontype2_pre_defined_concession where id in ('.$remove_pre_definedIds.')';
      $this->db->query($sql1);
    }else{
      $this->_insert_pre_defined_concession_details($cohort_student_id, $total_con, $pre_defined_name, $pre_defined_con_amount, $concession_name);
      
    }
    $this->db->trans_complete();
    return $this->db->trans_status();
    // if ($this->db->trans_status() === TRUE) {
    //   return TRUE;
    // }else{
    //   return FALSE;
    // }
  }

  public function changes_update_concession_amount_v2($std_sch_id, $concession_amount=[]){

    $total_con = 0;
    foreach ($concession_amount as $key => $val) {
      $total_con += array_sum($val);
    }

    $this->db->trans_start();
    $fee_student_schedule_id = $this->_update_concession_amount_student_sch_tb($std_sch_id, $total_con);

    $fee_ins_query = $this->_update_con_feev2_student_installments_table($concession_amount);

    $fee_ins_ids = $this->_update_con_feev2_student_installments_components($concession_amount);

    $this->db->trans_complete();
    return $this->db->trans_status();
    // if ($this->db->trans_status() === TRUE) {
    //   return TRUE;
    // }else{
    //   return FALSE;
    // }
  }

  public function _insert_pre_defined_concession_details($cohort_student_id, $total_con, $pre_defined_name, $pre_defined_con_amount, $concession_name){
    $data = array(
      'feev2_predefined_name' =>$pre_defined_name,
      'concession_amount' =>$pre_defined_con_amount,
      'cohort_student_id' =>$cohort_student_id,
      'is_applied_status' =>0,
      'created_by' =>$this->authorization->getAvatarId(),
      'created_on' => $this->Kolkata_datetime(),
      'remarks' => $concession_name,
    );

    return $this->db->insert('feev2_concessiontype2_pre_defined_concession',$data);

  }

  public function update_concession_amount_addition($addConAmount, $blueprintId, $student_id){
      $this->db->where('student_id',$student_id);
      $this->db->where('blueprint_id',$blueprintId);
      $query = $this->db->get('feev2_additional_amount_paid');
      if ($query->num_rows() > 0) {
        $this->db->where('id',$query->row()->id);
        return $this->db->update('feev2_additional_amount_paid', array('additional_amount_paid'=> $addConAmount));
      }else{
        $data = array(
          'student_id' => $student_id, 
          'blueprint_id' => $blueprintId, 
          'additional_amount_paid' => $addConAmount, 
          'created_on' => $this->Kolkata_datetime(),
          'created_by' =>  $this->authorization->getAvatarId(),
          'acad_year_id' => $this->yearId,
          'status' => 0,
        );
        return $this->db->insert('feev2_additional_amount_paid',$data);
      }
  }

  private function _update_concession_amount_student_sch_tb($std_sch_id, $total_con){
    $fstdSchd = array(
      'total_concession_amount'=>$total_con,
    );
    $this->db->where('id', $std_sch_id);
    return $this->db->update('feev2_student_schedule',$fstdSchd);
  }

  private function _update_con_feev2_student_installments_table($concession_amount){
    $fstdins = array();
    foreach ($concession_amount as $stdInsIds => $val) {
      $conAmount = array_sum($val);
      $fstdins[] = array(
        'id'=>$stdInsIds,
        'total_concession_amount' => $conAmount,
      );
    }

    return $this->db->update_batch('feev2_student_installments',$fstdins,'id');
  }

  private function _update_con_feev2_student_installments_components($concession_amount){
    $fstdinsComp = array();
    foreach ($concession_amount as $stdInsIds => $val) {
      foreach ($val as $stdinsCompIds => $con_amount) {
        $fstdinsComp[] = array(
          'id' => $stdinsCompIds,
          'concession_amount' => $con_amount
        );
      }
    }
    return $this->db->update_batch('feev2_student_installments_components',$fstdinsComp,'id');  
  }

  public function get_assign_fee_structure($cohort_student_id){
    
    $result =  $this->db->select("fsic.component_amount as compAmount, fbc.name as compName, fi.name as insName, (case when ifnull(fsic.concession_amount_paid,'') then fsic.concession_amount_paid else fsic.concession_amount end) as concession_amount")
    ->from('feev2_student_schedule fss')
    ->where('fss.feev2_cohort_student_id',$cohort_student_id)
    ->join('feev2_student_installments fsi','fss.id=fsi.fee_student_schedule_id')
    ->join('feev2_student_installments_components fsic','fsi.id=fsic.fee_student_installment_id')
    ->join('feev2_installments fi','fsi.feev2_installments_id=fi.id')
    ->join('feev2_blueprint_components fbc','fsic.blueprint_component_id=fbc.id')
    ->get()->result();

    $insComp = [];
    foreach ($result as $key => $val) {
      $insComp[$val->insName][] = $val;
    }
    return $insComp;

  }

  public function get_cohort_student_id($cohort_student_id){
    return $this->db->select('fcs.id, blueprint_id, feev2_cohort_id, fee_collect_status, student_id,fss.id as schId, fss.total_fee')
    ->from('feev2_cohort_student fcs')
    ->join('feev2_student_schedule fss','fcs.id=fss.feev2_cohort_student_id')
    ->where('fcs.id',$cohort_student_id)
    ->get()->row();
  }


  public function get_assign_student_classwise($blueprint_id){
    // echo $blueprint_id; die();
    $this->db->select("sum(if(sa.id != fcs.student_id, 0 , 1)) as student_count,c.id as class_id, c.class_name, sum(case when fcs.publish_status='PUBLISHED' then 1 else 0 end) as published, sum(case when fcs.fee_collect_status='COHORT_CONFIRM' then 1 else 0 end) as confirm, sum(case when fcs.fee_collect_status='NOT_STARTED' then 1 else 0 end) as not_started, sum(case when fcs.fee_collect_status='STARTED' then 1 else 0 end) as started, sum(case when fcs.online_payment='PUBLISHED' then 1 else 0 end) as online_payment");
    $this->db->from('student_admission sa');
    $this->db->join('student_year sy',"sa.id=sy.student_admission_id");
    $this->db->join('class_section cs','sy.class_section_id=cs.id');
    $this->db->join('class c','sy.class_id=c.id');
    $this->db->where('sy.acad_year_id',$this->yearId);
    if($this->current_branch) {
      $this->db->where('c.branch_id',$this->current_branch);
    }
    $this->db->join('feev2_cohort_student fcs', "sa.id=fcs.student_id and fcs.blueprint_id=$blueprint_id",'left');
    $this->db->where_in('sa.admission_status',['1','2']);
    $this->db->where('sy.promotion_status!=', '4');
    $this->db->where('sy.promotion_status!=', '5');
    $this->db->where('sy.promotion_status!=', 'JOINED');
    $this->db->group_by('c.id');
    $this->db->order_by('c.display_order');
    return $this->db->get()->result();
  }

  /**
   * Get fee assignment data grouped by class
   * Optimized for AJAX loading and better performance
   *
   * @param int $blueprint_id The blueprint ID to filter by
   * @param int $class_id Optional class ID to filter by (for chunk loading)
   * @return array Result set with class-wise fee assignment data
   */
  public function get_assign_student_classwise_count($blueprint_id, $class_id = null){
    // Initialize result array
    $result = array();

    // QUERY 1: Get student count and RTE information by class
    $this->db_readonly->select("c.id as class_id, c.class_name, COUNT(DISTINCT sa.id) as student_count, SUM(CASE WHEN sy.is_rte=1 THEN 1 ELSE 0 END) as total_rte,
      SUM(CASE WHEN sy.is_rte=3 THEN 1 ELSE 0 END) as total_deposit");
    $this->db_readonly->from('student_admission sa');
    $this->db_readonly->join('student_year sy', "sa.id=sy.student_admission_id and sy.acad_year_id=$this->yearId");
    $this->db_readonly->join('class_section cs', 'sy.class_section_id=cs.id','left');
    $this->db_readonly->join('class c', "sy.class_id=c.id");
    $this->db_readonly->where("sy.acad_year_id", $this->yearId);
    $this->db_readonly->where_in('sa.admission_status', ['1', '2']);
    $this->db_readonly->where('sy.promotion_status!=', '4');
    $this->db_readonly->where('sy.promotion_status!=', '5');
    $this->db_readonly->where('sy.promotion_status!=', 'JOINED');

    // If a specific class ID is provided, filter by it
    if ($class_id !== null) {
      $this->db_readonly->where('c.id', $class_id);
    }

    // Apply branch filter if needed
    if($this->current_branch) {
      $this->db_readonly->where('c.branch_id', $this->current_branch);
    }

    $this->db_readonly->group_by('c.id');
    $this->db_readonly->order_by('c.display_order');
    $student_master = $this->db_readonly->get()->result();

    // If no students found, return empty result
    if (empty($student_master)) {
      return $result;
    }

    // Extract class IDs for the second query
    $class_ids = array();
    foreach ($student_master as $row) {
      $class_ids[] = $row->class_id;
    }

    // QUERY 2: Get fee status information for these classes
    $this->db_readonly->select("c.id as class_id,
      SUM(CASE WHEN fcs.publish_status='PUBLISHED' THEN 1 ELSE 0 END) as published,
      SUM(CASE WHEN fcs.fee_collect_status='COHORT_CONFIRM' THEN 1 ELSE 0 END) as confirm,
      SUM(CASE WHEN fcs.fee_collect_status='NOT_STARTED' THEN 1 ELSE 0 END) as not_started,
      SUM(CASE WHEN fcs.fee_collect_status='STARTED' THEN 1 ELSE 0 END) as started,
      SUM(CASE WHEN fcs.online_payment='PUBLISHED' THEN 1 ELSE 0 END) as online_payment,
      SUM(CASE WHEN fss.payment_status='FULL' THEN 1 ELSE 0 END) as fully_paid,
      SUM(CASE WHEN fss.payment_status='PARTIAL' THEN 1 ELSE 0 END) as partial_paid,
      SUM(CASE WHEN fss.payment_status='NOT_STARTED' THEN 1 ELSE 0 END) as not_started_payment");
    $this->db_readonly->from('student_admission sa');
    $this->db_readonly->join('student_year sy', "sa.id=sy.student_admission_id and sy.acad_year_id=$this->yearId");
    $this->db_readonly->join('class c', "sy.class_id=c.id");
    $this->db_readonly->join('feev2_cohort_student fcs', "sa.id=fcs.student_id AND fcs.blueprint_id=$blueprint_id", 'left');
    $this->db_readonly->join('feev2_student_schedule fss', 'fss.feev2_cohort_student_id=fcs.id', 'left');
    $this->db_readonly->where_in('c.id', $class_ids);
    $this->db_readonly->where('fcs.blueprint_id', $blueprint_id);
    $this->db_readonly->group_by('c.id');
    $fees_status = $this->db_readonly->get()->result();

    // Create a lookup array for fee status by class_id
    $fee_status_by_class = array();
    foreach ($fees_status as $row) {
      $fee_status_by_class[$row->class_id] = $row;
    }

    // Merge the data from both queries
    foreach ($student_master as $student_row) {
      $class_id = $student_row->class_id;
      $merged_row = clone $student_row;

      // Add fee status data if available
      if (isset($fee_status_by_class[$class_id])) {
        $fee_row = $fee_status_by_class[$class_id];
        $merged_row->published = $fee_row->published;
        $merged_row->confirm = $fee_row->confirm;
        $merged_row->not_started = $fee_row->not_started;
        $merged_row->started = $fee_row->started;
        $merged_row->online_payment = $fee_row->online_payment;
        $merged_row->fully_paid = $fee_row->fully_paid;
        $merged_row->partial_paid = $fee_row->partial_paid;
        $merged_row->not_started = $fee_row->not_started_payment;
      } else {
        // Set default values if no fee data found
        $merged_row->published = 0;
        $merged_row->confirm = 0;
        $merged_row->not_started = 0;
        $merged_row->started = 0;
        $merged_row->online_payment = 0;
        $merged_row->fully_paid = 0;
        $merged_row->partial_paid = 0;
        $merged_row->not_started = 0;
      }

      $result[] = $merged_row;
    }

    return $result;
  }

  public function assing_all_student_fee_structure($classId, $blueprint_id){

    $result = $this->search_std_class_wise_fee($classId, $blueprint_id);
    $blue_print = $this->fees_collection_model->ge_blueprint_details_by_id($blueprint_id);
    foreach ($result as $key => $val) {
      if (empty($val->fee_collect_status)) {
        $student_data = $this->get_std_detailsbyId($val->id, $blue_print->acad_year_id);
        $cohort_id = $this->determine_cohort($blueprint_id, $student_data);
        if (!empty($cohort_id)) {
        $insert_bluk =  $this->__cohort_data_insert_fee_table($cohort_id, $blueprint_id, $val->id);
        }
      }
    }
    return $insert_bluk;
    
  }

  private function __cohort_data_insert_fee_table($cohort_id, $blueprint_id, $stdId){
    $installments_types = $this->fees_collection_model->get_installment_types($blueprint_id);
      $default_ins = $this->fees_collection_model->get_default_ins_id($cohort_id);
      if (!empty($installments_types)) {
        foreach ($installments_types as $key => $val) {
          if (!empty($default_ins)) {
            if ($default_ins->default_ins != 0) {
              if ($default_ins->default_ins == $val->typeId) {
                $blueprint_installment_types_id = $val->feev2_blueprint_installment_types_id;
                $selectedId = $val->feev2_blueprint_installment_types_id;
              }
            }
          }
          $blueprint_installment_types_id = $val->feev2_blueprint_installment_types_id;
          $selectedId = $val->feev2_blueprint_installment_types_id;
        }
        $fee_amount = $this->fees_collection_model->fee_cohort_component_structure($cohort_id, $blueprint_installment_types_id);
      }
      $input = $this->_construct_fee_components($fee_amount, $blueprint_installment_types_id);
      return $this->fees_student_model->insert_cohort_details($blueprint_id, $cohort_id, $input['cohort_status'], $input['blueprint_installment_type_id'], $input['comp_amount'], $input['concession_amount'], $stdId, $input['concession_name']);
  }

  private function _construct_fee_components($fee_component, $blueprint_installment_type_id){

    $comp_amount = array();
    $data = array();
     foreach ($fee_component as $ins_name => $component) {
      foreach ($component as $key => $comp) {
        $comp_amount[$comp->feev2_installment_id][$comp->feev2_blueprint_component_id] = $comp->compAmount;
        $concession_amount[$comp->feev2_installment_id][$comp->feev2_blueprint_component_id] = 0;
      }
     }
    $data['cohort_status'] = 'STANDARD';
    $data['blueprint_installment_type_id'] = $blueprint_installment_type_id;
    $data['comp_amount'] = $comp_amount;
    $data['concession_amount'] = $concession_amount;
    $data['concession_name'] = '';
    return $data;
  }

 
  public function publish_all_student_fee_structure($classId, $blueprint_id){
    $result = $this->get_confirm_fee_cohort_students($classId, $blueprint_id);
    $input = [];
    foreach ($result as $key => $val) {
      $input['cohort_student_ids'][] =  $val->id;
    }
    if (empty($input)) {
      return 0;
    }
    return $this->publshed_student_fee_structure($input);
  }

  public function get_confirm_fee_cohort_students($classId, $blueprint_id)
  {
    return $this->db->select('fcs.id')
    ->from('feev2_cohort_student fcs')
    ->where('fcs.blueprint_id', $blueprint_id)
    ->join('student_admission sa','fcs.student_id=sa.id')
    ->join('student_year sy',"sa.id=sy.student_admission_id")
    ->join('class_section cs','sy.class_section_id=cs.id')
    ->join('class c','cs.class_id=c.id')
    ->where('c.id',$classId)
    ->where('fee_collect_status!=','NOT_STARTED')
    ->where('publish_status','NOT_PUBLISHED')
    ->get()->result();
  }

  public function online_all_student_fee_structure($classId, $blueprint_id){
    $result = $this->get_published_fee_cohort_students($classId, $blueprint_id);

    $input = [];
    foreach ($result as $key => $val) {
      $input['cohort_student_ids'][] =  $val->id;
    }
    if (empty($input)) {
      return 0;
    }
    return $this->_online_payment_enabled_student_fee_structure($input);
  }

  public function get_published_fee_cohort_students($classId, $blueprint_id)
  {
     return $this->db->select('fcs.id')
    ->from('feev2_cohort_student fcs')
    ->where('fcs.blueprint_id', $blueprint_id)
    ->join('student_admission sa','fcs.student_id=sa.id')
    ->join('student_year sy',"sa.id=sy.student_admission_id")
    ->join('class_section cs','sy.class_section_id=cs.id')
    ->join('class c','cs.class_id=c.id')
    ->where('c.id',$classId)
    ->where('publish_status','PUBLISHED')
    ->get()->result();
  }


  private function _online_payment_enabled_student_fee_structure($input){
    $this->db->where_in('id',$input['cohort_student_ids']);
    return $this->db->update('feev2_cohort_student', array('online_payment'=>'PUBLISHED'));
  }

  public function get_receipt_number_student_details($mode, $fee_type){
    $this->db->select("sd.id, sy.id as stdId, concat(ifnull(sd.first_name,''), ' ' ,ifnull(sd.last_name,'')) as stdName, c.class_name as clsName, sd.admission_no, c.id as class_id, ft.receipt_number, date_format(ft.paid_datetime,'%d-%m-%Y') as receiptDate, ft.amount_paid, ft.id as transId, case ft.pdf_status when '0' then 'Not Generated' else 'Generated' end as pdfStatus, ftp.payment_type, fss.id as schId");
    $this->db->from('feev2_transaction ft');
    $this->db->join('feev2_transaction_payment ftp','ft.id=ftp.fee_transaction_id');
    $this->db->where('ft.soft_delete!=1');
    $this->db->join('student_admission sd','ft.student_id=sd.id');
    $this->db->join('student_year sy','sd.id=sy.student_admission_id');
    $this->db->where('sy.acad_year_id',$this->yearId);
    $this->db->join('class c','sy.class_id=c.id');
    $this->db->join('feev2_student_schedule fss','ft.fee_student_schedule_id=fss.id');
    $this->db->join('feev2_cohort_student fcs',"fss.feev2_cohort_student_id=fcs.id and fcs.blueprint_id=$fee_type");
    if($mode == 'receipts'){
      $this->db->where('ft.receipt_number',$_POST['receipt_number']);
    }elseif($mode == 'date'){
      $fromDate = date('Y-m-d',strtotime($_POST['from_date']));
      $toDate =date('Y-m-d',strtotime($_POST['to_date']));
      $this->db->where('date_format(ft.paid_datetime,"%Y-%m-%d") BETWEEN "'.$fromDate. '" and "'.$toDate.'"');
    }else
    if ($mode='class') {
      $this->db->where('sy.class_id',$_POST['classId']);
    }

    $this->db->where('ft.status','SUCCESS');
    $this->db->where('sd.admission_status',2);
    $this->db->order_by('ft.receipt_number');
    return $this->db->get()->result();
  }

  public function update_receipt_and_date_transId($transId,$receiptNumber,$receipts_date,$schId){
    $timezone = new DateTimeZone("Asia/Kolkata" );
    $date = new DateTime($receipts_date);
    $time = new DateTime();
    $time->setTimezone($timezone);
    $merge = new DateTime($date->format('Y-m-d') .' ' .$time->format('H:i:s'));
    $receipt_date =  $merge->format('Y-m-d H:i:s');

    $this->db->where('id',$transId);
    $this->db->update('feev2_transaction', array('paid_datetime'=>$receipt_date,'receipt_number'=>$receiptNumber));

    // $this->db->where('fee_transaction_id',$transId);
    // $this->db->update('feev2_transaction_payment', array('payment_type'=>$payMode));

    // $this->db->where('id',$schId);
    // $query = $this->db->get('feev2_student_schedule')->row();

    // $fstdSchdUpdate = array(
    //   'total_card_charge_amount' => $query->total_card_charge_amount + $cardCharge,
    // );

    // $this->db->where('id',$schId);
    // $this->db->update('feev2_student_schedule', $fstdSchdUpdate);
    
    return  $this->db->affected_rows();
  }

  public function update_transaction_amount($transId, $amount_paid){
    $this->db->where('id',$transId);
    $this->db->update('feev2_transaction', array('amount_paid'=>$amount_paid));
    return  $this->db->affected_rows();
  }

  public function get_assign_fee_structure_edit($cohort_student_id){
    
    $result =  $this->db->select("fsic.id as compId, fss.id schId, fsi.id as insId,fsic.component_amount as compAmount, ifnull(component_amount_paid,0) as  component_amount_paid, fbc.name as compName, fi.name as insName, (case when ifnull(fsic.concession_amount_paid,'') then fsic.concession_amount_paid else fsic.concession_amount end) as concession_amount")
    ->from('feev2_student_schedule fss')
    ->where('fss.feev2_cohort_student_id',$cohort_student_id)
    ->join('feev2_student_installments fsi','fss.id=fsi.fee_student_schedule_id')
    ->join('feev2_student_installments_components fsic','fsi.id=fsic.fee_student_installment_id')
    ->join('feev2_installments fi','fsi.feev2_installments_id=fi.id')
    ->join('feev2_blueprint_components fbc','fsic.blueprint_component_id=fbc.id')
    ->get()->result();

    $insComp = [];
    foreach ($result as $key => $val) {
      $insComp[$val->insName][] = $val;
    }
    return $insComp;

  }

  public function update_edit_fees_student_data($input){
    $this->db->trans_start();
    foreach ($input['comp_amount'] as $schId => $ins) {
      $sumTotal = 0;
      foreach ($ins as $insId => $comp) {
        $sum = 0;
        foreach ($comp as $compId => $amount) {
          $sum += $amount;
          $this->db->where('id', $compId)->update('feev2_student_installments_components', array('component_amount' => $amount));
        }
        $this->db->where('id', $insId)->update('feev2_student_installments', array('installment_amount' => $sum));

        $this->db->where('id',$insId);
        $insData = $this->db->get('feev2_student_installments')->row();

        $paid_amount =  $insData->installment_amount_paid + $insData->total_concession_amount_paid;

        $fstdins = array(
          'status' => ($sum == $paid_amount) ? 'FULL' : 'PARTIAL',
        );
        $this->db->where('id',$insId);
        $this->db->update('feev2_student_installments',$fstdins);
        $sumTotal += $sum;
      }

      $this->db->where('id', $schId)->update('feev2_student_schedule', array('total_fee' => $sumTotal));

      $this->db->where('id',$schId);
      $query = $this->db->get('feev2_student_schedule')->row();
      $payment_status = $query->total_fee_paid + $query->total_concession_amount_paid +  $query->discount  - $query->total_fine_amount_paid - $query->total_card_charge_amount;
      $fstdSchdUpdate = array(
        'payment_status' => ($payment_status == $query->total_fee) ? 'FULL' : 'PARTIAL',
      );
      $this->db->where('id',$schId);
      $this->db->update('feev2_student_schedule', $fstdSchdUpdate);
    }
    $this->db->trans_complete();
    if ($this->db->trans_status() === true) {
      return true;
    }else{
      return false;
    }

  }

  public function update_fine_amount_every_day(){

    $today  = date('Y-m-d');

    $fineAlgo = $this->db->select('fbit.fine_amount_algo, fbit.fine_amount_params, fb.id as blueprintId')
    ->from('feev2_blueprint_installment_types fbit')
    ->join('feev2_blueprint fb','fbit.feev2_blueprint_id=fb.id')
    ->where('fbit.fine_amount_algo!=','none')
    ->where('fbit.fine_amount_algo!=','manually_enter')
    ->group_by('fb.id')
    ->get()->result();


    if (!empty($fineAlgo)) {
      $bpIds = [];
      foreach ($fineAlgo as $key => $fine) {
        array_push($bpIds, $fine->blueprintId);
      }
     
      $intallments =  $this->db->select('fsi.fee_student_schedule_id as schId, fsi.id as fsiId, fi.end_date, fi.name as installment_name, fi.id as insId, fcs.blueprint_id')
      ->from('feev2_cohort_student fcs')
      ->where_in('fcs.blueprint_id',$bpIds)
      ->join('feev2_student_schedule fss','fcs.id=fss.feev2_cohort_student_id')
      ->join('feev2_student_installments fsi','fss.id=fsi.fee_student_schedule_id')
      ->where('fsi.status!=','FULL')
      ->join('feev2_installments fi','fsi.feev2_installments_id=fi.id')
      ->where('fi.end_date < ', $today)
      ->where('fcs.student_id','4732')
      // ->where('fi.end_date !=','0000-00-00')
      // ->where('fsi.fine_manual_overwrite',0)
      ->get()->result();

    }

    $fineAmount = [];
    foreach ($fineAlgo as $key => &$val) {
      $amount = 0;
      foreach ($intallments as $key => $ins) {
        if ($ins->blueprint_id == $val->blueprintId) {
          // Use the new date calculation function
          $overduePeriods = $this->_calculate_overdue_periods($ins->end_date);

          // Skip if not overdue
          if (!$overduePeriods['is_overdue']) {
            continue;
          }
          switch ($val->fine_amount_algo) {
            case 'fixed_fine':
              // Fixed fine should only be applied once when installment becomes overdue
              // Check if fine has already been applied by checking current fine amount
              $currentFineAmount = $this->_get_current_fine_amount($ins->fsiId);
              if ($currentFineAmount == 0) {
                $amount = $val->fine_amount_params;
              } else {
                $amount = 0; // Fine already applied, don't add more
              }
              break;
            case 'fine_per_day':
              $amount = $val->fine_amount_params * $overduePeriods['days'];
              break;
            case 'fine_per_week':
              $amount = $val->fine_amount_params * $overduePeriods['weeks'];
              break;
            case 'fine_per_month':
              $amount = $val->fine_amount_params * $overduePeriods['months'];
              break;
            case 'fine_json':
              // JSON format for days/weeks/months
              $currentFineAmount = $this->_get_current_fine_amount($ins->fsiId);
              $newFineAmount = $this->_calculate_json_fine($val->fine_amount_params, $overduePeriods['days'], 'days');
              $amount = max(0, $newFineAmount - $currentFineAmount);
              break;
            case 'fine_json_bimonthly':
              // Get current fine amount to calculate cumulative fine
              $currentFineAmount = $this->_get_current_fine_amount($ins->fsiId);
              $newFineAmount = $this->_calculate_json_fine($val->fine_amount_params, $overduePeriods['bimonths'], 'bimonths');
              // Only add the difference if new fine is greater than current
              $amount = max(0, $newFineAmount - $currentFineAmount);
              break;
            case 'fine_date_range':
              // Get current fine amount to calculate cumulative fine
              $currentFineAmount = $this->_get_current_fine_amount($ins->fsiId);      

              $newFineAmount = $this->_calculate_date_range_fine($val->fine_amount_params, $ins->end_date);
    

              // Only add the difference if new fine is greater than current
              $amount = max(0, $newFineAmount - $currentFineAmount);
              break;
          }

          // Only add to fine amount if there's an actual fine calculated
          if ($amount > 0) {
            $fineAmount[$ins->schId][$ins->fsiId]['fine_amount'] = $amount;
          }
        }
      }
    }
    if(empty($fineAmount)){
      return 0;
    }
    if(!empty($fineAmount)){
      $updateTotalFine =[];
      $updateInsFine =[];
      foreach ($fineAmount as $shcId => $insArray) {
        $totaAmount = 0;
        foreach ($insArray as $insId => $amount) {
          // Get current fine amount and add new fine amount (cumulative)
          $currentFineAmount = $this->_get_current_fine_amount($insId);
          $newFineAmount = $currentFineAmount + $amount['fine_amount'];

          $totaAmount += $newFineAmount;
          $updateInsFine[] = array(
            'id'=>$insId,
            'total_fine_amount'=>$newFineAmount
          );
        }

        // Get current schedule fine amount and update cumulatively
        $currentScheduleFine = $this->_get_current_schedule_fine_amount($shcId);
        $newScheduleFine = $currentScheduleFine + ($totaAmount - $currentScheduleFine);

        $updateTotalFine[] = array(
          'id'=>$shcId,
          'total_fine_amount'=>$newScheduleFine
        );
      }
      $this->db->trans_start();
      $this->db->update_batch('feev2_student_installments',$updateInsFine,'id');
      $this->db->update_batch('feev2_student_schedule',$updateTotalFine,'id');
      $this->db->trans_complete();
      if ( $this->db->trans_status()) {
        return 1;
      }else{
        return  0;
      }
    }
    
  }


  /**
   * Get current fine amount for a student installment
   * @param int $fsiId Student installment ID
   * @return float Current fine amount
   */
  private function _get_current_fine_amount($fsiId) {
    $result = $this->db->select('ifnull(total_fine_amount, 0) as total_fine_amount')
      ->from('feev2_student_installments')
      ->where('id', $fsiId)
      ->get()->row();

    return $result ? $result->total_fine_amount : 0;
  }

  /**
   * Get current fine amount for a student schedule
   * @param int $schId Student schedule ID
   * @return float Current fine amount
   */
  private function _get_current_schedule_fine_amount($schId) {
    $result = $this->db->select('ifnull(total_fine_amount, 0) as total_fine_amount')
      ->from('feev2_student_schedule')
      ->where('id', $schId)
      ->get()->row();

    return $result ? $result->total_fine_amount : 0;
  }

  /**
   * Calculate overdue periods (days, weeks, months, bimonths) from due date
   * @param string $dueDate Due date in Y-m-d format
   * @param string $currentDate Optional current date (defaults to today)
   * @return array Array containing days, weeks, months, bimonths overdue
   */
  private function _calculate_overdue_periods($dueDate, $currentDate = null) {
    // Validate input dates
    if (empty($dueDate) || $dueDate == '0000-00-00') {
      return array(
        'days' => 0,
        'weeks' => 0,
        'months' => 0,
        'bimonths' => 0,
        'is_overdue' => false
      );
    }

    // Use current date if not provided
    if ($currentDate === null) {
      $currentDate = date('Y-m-d');
    }

    try {
      $endDate = new DateTime($dueDate);
      $current = new DateTime($currentDate);

      // Check if actually overdue
      if ($current <= $endDate) {
        return array(
          'days' => 0,
          'weeks' => 0,
          'months' => 0,
          'bimonths' => 0,
          'is_overdue' => false
        );
      }

      $interval = $current->diff($endDate);
      $days = $interval->days;

      // Calculate different period types
      $weeks = floor($days / 7) + 1;
      $months = floor($days / date('t')) + 1; // Using days in current month

      // Better bimonthly calculation - calculate actual 2-month periods
      $endDateTime = new DateTime($dueDate);
      $currentDateTime = new DateTime($currentDate);
      $bimonths = 0;

      // Calculate bimonthly periods more accurately
      $tempDate = clone $endDateTime;
      while ($tempDate < $currentDateTime) {
        $tempDate->add(new DateInterval('P2M')); // Add 2 months
        $bimonths++;
      }

      return array(
        'days' => $days,
        'weeks' => $weeks,
        'months' => $months,
        'bimonths' => $bimonths,
        'is_overdue' => true,
        'due_date' => $dueDate,
        'current_date' => $currentDate
      );

    } catch (Exception $e) {
      log_message('error', 'Date calculation error: ' . $e->getMessage() . ' for due_date: ' . $dueDate);
      return array(
        'days' => 0,
        'weeks' => 0,
        'months' => 0,
        'bimonths' => 0,
        'is_overdue' => false,
        'error' => $e->getMessage()
      );
    }
  }

  /**
   * Calculate fine amount based on JSON configuration
   * @param string $jsonParams JSON string containing fine configuration
   * @param int $period Number of days/weeks/months overdue
   * @param string $type Type of period (days, weeks, months)
   * @return float Fine amount
   */
  private function _calculate_json_fine($jsonParams, $period, $type) {
    // Validate and decode JSON
    if (empty($jsonParams)) {
      return 0;
    }

    // Handle if jsonParams is already an array (from database JSON column)
    if (is_array($jsonParams)) {
      $fineConfig = $jsonParams;
    } else {
      // Decode JSON string
      $fineConfig = json_decode($jsonParams, true);
      if (json_last_error() !== JSON_ERROR_NONE || !is_array($fineConfig)) {
        log_message('error', 'Invalid JSON format in fine_amount_params: ' . $jsonParams);
        return 0;
      }
    }

    $fineAmount = 0;

    // Support both singular and plural forms (day/days, week/weeks, etc.)
    $periodKey = rtrim($type, 's'); // Remove 's' from 'days', 'weeks', 'months', 'bimonths'
    $periodKeyPlural = $type; // Keep original plural form

    if ($periodKey === 'bimonth') {
      $periodKey = 'bimonth';
      $noPeriodKey = 'no_bimonth';
    } else {
      $noPeriodKey = 'no_' . $periodKey;
    }

    // Sort configuration by period in ascending order (except no_period entries)
    usort($fineConfig, function($a, $b) use ($periodKey, $periodKeyPlural, $noPeriodKey) {
      // Check both singular and plural forms
      $aVal = PHP_INT_MAX;
      if (isset($a[$periodKey]) && is_numeric($a[$periodKey])) {
        $aVal = $a[$periodKey];
      } elseif (isset($a[$periodKeyPlural]) && is_numeric($a[$periodKeyPlural])) {
        $aVal = $a[$periodKeyPlural];
      }

      $bVal = PHP_INT_MAX;
      if (isset($b[$periodKey]) && is_numeric($b[$periodKey])) {
        $bVal = $b[$periodKey];
      } elseif (isset($b[$periodKeyPlural]) && is_numeric($b[$periodKeyPlural])) {
        $bVal = $b[$periodKeyPlural];
      }

      // Handle no_period entries (put them at the end)
      if (isset($a[$periodKey]) && $a[$periodKey] === $noPeriodKey) $aVal = PHP_INT_MAX;
      if (isset($b[$periodKey]) && $b[$periodKey] === $noPeriodKey) $bVal = PHP_INT_MAX;

      return $aVal <=> $bVal;
    });

    // Find applicable fine based on period
    foreach ($fineConfig as $config) {
      if (!isset($config['fine'])) {
        continue;
      }

      // Check for no_period condition (applies when no specific period matches)
      if (isset($config[$periodKey]) && $config[$periodKey] === $noPeriodKey) {
        $fineAmount = $config['fine'];
        continue; // Keep checking for more specific matches
      }

      // Check for specific period match (support both singular and plural forms)
      $configPeriodValue = null;
      if (isset($config[$periodKey]) && is_numeric($config[$periodKey])) {
        $configPeriodValue = $config[$periodKey];
      } elseif (isset($config[$periodKeyPlural]) && is_numeric($config[$periodKeyPlural])) {
        $configPeriodValue = $config[$periodKeyPlural];
      }

      if ($configPeriodValue !== null && $period >= $configPeriodValue) {
        $fineAmount = $config['fine'];
      }
    }

    return $fineAmount;
  }

  /**
   * Calculate fine amount based on date ranges
   * @param string $jsonParams JSON string containing date range configuration
   * @param string $dueDate Due date of the fee
   * @return float Fine amount
   */
  private function _calculate_date_range_fine($jsonParams, $dueDate) {
    if (empty($jsonParams) || empty($dueDate) || $dueDate == '0000-00-00') {
      return 0;
    }

    // Handle if jsonParams is already an array (from database JSON column)
    if (is_array($jsonParams)) {
      $rangeConfig = $jsonParams;
    } else {
      $rangeConfig = json_decode($jsonParams, true);
      if (json_last_error() !== JSON_ERROR_NONE || !is_array($rangeConfig)) {
        return 0;
      }
    }

    $currentDate = date('Y-m-d');
    $totalFineAmount = 0;

    // Only apply fine if current date is after due date
    if ($currentDate <= $dueDate) {
      return 0;
    }

    // Log for debugging

    // Sort ranges by start_date to ensure proper cumulative application
    usort($rangeConfig, function($a, $b) {
      return strtotime($a['start_date']) - strtotime($b['start_date']);
    });

    // Check each date range and sum up applicable fines
    foreach ($rangeConfig as $range) {
      if (!isset($range['start_date']) || !isset($range['end_date']) || !isset($range['fine'])) {
        continue;
      }

      // Normalize date formats (handle single digit days/months)
      $startDate = date('Y-m-d', strtotime($range['start_date']));
      $endDate = date('Y-m-d', strtotime($range['end_date']));

      // Apply fine if current date has passed the start date of this range
      // This ensures cumulative application as per your requirements
      if ($currentDate >= $startDate) {
        $totalFineAmount += $range['fine'];

        // Log for debugging (optional)
        log_message('debug', "Date range fine applied: Start={$startDate}, End={$endDate}, Fine={$range['fine']}, Current={$currentDate}");
      }
    }
    return $totalFineAmount;
  }

  public function class_wise_fee_assing_data_by_id($classId){
    $this->db_readonly->select('sa.id as stdId')
    ->from('student_admission sa')
    ->where('sa.admission_status','2')
    ->join('student_year sy','sa.id=sy.student_admission_id')
    ->join('class c','sy.class_id=c.id')
    // ->join('class_section cs','sy.class_section_id=cs.id')
    ->where('sy.promotion_status!=', '4')
    ->where('sy.promotion_status!=', '5')
    ->where('sy.acad_year_id',$this->yearId);    
    if($classId) {
      $this->db_readonly->where_in('sy.class_id',$classId);
    }
    $this->db_readonly->order_by('c.id','sa.first_name');
    $result = $this->db_readonly->get()->result();
    $stdIds = [];
    foreach ($result as $key => $res) {
      array_push($stdIds, $res->stdId);
    }
    return $stdIds;
  }

  public function class_section_wise_fee_assing_data_by_id($classSectionId){
    $this->db_readonly->select('sa.id as stdId')
    ->from('student_admission sa')
    ->where('sa.admission_status','2')
    ->join('student_year sy','sa.id=sy.student_admission_id')
    ->join('class c','sy.class_id=c.id')
    // ->join('class_section cs','sy.class_section_id=cs.id')
    ->where('sy.promotion_status!=', '4')
    ->where('sy.promotion_status!=', '5')
    ->where('sy.acad_year_id',$this->yearId);    
    if($classSectionId) {
      $this->db_readonly->where_in('sy.class_section_id',$classSectionId);
    }
    $this->db_readonly->order_by('c.id','sa.first_name');
    $result = $this->db_readonly->get()->result();
    $stdIds = [];
    foreach ($result as $key => $res) {
      array_push($stdIds, $res->stdId);
    }
    return $stdIds;
  }

  public function fee_assing_student_fine_list($student_ids, $blueprint_id, $installment_id){
    $today = date('%Y-%m-%d');

    $this->db_readonly->select('fcs.id as cohortStdId, fcs.student_id, fss.id as schId, sa.first_name as stdName, c.class_name, fss.total_fine_amount as sch_total_fine_amount,sa.id as student_id')
    ->from('feev2_cohort_student fcs')
    ->where('fcs.blueprint_id',$blueprint_id)
    ->join('feev2_student_schedule fss','fcs.id=fss.feev2_cohort_student_id')
    ->where('fss.payment_status!=','FULL')
    ->join('student_admission sa','fcs.student_id=sa.id')
    ->where_in('sa.id',$student_ids)
    ->join('student_year sy',"sa.id=sy.student_admission_id and sy.acad_year_id = $this->yearId")
    ->join('class c','c.id=sy.class_id');
    $stdDetails  = $this->db_readonly->get()->result();

    $trans = $this->db_readonly->select('max(ft.id), ft.student_id, max(ftp.reconciliation_status) as recon')
    ->from('feev2_transaction ft')
    ->join('feev2_transaction_payment ftp','ft.id=ftp.fee_transaction_id')
    ->where_in('ft.student_id',$student_ids)
    ->group_by('ft.student_id')
    ->get()->result();

    $schIds = [];
    foreach($stdDetails as $shc){
      array_push($schIds, $shc->schId);
    }
    if (!empty($schIds)) {
      $intallments =  $this->db_readonly->select('fsi.fee_student_schedule_id as schId, fsi.id as fsiId, date_format(fi.end_date,"%d-%m-%Y") as end_date,   (ifnull(fsi.total_fine_amount,0) - ifnull(fsi.total_fine_amount_paid,0) - ifnull(fsi.total_fine_waived,0)) as fine_balance_amount, ifnull(fsi.total_fine_amount,0) as total_fine_amount, ifnull(fsi.total_fine_amount_paid,0) as total_fine_amount_paid, ifnull(fsi.total_fine_waived,0) as waived_amount, fi.name as installment_name, fi.id as insId, ifnull(fw.amount,0) as amount, ifnull(fw.is_applied,0) as is_applied, (case when fi.end_date < CURDATE() then 1 else 0 end) as date_status, (ifnull(fsi.installment_amount,0) - ifnull(fsi.installment_amount_paid,0) -  ifnull(fsi.total_concession_amount,0)  - ifnull(fsi.total_concession_amount_paid,0) - ifnull(fsi.total_adjustment_amount,0) - ifnull(fsi.total_adjustment_amount_paid,0)) as balance')
      ->from('feev2_student_installments fsi')
      ->where_in('fsi.fee_student_schedule_id',$schIds)
      ->where('fsi.feev2_installments_id',$installment_id)
      ->where('fsi.status!=','FULL')
      ->join('feev2_installments fi','fsi.feev2_installments_id=fi.id')
      ->join('feev2_fine_waiver fw',"fsi.id=fw.installment_id and fw.is_applied=0",'left')
      ->get()->result();
    }
   
    foreach ($stdDetails as $key => &$val) {
      if (!empty($intallments)) {
        foreach ($intallments as $key => $ins) {
          if ($val->schId == $ins->schId) {
            $val->installment[] = $ins;
          }
        }
      }
      if (!empty($trans)) {
        foreach ($trans as $key => $tran) {
          if ($val->student_id == $tran->student_id) {
            $val->recon_status = $tran->recon;
          }
        }
      }
     
    }
    return $stdDetails;
  }

  public function fee_wavier_assing_student_fine_list($student_ids, $blueprint_id){
    $today = date('%Y-%m-%d');

    $this->db_readonly->select('fcs.id as cohortStdId, fcs.student_id, fss.id as schId, sa.first_name as stdName, c.class_name, fss.total_fine_amount as sch_total_fine_amount')
    ->from('feev2_cohort_student fcs')
    ->where('fcs.blueprint_id',$blueprint_id)
    ->join('feev2_student_schedule fss','fcs.id=fss.feev2_cohort_student_id')
    ->where('fss.payment_status!=','FULL')
    ->join('student_admission sa','fcs.student_id=sa.id')
    ->where_in('sa.id',$student_ids)
    ->join('student_year sy',"sa.id=sy.student_admission_id and sy.acad_year_id = $this->yearId")
    ->join('class c','c.id=sy.class_id');
    $stdDetails  = $this->db_readonly->get()->result();

    $trans = $this->db_readonly->select('max(ft.id), ft.student_id, max(ftp.reconciliation_status) as recon')
    ->from('feev2_transaction ft')
    ->join('feev2_transaction_payment ftp','ft.id=ftp.fee_transaction_id')
    ->where_in('ft.student_id',$student_ids)
    ->group_by('ft.student_id')
    ->get()->result();

    $schIds = [];
    foreach($stdDetails as $shc){
      array_push($schIds, $shc->schId);
    }
    if (!empty($schIds)) {
      $intallments =  $this->db_readonly->select('fsi.fee_student_schedule_id as schId, fsi.id as fsiId, date_format(fi.end_date,"%d-%m-%Y") as end_date,   (ifnull(fsi.total_fine_amount,0) - ifnull(fsi.total_fine_amount_paid,0) - ifnull(fsi.total_fine_waived,0)) as fine_balance_amount, ifnull(fsi.total_fine_amount,0) as total_fine_amount, ifnull(fsi.total_fine_amount_paid,0) as total_fine_amount_paid, ifnull(fsi.total_fine_waived,0) as waived_amount, fi.name as installment_name, fi.id as insId, ifnull(fw.amount,0) as amount, ifnull(fw.is_applied,0) as is_applied, (case when fi.end_date < CURDATE() then 1 else 0 end) as date_status, (ifnull(fsi.installment_amount,0) - ifnull(fsi.installment_amount_paid,0) -  ifnull(fsi.total_concession_amount,0)  - ifnull(fsi.total_concession_amount_paid,0) - ifnull(fsi.total_adjustment_amount,0) - ifnull(fsi.total_adjustment_amount_paid,0)) as balance')
      ->from('feev2_student_installments fsi')
      ->where_in('fsi.fee_student_schedule_id',$schIds)
      ->where('fsi.status!=','FULL')
      ->join('feev2_installments fi','fsi.feev2_installments_id=fi.id')
      ->join('feev2_fine_waiver fw',"fsi.id=fw.installment_id and fw.is_applied=0",'left')
      ->get()->result();
    }
   
    foreach ($stdDetails as $key => &$val) {
      if (!empty($intallments)) {
        foreach ($intallments as $key => $ins) {
          if ($val->schId == $ins->schId) {
            $val->installment[] = $ins;
          }
        }
      }
      if (!empty($trans)) {
        foreach ($trans as $key => $tran) {
          if ($val->student_id == $tran->student_id) {
            $val->recon_status = $tran->recon;
          }
        }
      }
     
    }
    return $stdDetails;
  }

  public function fine_amount_get($value, $blueprint_id, $type=''){

    $this->db_readonly->select('fss.id as schId, sa.first_name as stdName, c.class_name, fbit.fine_amount_algo, fbit.fine_amount_params')
    ->from('feev2_cohort_student fcs')
    ->join('feev2_blueprint_installment_types fbit','fcs.blueprint_id=fbit.feev2_blueprint_id')
    ->where('fcs.blueprint_id',$blueprint_id)
    ->join('feev2_student_schedule fss','fcs.id=fss.feev2_cohort_student_id')
    ->where('fss.payment_status!=','FULL')
    ->join('student_admission sa','fcs.student_id=sa.id')
    ->join('student_year sy',"sa.id=sy.student_admission_id and sy.acad_year_id = $this->yearId")
    ->join('class c','c.id=sy.class_id');
    if ($value && $type == 'class') {
      $this->db_readonly->where('c.id',$value);
    }
    if($type == 'adm_no') {
      $this->db_readonly->where('sa.admission_no',$value);
    }
    if($type == 'name') {
      $this->db_readonly->where("(LOWER(sa.first_name) like '%$value%' OR (LOWER(sa.last_name) like '%$value%'))");
    }
    if($this->current_branch) {
      $this->db_readonly->where('c.branch_id',$this->current_branch);
    }
    $stdDetails  = $this->db_readonly->get()->result();

    $schIds = [];
    foreach($stdDetails as $shc){
      array_push($shc->schId, $schIds);
    }
    $intallments =  $this->db_readonly->select('fsi.fee_student_schedule_id as schId, fsi.id as fsiId, fi.end_date, (ifnull(fsi.total_fine_amount,0) - ifnull(fsi.total_fine_amount_paid,0) - ifnull(fsi.total_fine_waived,0)) as fine_amount, fi.name as installment_name, fi.id as insId')
    ->from('feev2_student_installments fsi')
    ->where_in('fsi.fee_student_schedule_id',$schIds)
    ->where('fsi.status!=','FULL')
    ->join('feev2_installments fi','fsi.feev2_installments_id=fi.id')
    ->get()->result();

    foreach ($stdDetails as $key => &$val) {
      foreach ($intallments as $key => $ins) {
        if ($val->schId == $ins->schId) {
          $val->installment[] = $ins;
        }
      }
    }
    return $stdDetails;
  }

  public function update_fine_amountbyinsId($shcId,$fsiId,$fineAmount, $previousInsAmount, $previousSchAmount){

    $input = $this->input->post();

    $this->db->trans_start();

    $updateInsFine = array(
      'total_fine_amount'=>$previousInsAmount + $fineAmount
    );

    $this->db->where('id',$fsiId);
    $this->db->update('feev2_student_installments',$updateInsFine);

    // $this->db->select_sum('total_fine_amount');
    // $this->db->where('status!=','FULL');
    // $this->db->where('fee_student_schedule_id',$shcId);
    // $query = $this->db->get('feev2_student_installments')->row();

    $updateTotalFine = array(
      'total_fine_amount'=>$previousSchAmount + $fineAmount
    );
    $this->db->where('id',$shcId);
    $this->db->update('feev2_student_schedule',$updateTotalFine);
     $this->db->trans_complete();
    if ($this->db->trans_status()) {
      return 1;
    }else{
      return  0;
    }

    // $this->db->trans_start();

    // $updateInsFine = array(
    //   'total_fine_amount'=>$fineAmount
    // );
    // $this->db->where('id',$fsiId);
    // $this->db->update('feev2_student_installments',$updateInsFine);

    // $this->db->select_sum('total_fine_amount');
    // $this->db->where('status!=','FULL');
    // $this->db->where('fee_student_schedule_id',$shcId);
    // $query = $this->db->get('feev2_student_installments')->row();

    // $updateTotalFine = array(
    //   'total_fine_amount'=>$query->total_fine_amount
    // );
    // $this->db->where('id',$shcId);
    // $this->db->update('feev2_student_schedule',$updateTotalFine);
    //  $this->db->trans_complete();
    // if ($this->db->trans_status()) {
    //   return 1;
    // }else{
    //   return  0;
    // }

  }

  public function update_wavier_amountbyinsId($shcId,$fsiId,$wavierAmount, $cohort_student_id, $fine_wavier_remarks){
    $this->db->trans_start();

    $this->_insert_feev2_fine_waiver($fsiId, $cohort_student_id, $fine_wavier_remarks, $wavierAmount);

    $waivePartialAmountCheck = $this->db->select('fsi.id as insId, total_fine_waived, fw.is_applied')
    ->from('feev2_student_installments fsi')
    ->where('fsi.id',$fsiId)
    ->join('feev2_fine_waiver fw','fsi.id=fw.installment_id')
    ->get()->row();

    if ($waivePartialAmountCheck->is_applied == 0) {
      
      $updateInsFine = array(
        'total_fine_waived'=>$wavierAmount
      );

    }else{
      $updateInsFine = array(
        'total_fine_waived'=>$waivePartialAmountCheck->total_fine_waived + $wavierAmount
      );
    }
    $this->db->where('id',$fsiId);
    $this->db->update('feev2_student_installments',$updateInsFine);
    

    $this->db->select_sum('total_fine_waived');
    $this->db->where('fee_student_schedule_id',$shcId);
    $query = $this->db->get('feev2_student_installments')->row();
    
    $updateTotalFine = array(
      'total_fine_waived'=>$query->total_fine_waived
    );

    $this->db->where('id',$shcId);
    $this->db->update('feev2_student_schedule',$updateTotalFine);

     $this->db->trans_complete();
    if ($this->db->trans_status()) {
      return 1;
    }else{
      return  0;
    }
  }

  private function _insert_feev2_fine_waiver($fsiId, $cohort_student_id, $fine_wavier_remarks, $wavierAmount){
    $this->db->where('cohort_student_id', $cohort_student_id);
    $this->db->where('installment_id', $fsiId);
    $this->db->where('is_applied', 0);
    $query = $this->db->get('feev2_fine_waiver')->row();
    if (!empty($query)) {
      $sql1 = "DELETE fc from feev2_fine_waiver fc
        WHERE fc.cohort_student_id=$cohort_student_id and fc.installment_id=$fsiId AND is_applied = 0";
      $this->db->query($sql1);
    }
    $fineWavier_data = array(
      'cohort_student_id' => $cohort_student_id,
      'installment_id' => $fsiId,
      'created_by' => $this->authorization->getAvatarId(),
      'created_on' => $this->Kolkata_datetime(),
      'remarks' => $fine_wavier_remarks,
      'is_applied' => 0,
      'amount' => $wavierAmount,
    );
    $this->db->insert('feev2_fine_waiver', $fineWavier_data);
    return $this->db->insert_id();
  }

  public function get_transactionsIds_for_receipts($blueprintId){
   return $this->db->select('ft.id as transId')
    ->from('feev2_cohort_student fcs')
    ->where('fcs.blueprint_id',$blueprintId)
    ->join('feev2_student_schedule fss','fcs.id=fss.feev2_cohort_student_id')
    ->join('feev2_transaction ft','fss.id=ft.fee_student_schedule_id')
    ->get()->result();
  }

  public function concession_update($filters){
    $blueprint_id = $filters['blueprint_id'];
    $this->db->select("fcs.id as cohort_student_id, fcs.blueprint_id, fcs.student_id, fss.id as schId,  concat(ifnull(sa.first_name,''),' ',ifnull(sa.last_name,'')) as  stdName,  concat(ifnull(c.class_name,''),' ',ifnull(cs.section_name,'')) as class_name, sa.admission_no, concat(ifnull(p.first_name,''),' ',ifnull(p.last_name,'')) as  parent_name, fss.total_fee, ifnull( total_fee_paid,0) as total_fee_paid, ifnull(total_concession_amount,0) as total_concession_amount, ifnull( total_concession_amount_paid,0) as total_concession_amount_paid, payment_status, ifnull(famp.additional_amount_paid,0) as additional_amount_paid")
    ->from('feev2_cohort_student fcs')
    ->where('fcs.blueprint_id',$blueprint_id)
    ->join('feev2_student_schedule fss','fcs.id=fss.feev2_cohort_student_id')
    ->join('student_admission sa','fcs.student_id=sa.id')
    ->join('student_year sy',"sa.id=sy.student_admission_id and sy.acad_year_id = $this->yearId")
    ->join('student_relation sr',"sr.std_id=sa.id and sr.relation_type='Father'")
    ->join('parent p','p.id=sr.relation_id')
    ->join('class c','c.id=sy.class_id')
    ->join('class_section cs','cs.id=sy.class_section_id','left')
    ->join('feev2_additional_amount_paid famp','sa.id=famp.student_id','left');
    if (isset($filters['payment_status'])) {
      $this->db->where('fss.payment_status',$filters['payment_status']);
    }
    if ($filters['show_full_payment']) {
      $this->db->where('fss.payment_status','FULL');
    }else{
      $this->db->where('fss.payment_status!=','FULL');
    }
    if (isset($filters['board'])) {
      $this->db->where('sy.board',$filters['board']);
    }
    if (isset($filters['quota'])) {
      $this->db->where('sa.quota',$filters['quota']);
    }
    if (isset($filters['gender'])) {
      $this->db->where('sa.gender',$filters['gender']);
    }
    if (isset($filters['physical_disability'])) {
      $this->db->where('sh.physical_disability',$filters['physical_disability']);
    }
    if (isset($filters['is_lifetime_student'])) {
      $this->db->where('sa.life_time_fee_mode',$filters['is_lifetime_student']);
    }
    if (isset($filters['boarding'])) {
      $this->db->where('sy.boarding',$filters['boarding']);
    }
    if (isset($filters['stop'])) {
      $this->db->where('sy.stop',$filters['stop']);
    }
    if (isset($filters['pickup_mode'])) {
      $this->db->where('sy.pickup_mode',$filters['pickup_mode']);
    }
    if (isset($filters['has_staff'])) {
      $this->db->where('sa.has_staff',$filters['has_staff']);
    }
    if (isset($filters['has_sibling'])) {
      $this->db->where('sa.sibling_type',$filters['has_sibling']);
    }
    if (isset($filters['class_type'])) {
      $this->db->where('c.type',$filters['class_type']);
    }
    if(isset($filters['has_transport'])) {
      $this->db->where('sa.has_transport',$filters['has_transport']);
    }
    if(isset($filters['has_transport_km'])) {
      $this->db->where('sy.has_transport_km',$filters['has_transport_km']);
    }
    if(isset($filters['combination'])) {
      $this->db->where('sy.combination',$filters['combination']);
    }
    if(isset($filters['admission_type'])) {
      $this->db->where('sy.admission_type',$filters['admission_type']);
    }
    if(isset($filters['medium'])) {
      $this->db->where('sy.medium',$filters['medium']);
    }
    if(isset($filters['is_rte'])) {
      $this->db->where('sy.is_rte',$filters['is_rte']);
    }
    if(isset($filters['category'])) {
      $this->db->where('sa.category',$filters['category']);
    }
    if(isset($filters['academic_year_of_joining'])) {
      $this->db->where('sa.admission_acad_year_id',$filters['academic_year_of_joining']);
    }
    if(isset($filters['class'])) {
      $this->db->where('sy.class_id',$filters['class']);
    }
    if(isset($filters['attempt'])) {
      $this->db->where('sa.attempt',$filters['attempt']);
    }
    $stdDetails  = $this->db->get()->result();

    $cohortStdIds = [];
    if (!empty($stdDetails)) {
      foreach ($stdDetails as $key => $val) {
        array_push($cohortStdIds, $val->cohort_student_id);
      }
      $concession = $this->db->select('fc.cohort_student_id, fc.concession_name')
      ->from('feev2_concessions fc')
      ->where_in('fc.cohort_student_id', $cohortStdIds)
      ->get()->result();

      foreach ($stdDetails as $key => &$std) {
        $filterString = '';
        if (!empty($concession)) {
          foreach ($concession as $key => $con) {
            if ($std->cohort_student_id == $con->cohort_student_id) {
              if (!empty($filterString))
              $filterString = $filterString . ', ';
              $filterString = $filterString . $con->concession_name;

            }
          }
        }
        $std->concession_remarks = $filterString;
      }
    }
    return $stdDetails;
  }

  public function get_concession_applied_amount($cohort_student_id){
    $total_concession = $this->db->select("fcs.id as cohort_student_id, ifnull(fss.total_concession_amount,0)  + ifnull(fss.total_concession_amount_paid,0) as total_concession_amount")
    ->from('feev2_cohort_student fcs')
    ->where('fcs.id',$cohort_student_id)
    ->join('feev2_student_schedule fss','fcs.id=fss.feev2_cohort_student_id')
    ->get()->row();
    $concessionRemarks = $this->db->select('fc.cohort_student_id, fc.concession_name')
    ->from('feev2_concessions fc')
    ->where('fc.cohort_student_id',$cohort_student_id)
    ->get()->result();
     $filterString = '';
      foreach ($concessionRemarks as $key => $val) {
        if (!empty($filterString))
          $filterString = $filterString . ', ';
          $filterString = $filterString . $val->concession_name;
    }
    $total_concession->concession_remarks = $filterString;
    return $total_concession;
  }
  public function concession_view($schId){
      $result = $this->db->select('fss.feev2_blueprint_installment_types_id, fsi.id as stdInsIds, fsic.id as stdinsCompIds, fsic.component_amount as compAmount, fbc.id component_id, fbc.name as compName, fi.id as feev2_installment_id, fi.name as insName, fsic.concession_amount,  ifnull(fsic.concession_amount_paid,0) as pre_concession_amount, ifnull(fsic.component_amount_paid,0) as component_amount_paid,  fbc.is_concession_eligible, (fsic.component_amount - ifnull(fsic.component_amount_paid + fsic.concession_amount_paid, 0)) as balance')
      ->from('feev2_student_schedule fss')
      ->where('fss.id',$schId)
      ->join('feev2_student_installments fsi','fss.id=fsi.fee_student_schedule_id')
      ->join('feev2_student_installments_components fsic','fsi.id=fsic.fee_student_installment_id')
      ->join('feev2_installments fi','fsi.feev2_installments_id=fi.id')
      ->join('feev2_blueprint_components fbc','fsic.blueprint_component_id=fbc.id')
      ->order_by('fi.id')
      ->get()->result();
      $pre_data = [];
      foreach ($result as $key => $val) {
        $pre_data[$val->insName][] = $val;
      }
      return $pre_data;

    // $concession = $this->db->select('fss.id as schId, fss.feev2_blueprint_installment_types_id, fsi.id as stdInsIds, fsic.id as stdinsCompIds, fsic.component_amount as compAmount, fbc.id component_id, fbc.name as compName, fi.id as feev2_installment_id, fi.name as insName, fsic.concession_amount,  ifnull(fsic.concession_amount_paid,0) as pre_concession_amount, ifnull(fsic.component_amount_paid,0) as component_amount_paid,  fbc.is_concession_eligible, (fsic.component_amount - ifnull(fsic.component_amount_paid + fsic.concession_amount_paid, 0)) as balance')
    // ->from('feev2_student_schedule fss')
    // ->where('fss.id',$schId)
    // ->join('feev2_student_installments fsi','fss.id=fsi.fee_student_schedule_id')
    // // ->where('fsi.status!=','FULL')
    // ->join('feev2_student_installments_components fsic','fsi.id=fsic.fee_student_installment_id')
    // ->join('feev2_installments fi','fsi.feev2_installments_id=fi.id')
    // ->join('feev2_blueprint_components fbc',"fsic.blueprint_component_id=fbc.id")
    // ->get()->result();

    // return $concession;
  }

  public function get_student_schdule_amount_details($stdId, $bpId){
    return $this->db->select('fss.id as schId, total_fee, total_fee_paid, total_card_charge_amount, payment_status')
    ->from('feev2_cohort_student fcs')
    ->where('fcs.student_id',$stdId)
    ->where('fcs.blueprint_id',$bpId)
    ->join('feev2_student_schedule fss','fcs.id=fss.feev2_cohort_student_id')
    ->get()->row();
  }

   public function update_student_schd_amount_details($schId, $amount_paid, $payment_status, $cardCharge){

    
    $this->db->where('id',$schId);
    return  $this->db->update('feev2_student_schedule', array('total_fee_paid'=>$amount_paid,'payment_status'=>$payment_status,'total_card_charge_amount'=>$cardCharge));
  }

  public function get_all_prints_fee_details(){
    $this->db->select('fb.id, fb.name as blueprint_name');
    $this->db->from('feev2_blueprint fb');
    $this->db->where('fb.acad_year_id',$this->yearId);
    if($this->current_branch) {
      $this->db->where('fb.branches',$this->current_branch);
    }
    $blueprints = $this->db->get()->result();

    $this->db->select('fc.id as cohortId, fb.id as bpId, fb.name as blueprint_name, fc.friendly_name');
    $this->db->from('feev2_blueprint fb');
    $this->db->where('fb.acad_year_id',$this->yearId);
    if($this->current_branch) {
      $this->db->where('fb.branches',$this->current_branch);
    }
    $this->db->join('feev2_cohorts fc','fb.id=fc.blueprint_id');
    $cohortsFilter = $this->db->get()->result();

    foreach ($blueprints as $key => &$bp) {
      foreach ($cohortsFilter as $key => $cohort) {
        if ($bp->id == $cohort->bpId) {
          $bp->cohorts[] = array(
            'cohortId'=>$cohort->cohortId,
            'friendly_name'=>$cohort->friendly_name
          );
        }
      }
    }
    return $blueprints;
  }

  public function get_fee_assigned_details($stdId){
    $result = $this->db->select("fcs.id as cohort_student_id, fcs.student_id,fcs.fee_collect_status, fcs.publish_status, fcs.blueprint_id, fss.id as schId, fss.total_fee, ifnull(fss.total_concession_amount,0)  + ifnull(fss.total_concession_amount_paid,0) as total_concession_amount, fcs.online_payment")
    ->from('feev2_cohort_student  fcs')
    ->where('fcs.student_id',$stdId)
    ->join('feev2_student_schedule fss','fcs.id=fss.feev2_cohort_student_id')
    ->get()->result();
    $bpIds = [];
    foreach ($result as $key => $res) {
      $bpIds[$res->blueprint_id] = $res;
    }    
    return $bpIds;
  }

  public function publish_assigned_fee($cohortStudentIds){  
    $cohortStudentIdArry = [];
    foreach ($cohortStudentIds as $key => $val) {
      $cohortStudentIdArry[] = array(
        'id'=>$val,
        'publish_status'=>'PUBLISHED',
        'online_payment'=>'PUBLISHED'
      );
    }   
    return $this->db->update_batch('feev2_cohort_student',$cohortStudentIdArry,'id');
  }

  public function get_receipt_templatebyId($fee_type){
    $result = $this->db->where('blueprint_id',$fee_type)->get('feev2_receipt_template')->row();
    if (!empty($result)) {
      return $result->template;
    }else{
      return '';
    }

  }

  public function class_wise_student_data_fee($acadJoiningId, $rteType, $quotaType, $classId, $medium, $category, $admissionType, $boards, $boarding, $staff, $classType, $has_sibling, $transport, $transport_km, $transstop,$transport_pic_mode,$gender, $PhysicalDisability, $IsLifeTimeFee, $combination, $attemptType){

    $this->db_readonly->select('sa.id as stdId')
    ->from('student_admission sa')
    ->where('sa.admission_status','2')
    ->join('student_year sy','sa.id=sy.student_admission_id')
    ->join('student_health sh','sh.student_id=sa.id','left')
    ->join('class c','sy.class_id=c.id')
    ->where('sy.promotion_status!=', '4')
    ->where('sy.promotion_status!=', '5')
    ->where('sy.promotion_status!=', 'JOINED')
    ->where('sy.acad_year_id',$this->yearId);
    // ->where('sy.class_id',$clsId)
    if ($boards) {
      $this->db_readonly->where('sy.board',$boards);
    }
    if ($quotaType) {
      $this->db_readonly->where('sa.quota',$quotaType);
    }
    if ($gender) {
      $this->db_readonly->where('sa.gender',$gender);
    }
    if ($PhysicalDisability) {
      $this->db_readonly->where('sh.physical_disability',$PhysicalDisability);
    }
    if ($IsLifeTimeFee) {
      $this->db_readonly->where('sa.life_time_fee_mode',$IsLifeTimeFee);
    }
    if ($boarding) {
      $this->db_readonly->where('sy.boarding',$boarding);
    }
    if ($transstop) {
      $this->db_readonly->where('sy.stop',$transstop);
    }
    if ($transport_pic_mode) {
      $this->db_readonly->where('sy.pickup_mode',$transport_pic_mode);
    }
    if ($staff) {
      $this->db_readonly->where('sa.has_staff',$staff);
    }
    if ($has_sibling) {
      $this->db_readonly->where('sa.sibling_type',$has_sibling);
    }
    if ($classType) {
      $this->db_readonly->where('c.type',$classType);
    }
    if($transport) {
      $this->db_readonly->where('sa.has_transport',$transport);
    }
    if($transport_km) {
      $this->db_readonly->where('sy.has_transport_km',$transport_km);
    }
    if($combination) {
      $this->db_readonly->where('sy.combination',$combination);
    }
    if($admissionType) {
      $this->db_readonly->where('sy.admission_type',$admissionType);
    }
    if($medium) {
      $this->db_readonly->where('sy.medium',$medium);
    }
    if($rteType) {
      $this->db_readonly->where('sy.is_rte',$rteType);
    }
    if($category) {
      $this->db_readonly->where('sa.category',$category);
    }
    if($acadJoiningId) {
      $this->db_readonly->where('sa.admission_acad_year_id',$acadJoiningId);
    }
    if($classId) {
      $this->db_readonly->where('sy.class_id',$classId);
    }
    if($attemptType) {
      $this->db_readonly->where('sa.attempt',$attemptType);
    }
    if($this->current_branch) {
      $this->db_readonly->where('c.branch_id',$this->current_branch);
    }
    $this->db_readonly->order_by('sa.first_name');
    $result = $this->db_readonly->get()->result();

    $stdIds = [];
    foreach ($result as $key => $res) {
      array_push($stdIds, $res->stdId);
    }
    return $stdIds;
  }
  
  public function class_wise_student_data_fee_byId($classId){
    $this->db_readonly->select('sa.id as stdId')
    ->from('student_admission sa')
    ->where('sa.admission_status','2')
    ->join('student_year sy','sa.id=sy.student_admission_id')
    ->join('class c','sy.class_id=c.id')
    ->where('sy.promotion_status!=', '4')
    ->where('sy.promotion_status!=', '5')
    ->where('sy.promotion_status!=', 'JOINED')
    ->where('sy.acad_year_id',$this->yearId);    
    if($classId) {
      $this->db_readonly->where('sy.class_id',$classId);
    }
    $this->db_readonly->order_by('sa.first_name');
    $result = $this->db_readonly->get()->result();
    $stdIds = [];
    foreach ($result as $key => $res) {
      array_push($stdIds, $res->stdId);
    }
    return $stdIds;
  }

  public function class_section_wise_student_data_fee_byId($input){
    $this->db_readonly->select('sa.id as stdId')
    ->from('student_admission sa')
    ->where('sa.admission_status','2')
    ->join('student_year sy','sa.id=sy.student_admission_id')
    ->join('class c','sy.class_id=c.id')
    ->join('class_section cs','sy.class_section_id=cs.id')
    ->where('sy.promotion_status!=', '4')
    ->where('sy.promotion_status!=', '5')
    ->where('sy.promotion_status!=', 'JOINED')
    ->where('sy.acad_year_id',$this->yearId);    
    if(isset($input['classSectionId'])) {
      $this->db_readonly->where_in('sy.class_section_id',$input['classSectionId']);
    }
    if(isset($input['stdName'])) {
      $stdName = $input['stdName'];
      $this->db_readonly->where("(LOWER(sa.first_name) like '%$stdName%' OR (LOWER(sa.last_name) like '%$stdName%'))");
    }
    if(isset($input['admission_no'])) {
      $this->db_readonly->where('sa.admission_no',$input['admission_no']);
    }
    if($this->current_branch) {
      $this->db_readonly->where('c.branch_id',$this->current_branch);
    }
    $this->db_readonly->order_by('c.id','cs.id','sa.first_name');
    $result = $this->db_readonly->get()->result();
    $stdIds = [];
    foreach ($result as $key => $res) {
      array_push($stdIds, $res->stdId);
    }
    return $stdIds;
  }

  public function get_admission_wise_student_data($admission_no){
    $result = $this->db_readonly->select('sa.id as stdId')
    ->from('student_admission sa')
    ->where("sa.admission_no", $admission_no)
    ->where('sa.admission_status','2')
    ->join('student_year sy','sa.id=sy.student_admission_id')
    ->join('class c','sy.class_id=c.id')
    ->where('sy.promotion_status!=', '4')
    ->where('sy.promotion_status!=', '5')
    ->where('sy.promotion_status!=', 'JOINED')
    ->where('sy.acad_year_id',$this->yearId)
    ->get()->result();
    $stdIds = [];
    foreach ($result as $key => $res) {
      array_push($stdIds, $res->stdId);
    }
    return $stdIds;
  }

  public function get_search_student_fee($stdName){
    $result = $this->db_readonly->select('sa.id as stdId')
    ->from('student_admission sa')
    ->where("(LOWER(sa.first_name) like '%$stdName%' OR (LOWER(sa.last_name) like '%$stdName%'))")
    ->where('sa.admission_status','2')
    ->join('student_year sy','sa.id=sy.student_admission_id')
    ->join('class c','sy.class_id=c.id')
    ->where('sy.promotion_status!=', '4')
    ->where('sy.promotion_status!=', '5')
    ->where('sy.promotion_status!=', 'JOINED')
    ->where('sy.acad_year_id',$this->yearId)
    ->get()->result();
    $stdIds = [];
    foreach ($result as $key => $res) {
      array_push($stdIds, $res->stdId);
    }
    return $stdIds;
  }

  public function fee_assing_student_list($stdIds, $blueprint_id){
    $stdArry = [];
    foreach ($stdIds as $key => $stdId) {
      $stdData = new stdClass();
      $cohortStudent = $this->_get_assigned_student_amount($stdId, $blueprint_id);
      $student = $this->get_std_detailsbyId($stdId, $this->yearId);
      $cohort = $this->_get_cohort_details($blueprint_id, $student);
      if (!empty($cohortStudent)) {
        $stdData->assigned_status  = 1;
        $stdData->fee_collect_status  = ($cohortStudent->fee_collect_status =='STARTED') ? 1 : 2;
        $stdData->publish_status  = ($cohortStudent->publish_status =='PUBLISHED') ? 1 : 0;
        $stdData->online_payment  = ($cohortStudent->online_payment =='PUBLISHED') ? 1 : 0;
        $stdData->cohot_student_id  = $cohortStudent->id;
        $stdData->cohort_id  = $cohortStudent->feev2_cohort_id;
        $stdData->total_fee  =  $cohortStudent->total_fee;
      }else{
        $stdData->assigned_status  = 0;
        $stdData->publish_status  = 0;
        $stdData->online_payment  = 0;
        $stdData->cohot_student_id  = 0;
        $stdData->fee_collect_status  = 0;
        $stdData->cohort_id  = ($cohort !='') ? $cohort->id : '';
        $stdData->total_fee  = ($cohort !='') ? $cohort->total_fee : '0'; 
      }
      $stdData->student_name  = $student->stdName;
      $stdData->admission_no  = $student->admission_no;
      $stdData->className  = $student->className;
      $stdData->is_rte  = $student->is_rte;
      $stdData->student_id  = $student->std_admission_id;
      $stdData->friendly_name  = ($cohort !='') ? $cohort->friendly_name : '';
      array_push($stdArry, $stdData);
    }
    array_multisort(array_column($stdArry,'className'), SORT_ASC, array_column($stdArry, 'student_name'), SORT_ASC, $stdArry);
    $cohorts = $this->db_readonly->select('fc.id, fc.friendly_name')
    ->from('feev2_cohorts fc')
    ->where('fc.acad_year_id',$this->yearId)
    ->where('blueprint_id',$blueprint_id)
    ->get()->result();

    return array('stdArry' => $stdArry, 'cohorts' =>$cohorts);
  }
  
  private function _get_cohort_details($blueprint_id, $std_data) {
    $std_arr = (array)$std_data;
    $filters = $this->fee_library->construct_filter($blueprint_id, $std_arr);
    $result = $this->db_readonly->select('fc.id, fc.total_fee, fc.friendly_name')
      ->from('feev2_cohorts fc')
      ->where('fc.filter',$filters)
      ->where('blueprint_id',$blueprint_id)
      ->get()->row();
    return $result;
  }

  public function _get_assigned_student_amount($stdId, $blueprint_id){
    return $this->db_readonly->select('fcs.*, fss.total_fee')
    ->from('feev2_cohort_student fcs')
    ->join('feev2_student_schedule fss','fcs.id=fss.feev2_cohort_student_id')
    // ->join('feev2_student_schedule fss','fcs.id=fss.feev2_cohort_student_id')
    ->where('fcs.blueprint_id',$blueprint_id)
    ->where('fcs.student_id',$stdId)
    ->get()->row();
  }

  public function check_previous_balance_amount($stdId){
    return $this->db_readonly->select("fb.name as bpName, format(ifnull(fss.total_fee,0) - ifnull(fss.total_fee_paid,0) -  ifnull(fss.total_concession_amount,0)  - ifnull(fss.total_concession_amount_paid,0) - ifnull(fss.total_adjustment_amount,0) - ifnull(fss.total_adjustment_amount_paid,0),2,'EN_IN') as balance")
    ->from('feev2_blueprint fb')
    ->where('fb.acad_year_id <',$this->yearId)
    ->join('feev2_cohort_student fcs','fb.id=fcs.blueprint_id')
    ->where('fcs.student_id',$stdId)
    ->join('feev2_student_schedule fss','fcs.id=fss.feev2_cohort_student_id')
    ->where('fss.payment_status!=','FULL')
    ->get()->result();
  }

  public function update_adjustment_amount($std_sch_id, $adjustment_amount=[], $cohort_student_id, $feev2_blueprint_installment_types_id, $adjustment_remarks, $concession_amount_add =[]){

    $total_adjust = 0;
    foreach ($adjustment_amount as $key => $val) {
      $total_adjust += array_sum($val);
    }
    $this->db->trans_start();
    $fee_student_schedule_id = $this->_update_adjustment_amount_student_sch_tb($std_sch_id, $total_adjust);

    $fee_ins_query = $this->_update_adjustment_feev2_student_installments_table($adjustment_amount);

    $fee_ins_ids = $this->_update_adjustment_feev2_student_installments_components($adjustment_amount);

    $fee_concessionId = $this->_insert_feev2_adjustment($cohort_student_id, $adjustment_remarks);

    $this->db->trans_complete();
    return $this->db->trans_status();
    // if ($this->db->trans_status() === TRUE) {
    //   return TRUE;
    // }else{
    //   return FALSE;
    // }
  }

  private function _insert_feev2_adjustment($cohort_student_id, $adjustment_remarks){
    $this->db->where('cohort_student_id', $cohort_student_id);
    $this->db->where('is_applied', 0);
    $query = $this->db->get('feev2_adjustment')->row();
    if (!empty($query)) {
      $sql1 = "DELETE fa from feev2_adjustment fa
        WHERE fa.cohort_student_id=$cohort_student_id AND is_applied = 0";
      $this->db->query($sql1);
    }

    $adjustment_data = array(
      'cohort_student_id' => $cohort_student_id,
      'adjustment_by' => $this->authorization->getAvatarId(),
      'adjustment_remarks' => $adjustment_remarks,
      'is_applied' => 0,
    );
    $this->db->insert('feev2_adjustment', $adjustment_data);
    return $this->db->insert_id();
  }

  private function _update_adjustment_amount_student_sch_tb($std_sch_id, $total_adjust){
    $fstdSchd = array(
      'total_adjustment_amount'=>$total_adjust,
    );
   
    $this->db->where('id', $std_sch_id);
    return $this->db->update('feev2_student_schedule',$fstdSchd);
  }

  private function _update_adjustment_feev2_student_installments_table($adjustment_amount){
    $fstdins = array();
    foreach ($adjustment_amount as $stdInsIds => $val) {
      $adjAmount = array_sum($val);
      $fstdins[] = array(
        'id'=>$stdInsIds,
        'total_adjustment_amount' => $adjAmount,
      );
    }

    return $this->db->update_batch('feev2_student_installments',$fstdins,'id');
  }

  private function _update_adjustment_feev2_student_installments_components($adjustment_amount){
    $fstdinsComp = array();
    foreach ($adjustment_amount as $stdInsIds => $val) {
      foreach ($val as $stdinsCompIds => $adj_amount) {
        $fstdinsComp[] = array(
          'id' => $stdinsCompIds,
          'adjustment_amount' => $adj_amount
        );
      }
    }
    return $this->db->update_batch('feev2_student_installments_components',$fstdinsComp,'id');  
  }


  public function insert_fee_audit($student_id, $action, $description, $action_by, $blueprint_id){
    $data = array(
      'student_id' => $student_id,
      'action' => $action,
      'description' => $description,
      'action_by' => $action_by,
      'blueprint_id' => $blueprint_id
    );
    return $this->db->insert('fee_audit',$data);
  }

  public function get_siblings_data_by_id($std_id){

    $avatars = 'select a.user_id from student_admission sa join parent p on sa.id=p.student_id join avatar a on a.stakeholder_id=p.id where sa.id = '.$std_id.' and a.avatar_type=2';

    $avatar = $this->db->query($avatars)->row();

    $students = "select sa.id as sa_id, a.user_id, concat(ifnull(sa.first_name,''), ' ' ,ifnull(sa.last_name,'')) as sName, concat(cs.class_name, cs.section_name) as csName 
      from avatar a 
      join parent p on a.stakeholder_id = p.id
      join student_admission sa on p.student_id= sa.id
      join student_year sy on sa.id=sy.student_admission_id and sy.acad_year_id=$this->yearId
      left join class_section cs on cs.id=sy.class_section_id  where a.user_id = $avatar->user_id and a.avatar_type=2";
    $siblings = $this->db->query($students)->result();
    return  $siblings;

  }

  public function get_fine_amount_details_byschId($schId){
    
    return $this->db->select("fss.id as schId, fsi.id as fsiId, ifnull(fsi.total_fine_amount,0) as total_fine_amount, ifnull(fsi.total_fine_amount_paid,0) as total_fine_amount_paid, ifnull(fsi.total_fine_waived,0) as total_fine_waived, fi.name as insName, fsi.status, fss.feev2_cohort_student_id")
    ->from('feev2_student_schedule fss')
    ->join('feev2_student_installments fsi','fss.id=fsi.fee_student_schedule_id')
    ->join('feev2_installments fi','fsi.feev2_installments_id=fi.id')
    ->where('fss.id',$schId)
    ->get()->result();

  }

  public function get_km_details(){
    return $this->db->select('fk.*')
    ->from('feev2_km fk')
    ->get()->result();
  }

  public function get_route_details(){
    return $this->db->select('distinct(route) as route')->get('feev2_stops')->result();
  }

  public function get_transport_blueprint(){
    return $this->db->select('id, name, filter_blueprint')
    ->from('feev2_blueprint')
    ->where('acad_year_id',$this->yearId)
    ->where('is_transport',1)
    ->get()->result();
  }
  public function get_transaction_detail($clsId, $has_transport_km, $pickup_mode){

   $this->db_readonly->select("fc.filter, concat(ifnull(sa.first_name,''),' ',ifnull(sa.last_name,'')) as student_name, c.class_name, cs.section_name")
    ->from('feev2_blueprint fb')
    ->where('fb.is_transport',1)
    ->where('fb.acad_year_id',$this->yearId)
    ->join('feev2_cohort_student fcs','fb.id=fcs.blueprint_id')
    ->join('feev2_cohorts fc','fcs.feev2_cohort_id=fc.id')
    ->join('student_admission sa','fcs.student_id=sa.id')
    ->join('student_year sy','sa.id=sy.student_admission_id')
    ->wherE('sy.acad_year_id',$this->yearId)
    ->join('class c','sy.class_id=c.id')
    ->join('class_section cs','sy.class_section_id=cs.id','left');
    if ($clsId) {
      $this->db_readonly->where_in('c.id',$clsId);
    }
    $this->db_readonly->order_by('c.id, cs.id, sa.first_name');
    $result = $this->db_readonly->get()->result();

    foreach ($result as $key => $val) {
      $fsArr = explode(',', $val->filter);
      $transport = '';
      foreach ($fsArr as $fs) {
        $temp = explode('=',$fs);
        $key = $temp[0];
        $value = $temp[1];
        switch ($key) {
          case 'pickup_mode':
            $val->pickup_mode = $this->get_transport_mode_namebyValue($value);
            break; 
          case 'has_transport_km':
            $val->km_stage = $value;
            $val->stop = $this->get_transport_stage_stop($value);
            $val->route = $this->get_transport_stage_route($value);
            break; 
          default:
            // code...
            break;
        }
      }
    }
    return $result;
  }

  public function get_transport_stage_stop($stage){
    $result = $this->db_readonly->select('fs.name as stop')
    ->from('feev2_km fk')
    ->where('fk.kilometer',$stage)
    ->join('feev2_stops fs','fs.kilometer=fk.id')
    ->get()->row();

    return $result->stop;
  }

  public function get_transport_stage_route($stage){
    $result = $this->db_readonly->select('fs.route')
    ->from('feev2_km fk')
    ->where('fk.kilometer',$stage)
    ->join('feev2_stops fs','fs.kilometer=fk.id')
    ->get()->row();
    return $result->route;
  }
  public function get_transport_mode_namebyValue($value){
    $setting = $this->settings->getSetting('transport_mode');
    foreach ($setting as  $val) {
      if ($val->value == $value) {
        return $val->name;
      }
    }
  }

  public function insert_pre_defined_concession($input){
    $data = array(
      'name' => $input['pre_defined_concession'] , 
      'amount' => $input['pre_concession_amount'], 
      'concession_mode' => $input['concession_mode'],
      'status'=>0,
      'created_by'=>$this->authorization->getAvatarId(),
      'created_on'=>$this->Kolkata_datetime(),
      'remarks'=>$input['pre_concession_remarks']
    );
    return $this->db->insert('feev2_concessiontype2_concession',$data);
  }

  public function get_pre_defined_concession_data(){
    return $this->db_readonly->get('feev2_concessiontype2_concession')->result();
  }

  public function status_active_pre_defined_concession($stngId,$value){
    $data = array(
      'status' => $value
    );
    $this->db->where('id',$stngId);
    return $this->db->update('feev2_concessiontype2_concession', $data);
  }

  public function get_transport_fee_amount_details(){
    $bpId = 1;
    $schQuery = $this->db->select("fcs.id, fcs.feev2_cohort_id, sd.id as stdId, concat(ifnull(sd.first_name,''),' ',ifnull(sd.last_name,'')) as student_name, concat(ifnull(cs.class_name,''),' ',ifnull(cs.section_name,'')) as class_name, fss.id as schId, fss.total_fee as sch_total_fee")
    ->from('feev2_cohort_student fcs')
    ->where('fcs.blueprint_id',$bpId)
    ->join('student_admission sd','fcs.student_id=sd.id')
    ->join('student_year sy','sd.id=sy.student_admission_id')
    ->join('class c','sy.class_id=c.id')
    ->join('class_section cs','sy.class_section_id=cs.id','left')
    ->where('sy.acad_year_id',$this->yearId)
    ->join('feev2_student_schedule fss','fcs.id=fss.feev2_cohort_student_id')
    ->get()->result();

    return $schQuery;


    // $cohortIds = [];
    // foreach ($schQuery as $key => $val) {
    //  array_push($cohortIds, $val->feev2_cohort_id);
    // }

    // $cohorts = "select fcic.*, fi.name as insName from feev2_cohort_installment_components fcic join feev2_installments fi on fcic.feev2_installment_id= fi.id where fcic.feev2_installment_id not in 
    //   (select feev2_installments_id from feev2_student_installments fsi join feev2_student_schedule fss on fsi.fee_student_schedule_id=fss.id join feev2_cohort_student fcs on fss.feev2_cohort_student_id= fcs.id
    //   where fcs.blueprint_id = $bpId) and feev2_cohort_id in ".$cohortIds." ";
    // $query = $this->db->query($cohorts)->result();

    // return array('sch'=>$schQuery,'cohorts'=>$query);

  }

  public function get_additional_mount_popup_details($stdId,$cohort_id,$bpId){
    $cohorts = "select fcic.*, fi.name as insName from feev2_cohort_installment_components fcic join feev2_installments fi on fcic.feev2_installment_id= fi.id where fcic.feev2_installment_id not in 
      (select feev2_installments_id from feev2_student_installments fsi join feev2_student_schedule fss on fsi.fee_student_schedule_id=fss.id join feev2_cohort_student fcs on fss.feev2_cohort_student_id= fcs.id
      where fcs.blueprint_id = $bpId and fcs.student_id = $stdId)";
    return $this->db->query($cohorts)->result();
  }

  public function get_additional_installment_details($blueprint_id,$student_id){

    $installments = "select fi.id as insId, fi.name, fbc.id as component_id, fbc.name as component_name
      from feev2_installments fi
      join feev2_installment_types fit on fi.feev2_installment_type_id = fit.id
      join feev2_blueprint_installment_types fbit on fbit.feev2_installment_type_id= fit.id
      join feev2_blueprint_components fbc on fbit.feev2_blueprint_id = fbc.feev2_blueprint_id
      where fi.id not in (select feev2_installments_id 
      from feev2_student_installments fsi 
      join  feev2_student_schedule fss on fsi.fee_student_schedule_id = fss.id 
      join feev2_cohort_student fcs on fss.feev2_cohort_student_id = fcs.id
      where fcs.student_id = $student_id) and fbc.feev2_blueprint_id = $blueprint_id order by fi.id, fbc.id";
    return $this->db->query($installments)->result();
  }

  public function get_additional_student_installment_details($blueprint_id,$student_id){
    return $this->db->select('fi.name as insName, fsi.id as studentInsId')
    ->from('feev2_cohort_student fcs')
    ->join('feev2_student_schedule fss','fcs.id = fss.feev2_cohort_student_id')
    ->join('feev2_student_installments fsi','fss.id=fsi.fee_student_schedule_id')
    ->join('feev2_installments fi','fsi.feev2_installments_id=fi.id')
    ->where('fcs.blueprint_id',$blueprint_id)
    ->where('fcs.student_id',$student_id)
    ->get()->result();
  }

   public function get_additional_installment_component_data_details($blueprint_id,$student_id, $stdInsId){

    $installments = "select fbc.id as component_id, fbc.name as component_name  from feev2_cohort_student fcs
        join feev2_blueprint_components fbc on fcs.blueprint_id = fbc.feev2_blueprint_id
        where fbc.id not in (select blueprint_component_id from feev2_student_installments_components fsc
        join feev2_student_installments fsi on fsc.fee_student_installment_id = fsi.id and fsi.id = $stdInsId)
        and fcs.blueprint_id = $blueprint_id and fcs.student_id = $student_id";
    return $this->db->query($installments)->result();
  }


  public function insert_additional_transport_installments_data(){
    $this->db->trans_start();
    $total_insAmount = array_sum($this->input->post('installment_amount'));
    $fstdSchd = array(
      'total_fee'=>$this->input->post('sch_total_fee') + $total_insAmount
    );
    $this->db->where('id', $this->input->post('fees_schId'));
    $this->db->update('feev2_student_schedule',$fstdSchd);
    
    $infsIns = array(
      'fee_student_schedule_id'=>$this->input->post('fees_schId'),
      'feev2_installments_id'=>$this->input->post('feev2_installment_id'),
      'installment_amount'=>$total_insAmount,
      'status'=>'NOT_STARTED'
    );
    $this->db->insert('feev2_student_installments',$infsIns);
    $feeinsId = $this->db->insert_id();
    foreach ($this->input->post('feev2_blueprint_component_id') as $key => $val) {
      $infsInsComp = array(
        'fee_student_installment_id'=> $feeinsId,
        'blueprint_component_id'=>$val,
        'component_amount'=>$this->input->post('installment_amount')[$key],
      );
      $this->db->insert('feev2_student_installments_components',$infsInsComp);
    }
    $this->db->trans_complete();
    return $this->db->trans_status();
  }

  public function insert_additional_installments_details(){

    $this->db->trans_start();
    
    foreach ($this->input->post('check_installment') as $insId => $val) {
      $infsIns = array(
        'fee_student_schedule_id'=>$this->input->post('fees_schId'),
        'feev2_installments_id'=>$insId,
        'installment_amount'=>0,
        'total_concession_amount'=>0,
        'status'=>'NOT_STARTED'
      );
      $this->db->insert('feev2_student_installments',$infsIns);
      $feeinsId = $this->db->insert_id();
      $infsInsComp = [];
      foreach ($val as $compId => $value) {
        $infsInsComp[] = array(
          'fee_student_installment_id'=> $feeinsId,
          'blueprint_component_id'=>$compId,
          'component_amount'=>0,
          'concession_amount'=>0,
          'adjustment_amount'=>0
        );
      }
      $this->db->insert_batch('feev2_student_installments_components',$infsInsComp);
    }
    $this->db->trans_complete();
    return $this->db->trans_status();
  }

  public function insert_additional_installments_component_details(){
    $this->db->trans_start();
    
    foreach ($this->input->post('check_installment_component') as $insId => $val) {
      $infsInsComp = [];
      foreach ($val as $compId => $value) {
        $infsInsComp[] = array(
          'fee_student_installment_id'=> $insId,
          'blueprint_component_id'=>$compId,
          'component_amount'=>0,
          'concession_amount'=>0,
          'adjustment_amount'=>0
        );
      }
      $this->db->insert_batch('feev2_student_installments_components',$infsInsComp);
    }
    $this->db->trans_complete();
    return $this->db->trans_status();
  }

  public function get_fee_blueprint_component_details($selectedBP){
    $insTypes = $this->db->select('fbit.id as feev2_blueprint_installment_types_id, fb.id as feev2_blueprint_id, fit.name as type_name, fit.id as feev2_installment_type_id, fi.id as feev2_installment_id, fi.name as installment_name ')
    ->from('feev2_blueprint fb')
    ->where('fb.id', $selectedBP)
    ->join('feev2_blueprint_installment_types fbit','fb.id=fbit.feev2_blueprint_id')
    ->join('feev2_installment_types fit','fbit.feev2_installment_type_id=fit.id')
    ->join('feev2_installments fi','fit.id=fi.feev2_installment_type_id')
    ->get()->result();

    $insComp = $this->db->select('fbc.id as feev2_blueprint_component_id, fbc.name as component_name')
    ->from('feev2_blueprint fb')
    ->where('fb.id', $selectedBP)
    ->join('feev2_blueprint_components fbc','fb.id=fbc.feev2_blueprint_id')
    ->get()->result();

    foreach ($insTypes as $key => &$val) {
        $val->comp = $insComp;
    }

    return $insTypes;
  }

   public function insert_custom_fee_structure() {

      $fbCompIds = $this->input->post('feev2_blueprint_components_id');
      $compAmount = $this->input->post('comp_amount');

      $this->db->trans_start();
      $feeMasterData = array (
        'filter' => 'none',
        'total_fee' => $this->input->post('total_fee'),
        'blueprint_id' => $this->input->post('blueprint_id'),
        'acad_year_id'  => $this->acad_year->getAcadYearId(),
        'default_ins'  => $this->input->post('installments_type'),
        'friendly_name'  => $this->input->post('friendly_name_custom'),
      );
      $this->db->insert('feev2_cohorts', $feeMasterData);
      $fee_cohor_id = $this->db->insert_id();

      $fInsCompData = [];
      foreach ($fbCompIds as $key => $insCompId) {
        $insCompidsExpo = explode('_', $insCompId);
        if ($compAmount[$key]!=0) {
          $fInsCompData[] = array(
            'feev2_cohort_id'=>$fee_cohor_id,
            'feev2_blueprint_installment_types_id'=>$insCompidsExpo[0],
            'feev2_installment_id'=>$insCompidsExpo[1],
            'feev2_blueprint_component_id'=>$insCompidsExpo[2],
            'amount'=>$compAmount[$key],
          );
        }
      }
      $this->db->insert_batch('feev2_cohort_installment_components', $fInsCompData);

      $result = $this->db->trans_complete();
      return $fee_cohor_id;
    }

   
    public function publshed_student_fee_structure_student_details_admission($cohort_student_id){
      $this->db->where('id',$cohort_student_id);
      return $this->db->update('feev2_cohort_student', array('publish_status'=>'PUBLISHED'));
    }
    
    public function online_payment_enabled_parent_cohorts_admission($cohort_student_id){
      $this->db->where('id',$cohort_student_id);
      return $this->db->update('feev2_cohort_student', array('online_payment'=>'PUBLISHED'));
    }

    public function get_admission_offer_byStudentId($student_id){
      $this->db_readonly->select("ao.*,if(ao.offer_amount = 0.00,aso.override_offer_amount,ao.offer_amount) as offer_amount, CONCAT(ifnull(sm.first_name,' '), ' ', ifnull(sm.last_name,' ')) as staffName,  date_format(aso.created_on,'%d-%m-%Y') as offerAppliedDate");
      $this->db_readonly->from('admission_student_offers aso');
      $this->db_readonly->join('admission_offers ao','aso.admission_offer_id=ao.id');
      $this->db_readonly->where('aso.student_admission_id',$student_id);
      $this->db_readonly->join('staff_master sm','sm.id=aso.created_by','left');
      $this->db_readonly->where('aso.status','Active');
      return $this->db_readonly->get()->result();
    }

    public function insert_invoice_template_details($path){
      $input = $this->input->post();
      $data = array(
        'number_series_id' => $input['number_series_id'],
        'invoice_note' => $input['invoice_remarks'],
        'invoice_title' => $input['invoice_title'],
        'invoice_signature' => $path['file_name'],
        'created_by' => $this->authorization->getAvatarStakeHolderId(),
        'created_on' => $this->Kolkata_datetime(),
        'friendly_name' => $input['friendly_name']
      );
      return $this->db->insert('feev2_invoice_template', $data);
    }
    public function get_invoice_template_by_id($id) {
      return $this->db_readonly->select('*')
      ->from('feev2_invoice_template')
      ->where('id',$id)
      ->get()->row();

    }
    public function update_invoice_template_details($path) {
      $input = $this->input->post();
      $data = array(
        'number_series_id' => $input['update_number_series'],
        'invoice_note' => $input['update_invoice_note_remarks'],
        'invoice_title' => $input['update_invoice_title'],
        'created_by' => $this->authorization->getAvatarStakeHolderId(),
        'created_on' => $this->Kolkata_datetime(),
        'friendly_name' => $input['update_friendly_name']
      );
      if (!empty($path['file_name'])) {
        $data['invoice_signature'] = $path['file_name'];
      }
      $this->db->where('id',$input['invoice_id']);
      return $this->db->update('feev2_invoice_template', $data);
    }
    public function update_invoice_html_format($id,$invoice_template) {
      $data =array(
        'template' => $invoice_template
      );
      $this->db->where('id',$id);
      return $this->db->update('feev2_invoice_template', $data);

    }

    public function get_all_invoices(){
      return $this->db->select('fit.*')
      ->from('feev2_invoice_template fit')
      ->get()->result();
    }

    public function get_invoice_template(){
      $result = $this->db->select('fit.id as invoice_template_id, number_series_id, invoice_note, invoice_title, invoice_signature, template')
      ->from('feev2_invoice_template fit')
      ->where('invoice_type','Invoice')
      ->get()->row();
      if (empty($result)) {
        return 0;
      }
      
      $sql = "select frb.* from feev2_receipt_book frb where frb.id=$result->number_series_id";
      $receipt_book = $this->db->query($sql)->row();

      $receipt_number = $this->fee_library->receipt_format_get_update($receipt_book);
      $result->invoice_number = $receipt_number;
      return $result;
    }

    public function get_statement_template(){
      $result = $this->db->select('fit.id as invoice_template_id, number_series_id, invoice_note, invoice_title, invoice_signature, template')
      ->from('feev2_invoice_template fit')
      ->where('invoice_type','Statement')
      ->get()->row();
      if (empty($result)) {
        return 0;
      }
      
      $sql = "select frb.* from feev2_receipt_book frb where frb.id=$result->number_series_id";
      $receipt_book = $this->db->query($sql)->row();

      $receipt_number = $this->fee_library->receipt_format_get_update($receipt_book);
      $result->invoice_number = $receipt_number;
      return $result;
    }

    public function insert_invoice_student_details($id, $path, $student_admission_id){
      
      $data = array(
        'feev2_invoice_template_id' => $id,
        'student_admission_id' => $student_admission_id,
        'created_by' => $this->authorization->getAvatarStakeHolderId(),
        'created_on' => $this->Kolkata_datetime(),
        'invoice_path' => $path,
        'pdf_status' => 0,
      );
      $this->db->insert('feev2_invoice_student', $data);

      $sql = "select frb.* from feev2_invoice_template fit left join feev2_receipt_book frb on fit.number_series_id=frb.id where fit.id=$id for update";
			$receipt_book = $this->db->query($sql)->row();
 			$this->db->where('id',$receipt_book->id);
      return $this->db->update('feev2_receipt_book', array('running_number'=>$receipt_book->running_number+1));
    }
    
    public function updateFeeInvoicePdfLink($path, $status) {
      $this->db->where('invoice_path',$path);
      return $this->db->update('feev2_invoice_student', array('pdf_status' => $status));
    }
    
    public function download_invoice_fee_receipt($std_id, $template_type){
      return $this->db->select('fis.invoice_path')
      ->from('feev2_invoice_student fis')
      ->join('feev2_invoice_template fit','fis.feev2_invoice_template_id=fit.id')
      ->where('student_admission_id',$std_id)
      ->where('fit.invoice_type',$template_type)
      ->order_by('fis.id','desc')
      ->get()->row();
    }

    public function get_invoices_pdf_status($std_id, $invoiceType){
      $result =  $this->db->select('fis.id as std_invoice_id, fis.invoice_path,  fis.pdf_status, email_master_id')
      ->from('feev2_invoice_student fis')
      ->join('feev2_invoice_template fit','fis.feev2_invoice_template_id=fit.id')
      ->where('student_admission_id',$std_id)
      ->where('fit.invoice_type',$invoiceType)
      ->order_by('fis.id','desc')
      ->get()->row();
      if (!empty($result)) {
       return $result;
      }else{
        return false;
      }
    }

    public function check_pdf_generated_invoice($std_id, $invoice_type){

      $templeate =  $this->db->select('id')
      ->from('feev2_invoice_template fit')
      ->where('invoice_type',$invoice_type)
      ->get()->row();

      $result =  $this->db->select('invoice_path, pdf_status')
      ->from('feev2_invoice_student')
      ->where('student_admission_id',$std_id)
      ->where('feev2_invoice_template_id',$templeate->id)
      ->order_by('id','desc')
      ->get()->row();
      if (!empty($result)) {
        if ($result->pdf_status == 1) {
            return $result->invoice_path;
        }else{
          return 0;
        }
      }else{
        return 0;
      }
    }

    public function getFeeInvoiceStudentDetailsbyid($student_id){
      $relationType = ['Father','Mother'];
      return  $this->db_readonly->select("s.id as std_id,CONCAT(ifnull(s.first_name,''),' ',ifnull(s.last_name,'')) as studentName, CONCAT(ifnull(p.first_name,''),' ',ifnull(p.last_name,'')) as parentName, p.mobile_no as mobile, p.email, sr.relation_type")
      ->from('student_admission s')
      ->where('s.id',$student_id)
      ->join('student_year sy', 'sy.student_admission_id=s.id')
      ->where('sy.acad_year_id',$this->yearId)
      ->join('student_relation sr','s.id=sr.std_id')
      ->join('parent p','sr.relation_id=p.id')
      ->where_in('sr.relation_type',$relationType)
      ->get()->result();
    }

    public function save_master_id_to_inovice($student_admission_id, $email_master_id, $stdInvoiceId){
      return $this->db->where('student_admission_id', $student_admission_id)->where('id', $stdInvoiceId)->update('feev2_invoice_student', ['email_master_id' => $email_master_id]);
    }

    public function get_excess_amount_by_std($std_id){
      return $this->db->select('sum(total_amount - total_used_amount - excess_refund_amount) as total_excess_amount')
      ->from('feev2_additional_amount')
      ->where('student_id',$std_id)
      ->group_by('student_id')
      ->order_by('id','desc')
      ->get()->row();
    }

    public function check_invoice_previous_balance_amount($stdId){
      return $this->db_readonly->select("fb.name as bpName, ifnull(fss.total_fee,0) - ifnull(fss.total_fee_paid,0) -  ifnull(fss.total_concession_amount,0)  - ifnull(fss.total_concession_amount_paid,0) - ifnull(fss.total_adjustment_amount,0) - ifnull(fss.total_adjustment_amount_paid,0) as balance")
      ->from('feev2_blueprint fb')
      ->where('fb.acad_year_id <',$this->yearId)
      ->join('feev2_cohort_student fcs','fb.id=fcs.blueprint_id')
      ->where('fcs.student_id',$stdId)
      ->join('feev2_student_schedule fss','fcs.id=fss.feev2_cohort_student_id')
      ->where('fss.payment_status!=','FULL')
      ->get()->result();
    }

    public function getStudents_for_email($members, $send_to){
      $this->db_readonly->select("p.id, s.id as std_id, 2 as avatar_type, CONCAT(cs.class_name,cs.section_name,' - ',ifnull(s.first_name,''), ' ', ifnull(s.last_name,''), ' (', sr.relation_type, ')') AS Name, p.mobile_no, u.id as user_id, u.token, if(u.token is null, 0, 1) as tokenState, p.email")
      ->from('student_admission s')
      ->join('student_year sy', 'sy.student_admission_id=s.id')
      ->join('class c','sy.class_id=c.id')
      ->join('class_section cs', 'cs.id=sy.class_section_id','left')
      ->join('parent p', 'p.student_id=s.id')
      ->join('student_relation sr', 'sr.relation_id=p.id')
      ->join('avatar a', 'a.stakeholder_id=p.id')
      ->join('users u', 'u.id=a.user_id');
      if($send_to != 'Both') {
        $this->db_readonly->where('sr.relation_type',$send_to);
      }
      $this->db_readonly->where('admission_status','2'); // Approved 2
      $this->db_readonly->where('sy.promotion_status!=', '4'); 
      $this->db_readonly->where('sy.promotion_status!=', '5'); 
      $this->db_readonly->where('a.avatar_type','2');
      $this->db_readonly->where('sy.acad_year_id', $this->yearId);
      $this->db_readonly->where_in('s.id', $members);
      $this->db_readonly->order_by('cs.class_name,cs.section_name,s.first_name', 'ASC');
      $students = $this->db_readonly->get()->result();
      return $students;
    }

    public function check_invoice_email_send($student_id){
      $result = $this->db->select('*')
      ->from('feev2_invoice_student')
      ->where('student_admission_id',$student_id)
      ->get()->row();
      if(!empty($result)){
        if(!empty($result->email_master_id)){
          return 1;
        }else{
          return 0;
        }
      }else{
        return 0;
      }
    }
    
    public function get_online_settlemet_details($from_date, $to_date, $fee_type, $paymentModes){
      $this->db_readonly->select("opm.order_id, GROUP_CONCAT(ft.receipt_number) as receipt_number, 
                           date_format(ft.paid_datetime, '%d-%b-%Y') as paid_datetime, 
                           SUM(ft.amount_paid) as amount_paid, 
                           opm.settlement_details_json, fb.acad_year_id, ft.student_id, opm.tx_id");
      $this->db_readonly->from('online_payment_master opm');
      $this->db_readonly->join('feev2_transaction ft', 'FIND_IN_SET(ft.id, REPLACE(REPLACE(opm.source_id, "[", ""), "]", ""))');
      // $this->db_readonly->join('feev2_transaction ft', 'ft.id=opm.source_id');
      if ($from_date && $to_date) {
        $fromDate = date('Y-m-d',strtotime($from_date));
        $toDate = date('Y-m-d',strtotime($to_date));
        $this->db_readonly->where('date_format(ft.paid_datetime,"%Y-%m-%d") BETWEEN "'.$fromDate. '" and "'.$toDate.'"');
      }
      $this->db_readonly->join('feev2_transaction_payment ftp','ft.id=ftp.fee_transaction_id');
      $this->db_readonly->join('feev2_student_schedule fss','ft.fee_student_schedule_id=fss.id');
      $this->db_readonly->join('feev2_cohort_student fcs','fss.feev2_cohort_student_id=fcs.id');
      $this->db_readonly->join('feev2_blueprint fb','fcs.blueprint_id=fb.id');
      $this->db_readonly->where('ft.status','SUCCESS');
      if($fee_type){
        $this->db_readonly->where_in('fb.id',$fee_type);
      }
      $this->db_readonly->where('ftp.payment_type',$paymentModes);
      $this->db_readonly->group_by('opm.order_id');
      $this->db_readonly->order_by('ft.paid_datetime','desc');
      $result = $this->db_readonly->get()->result();
      foreach ($result as $key => $val) {
        $settlemntJson = json_decode($val->settlement_details_json);
       
        $studentData = $this->db_readonly->select("concat(ifnull(sa.first_name,''),'',ifnull(sa.last_name,'')) as student_name, CONCAT(ifnull(c.class_name,''),' ',ifnull(cs.section_name,'')) as class_name, sa.admission_no,ifnull(sa.enrollment_number,'-') as enrollment_number")
        ->from('student_admission sa')
        ->where('sa.id',$val->student_id)
        ->join('student_year sy', "sa.id=sy.student_admission_id and sy.acad_year_id=$val->acad_year_id")
        ->join('class c', 'sy.class_id=c.id')
        ->join('class_section cs', 'sy.class_section_id=cs.id','left')
        ->get()->row();
        $val->student_name = $studentData->student_name;
        $val->class_name = $studentData->class_name;
        $val->admission_no = $studentData->admission_no;
        $val->enrollment_number = $studentData->enrollment_number;
        if (!empty($settlemntJson)) {
          foreach ($settlemntJson as $key => $splitJson) {
            $val->settlements = $splitJson->settlement_split;
          }
        }else{
          $val->settlements = array();
        }
      }
      return $result;
    }

    public function get_std_fee_for_statement($std_id) {

   
      $current_acad_year_fees =  $this->db->select("fbc.name as blueprint_name, sum(fsic.component_amount) as component_amount, fcs.id as cohort_student_id, fb.acad_year_id, fss.id as schId, expense_type_mapping")
        ->from('feev2_blueprint fb')
        ->join('feev2_cohort_student fcs','fcs.blueprint_id=fb.id')
        ->where('fcs.student_id',$std_id)
        ->where('fb.acad_year_id',$this->yearId)
        ->join('feev2_student_schedule fss','fcs.id=fss.feev2_cohort_student_id')
        ->join('feev2_student_installments fsi','fss.id=fsi.fee_student_schedule_id')
        ->join('feev2_student_installments_components fsic','fsic.fee_student_installment_id=fsi.id')
        ->join('feev2_blueprint_components fbc','fbc.id=fsic.blueprint_component_id')
        ->group_by('fbc.id')
        ->get()->result();
      $schIds = [];
      foreach ($current_acad_year_fees as $key => $value) {
        array_push($schIds, $value->schId);
      }
      if(empty($schIds)){
        return '';
      }
      $current_acad_year_conc =  $this->db->select("fcs.id as cohort_student_id,(case when fcpdc.feev2_predefined_name = 'Custom' then fcpdc.remarks else fcpdc.feev2_predefined_name end) as feev2_predefined_name, fcpdc.concession_amount, fb.acad_year_id")
      ->from('feev2_blueprint fb')
      ->where('fb.acad_year_id',$this->yearId)
      ->join('feev2_cohort_student fcs','fcs.blueprint_id=fb.id')
      ->join('feev2_concessiontype2_pre_defined_concession fcpdc','fcs.id=fcpdc.cohort_student_id')
      ->where('fcs.student_id',$std_id)
      ->get()->result();
      
      $transaction = $this->db->select('sum(ft.amount_paid - ifnull(ft.refund_amount,0) - ifnull(ft.discount_amount,0)) as amount_paid, sum(ifnull(ft.discount_amount,0)) as full_fee_discount')
      ->from('feev2_transaction ft')
      ->join('feev2_transaction_payment ftp','ft.id=ftp.fee_transaction_id')
      ->where('ft.soft_delete !=','1')
      // ->where('ftp.payment_type !=','999')
      // ->where('ft.paid_datetime <=','2023-06-30')
      // ->where('ft.acad_year_id',$this->yearId)
      ->where_in('ft.fee_student_schedule_id',$schIds)
      ->where('ft.status','SUCCESS')
      ->where('ft.student_id',$std_id)
      ->get()->row();

      $concessRemarks = [];
      foreach ($current_acad_year_conc as $concession) {
        $concessRemarks[$concession->cohort_student_id][] = $concession;
      }
      $lastOccurrences = [];
      foreach ($current_acad_year_fees as $index => $fees) {
        $lastOccurrences[$fees->cohort_student_id] = $index;
      }
      foreach ($current_acad_year_fees as $index => $fees) {
        if (array_key_exists($fees->cohort_student_id, $concessRemarks) && $lastOccurrences[$fees->cohort_student_id] == $index) {
          $fees->concession = $concessRemarks[$fees->cohort_student_id];
        } else {
          $fees->concession = [];
        }
      }
    
      // $cohortKey = 0;
      // foreach ($current_acad_year_fees as $key => $fees) {
      //   if (array_key_exists($fees->cohort_student_id, $concessRemarks)) { 
      //     $fees->concession = $concessRemarks[$fees->cohort_student_id];
      //     $cohortKey++;
      //   }else{
      //     $fees->concession = [];
      //   }
      // }
      $transaction_details = $this->db->select('ft.id as trans_id, ft.amount_paid')
      ->from('feev2_transaction ft')
      ->join('feev2_transaction_payment ftp','ft.id=ftp.fee_transaction_id')
      ->where('ft.soft_delete !=','1')
      ->where('ft.status','SUCCESS')
      ->where('ftp.payment_type !=','999')
      // ->where('ft.acad_year_id',$this->yearId)
      // ->where('ft.paid_datetime <=','2023-06-30')
      ->where('ft.acad_year_id',$this->yearId)
      ->where('ft.student_id',$std_id)
      ->get()->result();
      $transArry = [];
      foreach ($transaction_details as $key => $val) {
        $transArry[$val->trans_id] = $val->amount_paid;
      }

      $AdjustAdditionExcessRemarks = $this->db->select('fam.remarks, 
        (fam.total_amount - fam.total_used_amount - fam.excess_refund_amount) as total_amount, faau.used_fee_trans_id,fam.id')
      ->from('feev2_additional_amount fam')
      ->join('feev2_additional_amount_usage faau','fam.id=faau.fee_addt_amount_id','left')
      ->where('fam.student_id',$std_id)
      ->where('fam.acad_year_id',$this->yearId)
      ->get()->result();
      $tmepArry = [];
      foreach ($AdjustAdditionExcessRemarks as $key => $val) {
        if(!array_key_exists($val->id, $tmepArry)){
          $tmepArry[$val->id]['used_fee_trans_id'] = [];
          $tmepArry[$val->id]['total_amount'] = 0;
          $tmepArry[$val->id]['remarks'] ='';
        }
        $tmepArry[$val->id]['used_fee_trans_id'][] = $val->used_fee_trans_id;
        $tmepArry[$val->id]['total_amount'] = $val->total_amount;
        $tmepArry[$val->id]['remarks'] = $val->remarks;
      }
      

      // foreach ($AdjustAdditionExcessRemarks as $key => $val) {
      //   $val->total_amount = $val->total_amount;
      //   if(array_key_exists($val->used_fee_trans_id, $transArry)){
      //     $val->total_amount = $val->total_amount - $transArry[$val->used_fee_trans_id];
      //   }
      // }
    

      foreach ($tmepArry as $excess_id => $value) {
        $resultArray[$excess_id] = 0;
        foreach ($value['used_fee_trans_id'] as $key => $val) {
          if (array_key_exists($val, $transArry)) {
            $resultArray[$excess_id] += $transArry[$val];
          }
        }
      }

      foreach ($tmepArry as $id => $value) {
          if(array_key_exists($id, $resultArray)){
            $tmepArry[$id]['total_amount'] = $value['total_amount'] - $resultArray[$id];
          }
      }

      $excess_amount_current = [];
      foreach ($tmepArry as $key => $value) {
        $obj = new stdClass();
        $obj->remarks = $value['remarks'];
        $obj->total_amount = $value['total_amount'];
        array_push($excess_amount_current, $obj);
      }
      
      $AdjustAdditionNextYear = $this->db->select('fam.total_amount')
      ->from('feev2_additional_amount fam')
      ->where('fam.student_id',$std_id)
      ->where('fam.acad_year_id >',$this->yearId)
      ->get()->row();

      $next_acad_year_data =  $this->db->select("fcs.id as cohort_student_id, fcs.id as feev2_cohort_student_id, fcs.fee_collect_status, fcs.fee_cohort_status, fss.id as std_sch_id, fb.acad_year_id, fbc.name as blueprint_name, sum(fsic.component_amount) as component_amount, sum(fsic.concession_amount) as concession_amount, sum(ifnull(component_amount_paid,0)) as component_paid, fss.discount")
      ->from('feev2_blueprint fb')
      ->join('feev2_cohort_student fcs','fcs.blueprint_id=fb.id')
      ->where('fcs.student_id',$std_id)
      ->where('fb.acad_year_id >',$this->yearId)
      ->join('feev2_student_schedule fss','fcs.id=fss.feev2_cohort_student_id')
      ->join('feev2_student_installments fsi','fss.id=fsi.fee_student_schedule_id')
      ->join('feev2_student_installments_components fsic','fsic.fee_student_installment_id=fsi.id')
      ->join('feev2_blueprint_components fbc','fbc.id=fsic.blueprint_component_id')
      ->group_by('fbc.id')
      ->get()->result();

      $transaction_next_year = $this->db->select('sum(ft.amount_paid - ifnull(ft.refund_amount,0) - ifnull(ft.discount_amount,0)) as amount_paid_next_year, sum(ifnull(ft.discount_amount,0)) as full_fee_discount')
      ->from('feev2_transaction ft')
      ->join('feev2_transaction_payment ftp','ft.id=ftp.fee_transaction_id')
      ->where('ftp.payment_type !=','999')
      ->where('ft.soft_delete !=','1')
      ->where('ft.status','SUCCESS')
      ->where('ft.acad_year_id >',$this->yearId)
      ->where('ft.student_id',$std_id)
      ->get()->row();
      
      $next_acad_year_conc =  $this->db->select("fcs.id as cohort_student_id,(case when fcpdc.feev2_predefined_name = 'Custom' then fcpdc.remarks else fcpdc.feev2_predefined_name end) as feev2_predefined_name, fcpdc.concession_amount, fb.acad_year_id")
      ->from('feev2_blueprint fb')
      ->where('fb.acad_year_id >',$this->yearId)
      ->join('feev2_cohort_student fcs','fcs.blueprint_id=fb.id')
      ->join('feev2_concessiontype2_pre_defined_concession fcpdc','fcs.id=fcpdc.cohort_student_id')
      ->where('fcs.student_id',$std_id)
      ->get()->result();

      return array('current_acad_year_data'=>$current_acad_year_fees,'transaction'=>$transaction,'current_acad_year_refund'=>$excess_amount_current,'next_acad_year_data'=>$next_acad_year_data,'next_acad_year_conc'=>$next_acad_year_conc,'transaction_next_year'=>$transaction_next_year,'next_year_excess_amount'=>$AdjustAdditionNextYear);
        // $statementArry = [];
        // foreach ($result as $key => $val) {
        //   $statementArry[$val->acad_year_id][] = $val;
        // }
        // return $statementArry;
    }
    
    public function check_statment_previous_balance_amount($stdId){
      return $this->db_readonly->select("fb.name as bpName, ifnull(fss.total_fee,0) - ifnull(fss.total_fee_paid,0) -  ifnull(fss.total_concession_amount,0)  - ifnull(fss.total_concession_amount_paid,0) - ifnull(fss.total_adjustment_amount,0) - ifnull(fss.total_adjustment_amount_paid,0) as balance")
      ->from('feev2_blueprint fb')
      ->join('feev2_cohort_student fcs','fb.id=fcs.blueprint_id')
      ->where('fcs.student_id',$stdId)
      ->join('feev2_student_schedule fss','fcs.id=fss.feev2_cohort_student_id')
      ->where('fss.payment_status!=','FULL')
      ->group_by('fb.id')
      ->get()->result();
    }

    public function create_fee_statement_template($statement_details, $student_data, $invoiceTemplate, $excess_amount, $student_Next_Yeardata, $previous_balance, $previous_year_excess_amount){
      $total_excess_amount = 0;
     
      if(!empty($excess_amount) && $excess_amount->total_excess_amount !='0.00'){
        $nextYearExcess = 0;
        if(!empty($statement_details['next_year_excess_amount'])){
          $nextYearExcess = $statement_details['next_year_excess_amount']->total_amount;
        }
        $label = '-';
        $total_excess_amount = $excess_amount->total_excess_amount -  $nextYearExcess;
      }
      $template = $invoiceTemplate->template;
      $boarding = '';
      $boarding = $this->settings->getSetting('boarding')[$student_data->boarding];
      $boardingNextYear = '';
      if (!empty($student_Next_Yeardata)) {
        $boardingNextYear = $this->settings->getSetting('boarding')[$student_Next_Yeardata->boarding];
        $template = str_replace('%%student_class_section_next_year%%',$student_Next_Yeardata->className, $template);
      }
      
      $template = str_replace('%%student_board_type%%',$boarding, $template);
      $template = str_replace('%%invoice_number%%',$invoiceTemplate->invoice_number, $template);
      $template = str_replace('%%invoice_date%%',date('d-m-Y'), $template);
      $template = str_replace('%%student_class_section%%',$student_data->className, $template);
      $template = str_replace('%%class_section_name%%',$student_data->class_section_name, $template);
      $template = str_replace('%%admission_no%%',$student_data->admission_no, $template);
      $template = str_replace('%%student_board_type_next_year%%',$boardingNextYear, $template);
      $template = str_replace('%%father_name%%',$student_data->father_name, $template);
      $template = str_replace('%%mother_name%%',$student_data->mother_name, $template);

      $template = str_replace('%%student_name%%',$student_data->stdName, $template);
      $template = str_replace('%%current_academic_year%%', $this->acad_year->getAcadYear(), $template);
      $invoice_part = '<table class="table table-bordered" style="border: 2px solid #000;">';
      $invoice_part .= '<tr>';
      $invoice_part .= '<th style="background: #fff;color: #000;border: 2px solid;">Particulars</th>';
      $invoice_part .= '<th style="background: #fff;color: #000;border: 2px solid;">Amount in Rs.</th>';
      $invoice_part .= '</tr>';
      
      $totalpayableFee = 0;
      $totalpayableFeeOthers = 0;
      $totalReceivedFee = (!empty($statement_details['transaction'])) ? $statement_details['transaction']->amount_paid : 0;
      $fullFeeDiscountFee = (!empty($statement_details['transaction'])) ? $statement_details['transaction']->full_fee_discount : 0;
      $preConcession = 0;
      $totalpayableFee_nextYear = 0;
      $totalReceivedFee_nextYear = 0;

      $totalReceivedFee_nextYear = (!empty($statement_details['transaction_next_year'])) ? $statement_details['transaction_next_year']->amount_paid_next_year : 0;

      $totalexcess_nextYear = (!empty($statement_details['next_year_excess_amount'])) ? $statement_details['next_year_excess_amount']->total_amount : 0;

      
      foreach ($statement_details['current_acad_year_data'] as $acadYearId => $val) {
        if(empty($val->expense_type_mapping) && $val->blueprint_name != 'Other Expenses' || $val->expense_type_mapping =='pocket_money' || $val->expense_type_mapping =='infirmary'){
          $acadyear = $this->acad_year->getAcadYearById($val->acad_year_id);
          $totalpayableFee += $val->component_amount;
          $invoice_part .= '<tr>';
          $invoice_part .= '<th style="background: #fff;color: #000;border: 2px solid; text-align:left ">'.$val->blueprint_name.'</th>';
          $invoice_part .= '<th style="background: #fff;color: #000;border: 2px solid; text-align:right">'.numberToCurrency_withoutSymbol($val->component_amount).'</th>';
          $invoice_part .= '</tr>';

          foreach ($val->concession as $key => $con) {
            $preConcession += $con->concession_amount;
            $predefinedName = explode('_',$con->feev2_predefined_name);
            $invoice_part .= '<tr>';
            $invoice_part .= '<th style="background: #fff;color: #000;border: 2px solid; text-align:left ">  Less: '.$predefinedName[0].'( Discount ) </th>';
            $invoice_part .= '<th style="background: #fff;color: #000;border: 2px solid; text-align:right"> ('.numberToCurrency_withoutSymbol($con->concession_amount).')</th>';
            $invoice_part .= '</tr>';
          }
          
        }
      }
      if($fullFeeDiscountFee !=0){
        $invoice_part .= '<tr>';
        $invoice_part .= '<th style="background: #fff;color: #000;border: 2px solid; text-align:left ">  Less: Full Fee ( Discount ) </th>';
        $invoice_part .= '<th style="background: #fff;color: #000;border: 2px solid; text-align:right"> ('.numberToCurrency_withoutSymbol($fullFeeDiscountFee).')</th>';
        $invoice_part .= '</tr>';
      }
      $openingBalance = 0;
      $openingBalanceRecived = 0;
      if(!empty($previous_balance)){
        foreach ($previous_balance as $key => $val) {
          $openingBalance += $val->balance;
          if($val->balance !=0){
            if(!empty($val->trans_amount)){
              $openingBalanceRecived += $val->trans_amount;
            }
          }
        }
      }
      $excessAmount = 0;
      foreach ($statement_details['current_acad_year_refund'] as $acadYearId => $val) {  
        // $excessAmount += $val->total_amount;
      }
      $openingBalance = round($openingBalance  - $previous_year_excess_amount->total_ob_excess_bal + $previous_year_excess_amount->total_ob_excess_return);
      $opening_Balance = 0;
      if($openingBalance > 0){
        $opening_Balance = '<strong>'.numberToCurrency_withoutSymbol($openingBalance).'</strong>';
        // $totalReceivedFee = $totalReceivedFee + $openingBalance;
      }else{
        $opening_Balance = '<strong>('.numberToCurrency_withoutSymbol(abs($openingBalance)).')</strong>';
        // $totalReceivedFee = $totalReceivedFee;

        // if($openingBalance > 0){
        //   $totalReceivedFee = $totalReceivedFee + $openingBalance;
        // }else{
        //   $totalReceivedFee = $totalReceivedFee;
        // }
      }
      if ($openingBalance !=0) { 
        $invoice_part .='<tr>';
        $invoice_part .='<th style="background: #fff;color: #000;border: 2px solid; text-align:left"><strong>Opening Balance </strong></th>';
        $invoice_part .='<th style="background: #fff;color: #000;border: 2px solid; text-align:right">'.$opening_Balance.'</th>';
        $invoice_part .='</tr>';
      }
      $totalpayableFeeCal = $totalpayableFee + $openingBalance - $fullFeeDiscountFee - $preConcession;

      $totalReceivedFeeCal = $totalReceivedFee + $excessAmount + $openingBalanceRecived  - $previous_year_excess_amount->total_ob_excess_recived + $previous_year_excess_amount->total_ob_excess_return;

      $invoice_part .='<tr>';
      // $invoice_part .='<th style="background: #fff;color: #000;border: 2px solid; text-align:left"><strong>Total Amount Payable for '.$acadyear.'</strong></th>';
      $invoice_part .='<th style="background: #fff;color: #000;border: 2px solid; text-align:left"><strong>Total Amount Payable '.$this->acad_year->getAcadYearById($this->yearId).'</strong></th>';
      $invoice_part .='<th style="background: #fff;color: #000;border: 2px solid; text-align:right"><strong>'.numberToCurrency_withoutSymbol($totalpayableFeeCal).'</th>';
      $invoice_part .='</tr>';
      $invoice_part .='<tr>';
      // $invoice_part .='<th style="background: #fff;color: #000;border: 2px solid; text-align:left"><strong> Less: Amount Received for '.$acadyear.'</strong></th>';
      $invoice_part .='<th style="background: #fff;color: #000;border: 2px solid; text-align:left"><strong> Less: Amount Received</strong></th>';
      $invoice_part .='<th style="background: #fff;color: #000;border: 2px solid; text-align:right"><strong> ('.numberToCurrency_withoutSymbol($totalReceivedFeeCal).')</th>';
      $invoice_part .='</tr>';
      $preConcessionOthers = 0;
      foreach ($statement_details['current_acad_year_data'] as $acadYearId => $val) {
        if($val->blueprint_name == 'Other Expenses'){
          $acadyear = $this->acad_year->getAcadYearById($val->acad_year_id);
          $totalpayableFeeOthers += $val->component_amount;
          $invoice_part .= '<tr>';
          $invoice_part .= '<th style="background: #fff;color: #000;border: 2px solid; text-align:left ">'.$val->blueprint_name.'</th>';
          $invoice_part .= '<th style="background: #fff;color: #000;border: 2px solid; text-align:right">'.numberToCurrency_withoutSymbol($val->component_amount).'</th>';
          $invoice_part .= '</tr>';

          foreach ($val->concession as $key => $con) {
            $preConcessionOthers += $con->concession_amount;
            $predefinedName = explode('_',$con->feev2_predefined_name);
            $invoice_part .= '<tr>';
            $invoice_part .= '<th style="background: #fff;color: #000;border: 2px solid; text-align:left ">  Less: '.$predefinedName[0].'( Discount ) </th>';
            $invoice_part .= '<th style="background: #fff;color: #000;border: 2px solid; text-align:right"> ('.numberToCurrency_withoutSymbol($con->concession_amount).')</th>';
            $invoice_part .= '</tr>';
          }
        }
      }

      foreach ($statement_details['current_acad_year_data'] as $acadYearId => $val) {
        if (!empty($val->expense_type_mapping) && $val->expense_type_mapping != 'pocket_money' && $val->expense_type_mapping != 'infirmary') {
        // if(!empty($val->expense_type_mapping) || $val->expense_type_mapping != 'pocket_money' || $val->expense_type_mapping !='infirmary'){

          $acadyear = $this->acad_year->getAcadYearById($val->acad_year_id);
          $totalpayableFeeOthers += $val->component_amount;
          if(!empty($val->concession)){
            foreach ($val->concession as $key => $con) {
              $preConcessionOthers += $con->concession_amount;
              $invoice_part .= '<tr>';
              $invoice_part .= '<th style="background: #fff;color: #000;border: 2px solid; position: relative;text-align: left;">'.$val->blueprint_name.'<span style="position: absolute;right: 22px;">| (' .numberToCurrency_withoutSymbol($con->concession_amount).') | '.numberToCurrency_withoutSymbol($val->component_amount). '</span></th>';
              $invoice_part .= '<th style="background: #fff;color: #000;border: 2px solid; text-align:right">'.numberToCurrency_withoutSymbol($val->component_amount- $con->concession_amount).'</th>';
              $invoice_part .= '</tr>';
  
            }
          }else{
            $preConcessionOthers += 0;
            $invoice_part .= '<tr>';
            $invoice_part .= '<th style="background: #fff;color: #000;border: 2px solid; position: relative;text-align: left;">'.$val->blueprint_name.'<span style="position: absolute;right: 22px;">| (' .numberToCurrency_withoutSymbol(0).') | '.numberToCurrency_withoutSymbol($val->component_amount). '</span></th>';
            $invoice_part .= '<th style="background: #fff;color: #000;border: 2px solid; text-align:right">'.numberToCurrency_withoutSymbol($val->component_amount- 0).'</th>';
            $invoice_part .= '</tr>';
          }
         
          // foreach ($val->concession as $key => $con) {
            // $preConcession += $con->concession_amount;
            // $predefinedName = explode('_',$con->feev2_predefined_name);
            // $invoice_part .= '<tr>';
            // $invoice_part .= '<th style="background: #fff;color: #000;border: 2px solid; text-align:left ">  Less: '.$predefinedName[0].'( Discount ) </th>';
            // $invoice_part .= '<th style="background: #fff;color: #000;border: 2px solid; text-align:right"> ('.numberToCurrency_withoutSymbol($con->concession_amount).')</th>';
            // $invoice_part .= '</tr>';
          // }
        }
      }
      
      foreach ($statement_details['current_acad_year_refund'] as $acadYearId => $val) {  
        $excessAmount += $val->total_amount;
        if($val->total_amount != 0){
          $invoice_part .= '<tr>';
          $invoice_part .= '<th  style="background: #fff;color: #000;border: 2px solid; text-align:left "> Less: '.$val->remarks.'</th>';
          $invoice_part .='<th style="background: #fff;color: #000;border: 2px solid; text-align:right"><strong> ('.numberToCurrency_withoutSymbol(round($val->total_amount,2)).')</th>';
          $invoice_part .= '</tr>';
        }
      }

      // foreach ($statement_details['current_acad_year_data'] as $acadYearId => $val) {
      //   if($val->enable_remarks ==1){
      //     $acadyear = $this->acad_year->getAcadYearById($val->acad_year_id);
      //     $totalpayableFee += $val->component_amount;
      //     $totalReceivedFee += $val->component_paid;

      //     // $acadyear = $this->acad_year->getAcadYearById($val->acad_year_id);
      //     // $totalpayableFee += $val->component_amount;
      //     // $totalReceivedFee += $val->component_paid;
      //     $invoice_part .= '<tr>';
      //     $invoice_part .= '<th style="background: #fff;color: #000;border: 2px solid; text-align:left ">'.$val->remarks.'</th>';
      //     $invoice_part .= '<th style="background: #fff;color: #000;border: 2px solid; text-align:right">'.numberToCurrency_withoutSymbol($val->component_amount).'</th>';
      //     $invoice_part .= '</tr>';
      //   }
      // }
      // excees return amount added
      // $CurrentTotalExcessAmount = ($total_excess_amount + $previous_year_excess_amount->total_ob_excess_recived)  - ($previous_year_excess_amount->total_ob_excess_bal - $previous_year_excess_amount->total_ob_excess_return) ;

        // excees return amount removed
      $CurrentTotalExcessAmount = ($total_excess_amount + $previous_year_excess_amount->total_ob_excess_recived)  - ($previous_year_excess_amount->total_ob_excess_bal) ;
      $balanceFeeDue = round(($totalpayableFeeCal  + $totalpayableFeeOthers) - ($totalReceivedFeeCal  + $preConcessionOthers + $CurrentTotalExcessAmount),2);
      $feeBalanceAdd = 0;
      $feeBalanceLess = 0;
      $feeBalance = 0;
      if($balanceFeeDue > 0){
        $feeBalance = '<strong>'.numberToCurrency_withoutSymbol($balanceFeeDue).'</strong>';
      }else{
        $feeBalance = '<strong>('.numberToCurrency_withoutSymbol(abs($balanceFeeDue)).')</strong>';
      }
      // echo "<pre>"; print_r($feeBalance); die();

      // echo "<pre>"; print_r($feeBalance);die();
      // if($feeBalance != 0){
        $invoice_part .='<tr>';
        $invoice_part .='<th style="background: #fff;color: #000;border: 2px solid; text-align:left"><strong> Balance fee due amount for '.$acadyear.'</strong></th>';
        $invoice_part .='<th style="background: #fff;color: #000;border: 2px solid; text-align:right"><strong>'.$feeBalance.'</strong></th>';
        $invoice_part .='</tr>';
      // }
      

     

      $fullFeeDiscount = 0;
      foreach ($statement_details['next_acad_year_data'] as $acadYearId => $nextyear) {      
        $acadyearNextYear = $this->acad_year->getAcadYearById($nextyear->acad_year_id);
        $totalpayableFee_nextYear += $nextyear->component_amount;
    
      }
      $fullFeeDiscount = (!empty($statement_details['transaction_next_year'])) ? $statement_details['transaction_next_year']->full_fee_discount : 0;
      $preConcession_NextYear = 0;
      foreach ($statement_details['next_acad_year_conc'] as $acadYearId => $con) {
        $preConcession_NextYear += $con->concession_amount;       
      }
      if (!empty($acadyearNextYear)) {
        $invoice_part .='<tr>';
        $invoice_part .='<th style="background: #fff;color: #000;border: 2px solid; text-align:left"><strong> Fee for the AY '.$acadyearNextYear.'</strong></th>';
        $invoice_part .='<th style="background: #fff;color: #000;border: 2px solid; text-align:right"><strong>'.numberToCurrency_withoutSymbol(($totalpayableFee_nextYear - $preConcession_NextYear - $fullFeeDiscount)).'</th>';
        $invoice_part .='</tr>';
        $invoice_part .='<tr>';
        $invoice_part .='<th style="background: #fff;color: #000;border: 2px solid; text-align:left"><strong> Less: Fee received for the AY '.$acadyearNextYear.'</strong></th>';
        $invoice_part .='<th style="background: #fff;color: #000;border: 2px solid; text-align:right"><strong> ('.numberToCurrency_withoutSymbol(($totalReceivedFee_nextYear + $totalexcess_nextYear)).')</th>';
        $invoice_part .='</tr>';
      }

      $totalPaybleFeeNextYear = $totalpayableFee_nextYear - $preConcession_NextYear - $fullFeeDiscount;
      $totalReceivedFeeNextYear = $totalReceivedFee_nextYear + $totalexcess_nextYear;
      $nextyearTotal = $totalPaybleFeeNextYear - $totalReceivedFeeNextYear;
      
      $TotalfeeBalance = 0;
      if($balanceFeeDue > 0){
        $TotalfeeBalance = round($totalpayableFee_nextYear + $balanceFeeDue - $totalReceivedFee_nextYear - $preConcession_NextYear - $fullFeeDiscount - $totalexcess_nextYear,2);
      }else{
        $TotalfeeBalance = round($nextyearTotal + $balanceFeeDue,2);
      }
      if($TotalfeeBalance > 0){
        $feeBalanceTotalyear = '<strong>'.numberToCurrency_withoutSymbol($TotalfeeBalance).'</strong>';
      }else{
        $feeBalanceTotalyear = '<strong>('.numberToCurrency_withoutSymbol(abs($TotalfeeBalance)).')</strong>';
      }
      if (!empty($acadyearNextYear)) {
        $invoice_part .='<tr>';
        $invoice_part .='<th style="background: #fff;color: #000;border: 2px solid; text-align:left"><strong>Balance amount Payable for the year '.$acadyearNextYear.'</strong></th>';
        $invoice_part .='<th style="background: #fff;color: #000;border: 2px solid; text-align:right">'.$feeBalanceTotalyear.'</th>';
        $invoice_part .='</tr>';
      }
      $invoice_part .='</table>';

      $invoice_partNextYear = '<table class="table table-bordered" style="border: 2px solid #000;">';
      if (!empty($acadyearNextYear)) {
      $invoice_partNextYear .= '<tr>';
      $invoice_partNextYear .= '<th colspan="2" style="background: #fff;color: #000;border: 2px solid;">Details of Fee for the year '.$acadyearNextYear.'</th>';
      if (!empty($student_Next_Yeardata)) {
         $invoice_partNextYear .= '<th style="background: #fff;color: #000;border: 2px solid;">Grade : '.$student_Next_Yeardata->className.'</th>';
      }
    
      $invoice_partNextYear .= '<th style="background: #fff;color: #000;border: 2px solid;"> Status : '.$boardingNextYear.'</th>';
      $invoice_partNextYear .= '</tr>';
      }
      $totalpayableFee_NextYear = 0;
      $totalReceivedFee_NextYear = 0;
      
      foreach ($statement_details['next_acad_year_data'] as $acadYearId => $val) {
          $acadyear = $this->acad_year->getAcadYearById($val->acad_year_id);
          $totalpayableFee_NextYear += $val->component_amount;
          $totalReceivedFee_NextYear += $val->component_paid;
          
          $invoice_partNextYear .= '<tr>';
          $invoice_partNextYear .= '<th style="background: #fff;color: #000;border: 2px solid; text-align:left ">'.$val->blueprint_name.'</th>';
          $invoice_partNextYear .= '<th style="background: #fff;color: #000;border: 2px solid; text-align:right">'.numberToCurrency_withoutSymbol($val->component_amount).'</th>';
          $invoice_partNextYear .= '<th style="background: #fff;color: #000;border: 2px solid; text-align:right"></th>';
          $invoice_partNextYear .= '<th style="background: #fff;color: #000;border: 2px solid; text-align:right"></th>';
          $invoice_partNextYear .= '</tr>';
      }
      foreach ($statement_details['next_acad_year_conc'] as $acadYearId => $con) {
        $preConcession += $con->concession_amount;  
        $predefinedName = explode('_',$con->feev2_predefined_name);      
        $invoice_partNextYear .= '<tr>';
        $invoice_partNextYear .= '<th style="background: #fff;color: #000;border: 2px solid; text-align:left "> Less:'.$predefinedName[0].'( Discount ) </th>';
        $invoice_partNextYear .= '<th style="background: #fff;color: #000;border: 2px solid; text-align:right"> ('.numberToCurrency_withoutSymbol($con->concession_amount).')</th>';
        $invoice_partNextYear .= '<th style="background: #fff;color: #000;border: 2px solid; text-align:right"></th>';
        $invoice_partNextYear .= '<th style="background: #fff;color: #000;border: 2px solid; text-align:right"></th>';
        $invoice_partNextYear .= '</tr>';
      }
      if ($fullFeeDiscount !=0) {
        $invoice_partNextYear .= '<tr>';
        $invoice_partNextYear .= '<th style="background: #fff;color: #000;border: 2px solid; text-align:left "> Less: Full Fees ( Discount ) </th>';
        $invoice_partNextYear .= '<th style="background: #fff;color: #000;border: 2px solid; text-align:right"> '.numberToCurrency_withoutSymbol($fullFeeDiscount).'</th>';
        $invoice_partNextYear .= '<th style="background: #fff;color: #000;border: 2px solid; text-align:right"></th>';
        $invoice_partNextYear .= '<th style="background: #fff;color: #000;border: 2px solid; text-align:right"></th>';
        $invoice_partNextYear .= '</tr>';
      }
     

      $invoice_partNextYear .= '<tr>';
      $invoice_partNextYear .= '<th style="background: #fff;color: #000;border: 2px solid;text-align:left">Total</th>';
      $invoice_partNextYear .= '<th style="background: #fff;color: #000;border: 2px solid; text-align:right">'.numberToCurrency_withoutSymbol($totalpayableFee_NextYear- $preConcession_NextYear - $fullFeeDiscount).'</th>';
      $invoice_partNextYear .= '<th style="background: #fff;color: #000;border: 2px solid;"></th>';
      $invoice_partNextYear .= '<th style="background: #fff;color: #000;border: 2px solid;"></th>';
      $invoice_partNextYear .= '</tr>';

      $template = str_replace('%%invoice_data%%', $invoice_part, $template);
      $template = str_replace('%%invoice_partNextYear%%', $invoice_partNextYear, $template);
      $signature = '';
      if ($invoiceTemplate->invoice_signature !='') {
        $getURL = $this->filemanager->getFilePath($invoiceTemplate->invoice_signature);
        $signature = '<img style="width:100px !important; height:60px!important" src="'.$getURL.'" />';
      }
      $template = str_replace('%%invoice_signature%%', $signature, $template);
      $template = str_replace('%%invoice_note%%', $invoiceTemplate->invoice_note, $template);
      return $template;
    }

    public function chort_student_remarks_update($cohort_student_id,$cohort_student_remarks){
      $this->db->where('id',$cohort_student_id);
      return $this->db->update('feev2_cohort_student', array('remarks'=>$cohort_student_remarks));
    }


    public function get_std_fee_for_statement_iish($std_id) {

      $current_acad_year_data =  $this->db->select("fcs.id as cohort_student_id, fcs.id as feev2_cohort_student_id, fcs.fee_collect_status, fcs.fee_cohort_status, fss.id as std_sch_id, fb.acad_year_id,fb.name as blueprint_name, fbc.name as component_name, sum(fsic.component_amount) as component_amount, sum(fsic.concession_amount) as concession_amount, sum(ifnull(component_amount_paid,0)) as component_paid, fcs.remarks, fb.enable_remarks, fss.discount")
        ->from('feev2_blueprint fb')
        ->join('feev2_cohort_student fcs','fcs.blueprint_id=fb.id')
        ->where('fcs.student_id',$std_id)
        ->where('fb.acad_year_id',$this->yearId)
        ->join('feev2_student_schedule fss','fcs.id=fss.feev2_cohort_student_id')
        ->join('feev2_student_installments fsi','fss.id=fsi.fee_student_schedule_id')
        ->join('feev2_student_installments_components fsic','fsic.fee_student_installment_id=fsi.id')
        ->join('feev2_blueprint_components fbc','fbc.id=fsic.blueprint_component_id')
        ->group_by('fbc.id')
        ->get()->result();
       
      $bpAmount = [];
      foreach ($current_acad_year_data as $key => $val) {
        $bpAmount[$val->blueprint_name][$val->component_name] = $val;
      }

      $current_acad_year_conc =  $this->db->select("fcs.id as cohort_student_id,(case when fcpdc.feev2_predefined_name = 'Custom' then fcpdc.remarks else fcpdc.feev2_predefined_name end) as feev2_predefined_name, fcpdc.concession_amount, fb.acad_year_id")
      ->from('feev2_blueprint fb')
      ->where('fb.acad_year_id',$this->yearId)
      ->join('feev2_cohort_student fcs','fcs.blueprint_id=fb.id')
      ->join('feev2_concessiontype2_pre_defined_concession fcpdc','fcs.id=fcpdc.cohort_student_id')
      ->where('fcs.student_id',$std_id)
      ->get()->result();

      $AdjustAdditionExcessRemarks = $this->db->select('remarks, (total_amount - total_used_amount - excess_refund_amount) as total_amount')
      ->from('feev2_additional_amount')
      ->where('student_id',$std_id)
      // ->group_by('student_id')
      // ->order_by('id','desc')
      ->get()->result();

      return array('current_acad_year_data'=>$bpAmount,'current_acad_year_concession'=>$current_acad_year_conc,'current_acad_year_refund'=>$AdjustAdditionExcessRemarks);
    }

    public function create_fee_statement_template_iish($statement_details, $student_data, $invoiceTemplate, $excess_amount, $feesBreakup, $previous_balance){
      $total_excess_amount = 0;
      if(!empty($excess_amount) && $excess_amount->total_excess_amount !='0.00'){
        $label = '-';
        $total_excess_amount = $excess_amount->total_excess_amount;
      }
      $template = $invoiceTemplate->template;
      $boarding = $this->settings->getSetting('boarding')[$student_data->boarding];
     
      $template = str_replace('%%student_board_type%%',$boarding, $template);
      $template = str_replace('%%invoice_number%%',$invoiceTemplate->invoice_number, $template);
      $template = str_replace('%%invoice_date%%',date('d-m-Y'), $template);
      $template = str_replace('%%student_class_section%%',$student_data->className, $template);
      $template = str_replace('%%father_name%%',$student_data->father_name, $template);
      $template = str_replace('%%mother_name%%',$student_data->mother_name, $template);
      $template = str_replace('%%admission_no%%',$student_data->admission_no, $template);
      $template = str_replace('%%student_name%%',$student_data->stdName, $template);
      $template = str_replace('%%current_academic_year%%', $this->acad_year->getAcadYear(), $template);
      $invoice_part = '<table class="table table-bordered" style="border: 2px solid #000;">';
      $invoice_part .= '<tr>';
      $invoice_part .= '<th style="background: #fff;color: #000;border: 2px solid;">Particulars</th>';
      $invoice_part .= '<th style="background: #fff;color: #000;border: 2px solid;">Amount in Rs.</th>';
      $invoice_part .= '</tr>';
      $totalpayableFee = 0;
      $totalReceivedFee = 0;
      $fullFeeDiscount = 0;
      foreach ($statement_details['current_acad_year_data'] as $bpName => $value) {
          $invoice_part .= '<tr>';
          $invoice_part .= '<th colspan="2" style="background: #fff;color: #000;border: 2px solid; text-align:left ">'.$bpName.'</th>';
          $invoice_part .= '</tr>';
          foreach ($value as $val) {
            $acadyear = $this->acad_year->getAcadYearById($val->acad_year_id);
            $totalpayableFee += $val->component_amount;
            $totalReceivedFee += $val->component_paid;
            $fullFeeDiscount += $val->discount;
            $invoice_part .= '<tr>';
            $invoice_part .= '<td style="background: #fff;color: #000;border: 2px solid; text-align:left ">'.$val->component_name.'</td>';
            $invoice_part .= '<th style="background: #fff;color: #000;border: 2px solid; text-align:right">'.numberToCurrency_withoutSymbol($val->component_amount).'</th>';
            $invoice_part .= '</tr>';
          }
      }
      

      $openingBalance = 0;
      if(!empty($previous_balance)){
        foreach ($previous_balance as $key => $val) {
          $openingBalance += $val->balance;
          
          
        }
      }
      if ($openingBalance !=0) { 
        $invoice_part .= '<tr>';
        $invoice_part .= '<th colspan="2" style="background: #fff;color: #000;border: 2px solid; text-align:left "><strong>Add: Dues for the year 2024-25</strong></th>';
        $invoice_part .= '</tr>';
        foreach ($previous_balance as $key => $val) {
          $invoice_part .='<tr>';
          $invoice_part .= '<td style="background: #fff;color: #000;border: 2px solid; text-align:left ">'.$val->bpName.'</td>';
          $invoice_part .= '<th style="background: #fff;color: #000;border: 2px solid; text-align:right">'.numberToCurrency_withoutSymbol($val->balance).'</th>';
          $invoice_part .='</tr>';
        }
      
      }

      $invoice_part .='<tr>';
      $invoice_part .='<th style="background: #fff;color: #000;border: 2px solid; text-align:left"><strong>Total (A)</strong></th>';
      $invoice_part .='<th style="background: #fff;color: #000;border: 2px solid; text-align:right"><strong>'.numberToCurrency_withoutSymbol($totalpayableFee + $openingBalance).'</th>';
      $invoice_part .='</tr>';

      // $invoice_part .='<tr>';
      // $invoice_part .='<th style="background: #fff;color: #000;border: 2px solid; text-align:left"><strong>Less: Credit Balances for the year 2023-24</strong></th>';
      // $invoice_part .='<th style="background: #fff;color: #000;border: 2px solid; text-align:right"><strong></th>';
      // $invoice_part .='</tr>';

      $excessAmount = 0;
      foreach ($statement_details['current_acad_year_refund'] as $acadYearId => $val) {
          $excessAmount += $val->total_amount;
          $excessRemarks = $val->remarks;
          if($val->remarks =='' || $val->remarks =='.'){
            $excessRemarks ='Excess Amount';
          }
          if($val->total_amount > 0){
            $invoice_part .= '<tr>';
            $invoice_part .= '<th style="background: #fff;color: #000;border: 2px solid; text-align:left ">'.$excessRemarks.'</th>';
            $invoice_part .= '<th style="background: #fff;color: #000;border: 2px solid; text-align:right">'.numberToCurrency_withoutSymbol($val->total_amount).'</th>';
            $invoice_part .= '</tr>';
          }
      }

      $preConcession = 0;
      foreach ($statement_details['current_acad_year_concession'] as $acadYearId => $con) {
        $preConcession += $con->concession_amount;
        $predefinedName = explode('_',$con->feev2_predefined_name);
        $invoice_part .= '<tr>';
        $invoice_part .= '<th style="background: #fff;color: #000;border: 2px solid; text-align:left ">  Less: '.$predefinedName[0].'( Discount ) </th>';
        $invoice_part .= '<th style="background: #fff;color: #000;border: 2px solid; text-align:right"> '.numberToCurrency_withoutSymbol($con->concession_amount).'</th>';
        $invoice_part .= '</tr>';
      }
      if ($fullFeeDiscount !=0) {
        $invoice_part .= '<tr>';
        $invoice_part .= '<th style="background: #fff;color: #000;border: 2px solid; text-align:left "> Less: Full Fees ( Discount ) </th>';
        $invoice_part .= '<th style="background: #fff;color: #000;border: 2px solid; text-align:right"> '.numberToCurrency_withoutSymbol($fullFeeDiscount).'</th>';
      }
      
      $invoice_part .='<tr>';
      $invoice_part .='<td style="background: #fff;color: #000;border: 2px solid; text-align:left"><strong>Fee Received</strong></td>';
      $invoice_part .='<td style="background: #fff;color: #000;border: 2px solid; text-align:right"><strong>'.numberToCurrency_withoutSymbol($totalReceivedFee).'</td>';
      $invoice_part .='</tr>';
      
      $invoice_part .='<tr>';
      $invoice_part .='<th style="background: #fff;color: #000;border: 2px solid; text-align:left"><strong>Total (B)</strong></th>';
      $invoice_part .='<th style="background: #fff;color: #000;border: 2px solid; text-align:right"><strong>'.numberToCurrency_withoutSymbol($totalReceivedFee + $preConcession + $excessAmount + $fullFeeDiscount).'</th>';
      $invoice_part .='</tr>';

      $invoice_part .='<tr>';
      $invoice_part .='<th style="background: #fff;color: #000;border: 2px solid; text-align:left"><strong>Grand Total Fee For The A Y 2025-26  (A-B)</strong></th>';
      $invoice_part .='<th style="background: #fff;color: #000;border: 2px solid; text-align:right"><strong>'.numberToCurrency_withoutSymbol(($totalpayableFee + $openingBalance - $fullFeeDiscount - $excessAmount - $totalReceivedFee - $preConcession)).'</th>';
      $invoice_part .='</tr>';
      $insAmountbreak = 0;
      foreach ($feesBreakup as $ins_date => $value) {
        $insAmountbreak = array_sum($feesBreakup[$ins_date]);
        if (strpos($ins_date, 'Term 1') !== false) {
          $insAmountbreak += $openingBalance - $excessAmount;
        }
        $compNamedetails= '';
        foreach ($value as $CompName => $value) {
          if (!empty($compNamedetails))
          $compNamedetails .= ', ';
          $compNamedetails .= $CompName;
        }
        $invoice_part .= '<tr>';
        $invoice_part .= '<th style="background: #fff;color: #000;border: 2px solid; text-align:left "> '.$ins_date.'</th>';
        $invoice_part .= '<th style="background: #fff;color: #000;border: 2px solid; text-align:right"> '.numberToCurrency_withoutSymbol($insAmountbreak).'</th>';
        $invoice_part .= '</tr>';
      }
      $invoice_part .='</table>';
      $template = str_replace('%%invoice_data%%', $invoice_part, $template);
      $signature = '';
      if ($invoiceTemplate->invoice_signature !='') {
        $getURL = $this->filemanager->getFilePath($invoiceTemplate->invoice_signature);
        $signature = '<img style="width:100px !important; height:60px!important" src="'.$getURL.'" />';
      }
      $template = str_replace('%%invoice_signature%%', $signature, $template);
      $template = str_replace('%%invoice_note%%', $invoiceTemplate->invoice_note, $template);
      return $template;
    }

    public function class_section_wise_student_invoice_statement($classSectionId){
      $this->db_readonly->select('sa.id as stdId')
      ->from('student_admission sa')
      ->where('sa.admission_status','2')
      ->join('student_year sy','sa.id=sy.student_admission_id')
      ->join('class c','sy.class_id=c.id')
      ->join('class_section cs','sy.class_section_id=cs.id','left')
      ->where('sy.promotion_status!=', '4')
      ->where('sy.promotion_status!=', '5')
      ->where('sy.acad_year_id',$this->yearId);    
      if($classSectionId) {
        $this->db_readonly->where_in('sy.class_section_id',$classSectionId);
      }
      if($this->current_branch) {
        $this->db_readonly->where('c.branch_id',$this->current_branch);
      }
      $this->db_readonly->order_by('c.id','cs.id','sa.first_name');
      $result = $this->db_readonly->get()->result();
      $stdIds = [];
      foreach ($result as $key => $res) {
        array_push($stdIds, $res->stdId);
      }
      return $stdIds;
    }

    function fee_invoice_statement_student_list($stdIds, $fee_acad_year){
      $stdArry = [];
      foreach ($stdIds as $key => $stdId) {
        $student = $this->get_std_details_invoice_statement_byId($stdId, $this->yearId);
        $student->fees = $this->get_fees_invoice_statment_details($stdId, $fee_acad_year);
        $student->invoice = $this->get_invoices_pdf_status($stdId, 'Invoice');
        $student->statment = $this->get_invoices_pdf_status($stdId, 'Statement');

        array_push($stdArry, $student);
      }
      return $stdArry;
    }

    function get_fees_invoice_statment_details($stdId, $fee_acad_year){

      $this->db->select('fb.acad_year_id');
      $this->db->from('feev2_blueprint fb');
      if($fee_acad_year){
        $this->db->where('fb.acad_year_id',$fee_acad_year);
      }
      $this->db->group_by('fb.acad_year_id');
      $fee_year = $this->db->get()->result();

      foreach ($fee_year as $key => $val) {
        $val->yearname = $this->acad_year->getAcadYearById($val->acad_year_id);
      }
      
      $this->db->select("fb.acad_year_id, fbc.name as blueprint_name, sum(fsic.component_amount) as component_amount, sum(fsic.concession_amount) as concession_amount, sum(ifnull(component_amount_paid,0)) as component_paid, fcs.remarks, fb.enable_remarks, fcs.id as cohort_student_id,fcs.student_id, fss.id as sch_id");
      $this->db->from('feev2_blueprint fb');
      $this->db->join('feev2_cohort_student fcs','fcs.blueprint_id=fb.id');
      $this->db->where('fcs.student_id',$stdId);
      $this->db->join('feev2_student_schedule fss','fcs.id=fss.feev2_cohort_student_id');
      $this->db->join('feev2_student_installments fsi','fss.id=fsi.fee_student_schedule_id');
      $this->db->join('feev2_student_installments_components fsic','fsic.fee_student_installment_id=fsi.id');
      $this->db->join('feev2_blueprint_components fbc','fbc.id=fsic.blueprint_component_id');
      if($fee_acad_year){
        $this->db->where('fb.acad_year_id',$fee_acad_year);
      }
      $this->db->group_by('fbc.id');
      $fee_data =  $this->db->get()->result();
      
      $feeArry =  [];
      foreach ($fee_data as $key => $value) {
        $feeArry[$value->acad_year_id][] = $value;
      }

      $schDiscount = $this->db->select('fb.acad_year_id, fss.id as sch_id, sum(ifnull(fss.discount,0)) as discount')
      ->from('feev2_blueprint fb')
      ->join('feev2_cohort_student fcs','fcs.blueprint_id=fb.id')
      ->join('feev2_student_schedule fss','fcs.id=fss.feev2_cohort_student_id')
      ->where('fcs.student_id',$stdId)
      ->group_by('fb.acad_year_id')
      ->get()->result();
      $shcArry = [];
      foreach ($schDiscount as $key => $val) {
        $shcArry[$val->acad_year_id] = $val;
      }

      $this->db->select("fcs.id as cohort_student_id,(case when fcpdc.feev2_predefined_name = 'Custom' then fcpdc.remarks else fcpdc.feev2_predefined_name end) as feev2_predefined_name, fcpdc.concession_amount, fb.acad_year_id");
      $this->db->from('feev2_blueprint fb');
      $this->db->join('feev2_cohort_student fcs','fcs.blueprint_id=fb.id');
      $this->db->join('feev2_concessiontype2_pre_defined_concession fcpdc','fcs.id=fcpdc.cohort_student_id');
      $this->db->where('fcs.student_id',$stdId);
      if($fee_acad_year){
        $this->db->where('fb.acad_year_id',$fee_acad_year);
      }
      $conc_data = $this->db->get()->result();

      $consArry =  [];
      foreach ($conc_data as $key => $value) {
        $consArry[$value->acad_year_id][] = $value;
      }

      foreach ($fee_year as $key => $val) {
        $val->fee_data[$val->acad_year_id] = [];
        $val->con_data[$val->acad_year_id] = [];
        $val->discounts[$val->acad_year_id] = [];
        if (array_key_exists($val->acad_year_id, $feeArry)) {
          $val->fee_data[$val->acad_year_id] = $feeArry[$val->acad_year_id];
        }
        if (array_key_exists($val->acad_year_id, $consArry)) {
          $val->con_data[$val->acad_year_id] = $consArry[$val->acad_year_id];
        }
        if (array_key_exists($val->acad_year_id, $shcArry)) {
          $val->discounts[$val->acad_year_id] = $shcArry[$val->acad_year_id];
        }
      }
      return $fee_year;
    }

    public function get_std_details_invoice_statement_byId($stdId, $fee_acad_year_id){
      $result =  $this->db->select("sd.id as std_admission_id, sy.id as std_year_id, concat(ifnull(sd.first_name,''),' ',ifnull(sd.last_name,'')) as stdName, sd.admission_no, c.class_name as className, cs.section_name, sy.board, sy.boarding, sy.medium, sd.category, sy.is_rte, sd.admission_acad_year_id as academic_year_of_joining, sy.admission_type, sy.class_id as class, sd.has_staff as has_staff, sd.sibling_type as has_sibling, c.type as class_type, sy.acad_year_id, donor, has_transport, sd.staff_id, has_transport_km, stop, pickup_mode, sd.gender, if(sh.physical_disability is null or sh.physical_disability='','0',sh.physical_disability) as physical_disability,sd.life_time_fee_mode as is_lifetime_student, sy.combination, sd.attempt, sd.quota, p1.first_name as father_name, p2.first_name as mother_name, ifnull(p1.mobile_no,'')  as father_phone, ifnull(p2.mobile_no,'') as mother_phone, p1.email as father_email, p2.email as mother_email,admission_status")
      ->from('student_year sy')
      ->where('sy.promotion_status!=', '4')
      ->where('sy.promotion_status!=', '5')
      ->where('sd.id',$stdId)
      ->where('sy.acad_year_id',$fee_acad_year_id)
      ->join('student_admission sd','sy.student_admission_id=sd.id')
      // ->where('sd.admission_status','2')
      ->join('student_health sh','sh.student_id=sd.id','left')
      ->join('class c','sy.class_id=c.id')
      ->join('class_section cs','sy.class_section_id=cs.id')
      ->join('student_relation sr1', "sr1.std_id=sd.id and sr1.relation_type='Father'")
      ->join('parent p1', 'p1.id=sr1.relation_id')
      ->join('student_relation sr2', "sr2.std_id=sd.id and sr2.relation_type='mother'")
      ->join('parent p2', 'p2.id=sr2.relation_id')
      ->get()->row();

      $AdjustAdditionExcessRemarks = $this->db->select('remarks, sum(total_amount - total_used_amount - excess_refund_amount) as total_amount, student_id')
      ->from('feev2_additional_amount')
      ->where('student_id',$stdId)
      ->where('acad_year_id',$fee_acad_year_id)
      ->group_by('student_id')
      ->get()->result();
      
      $sch_data = $this->db->select('sum(fss.total_fee_paid) as total_fee_paid')
      ->from('feev2_blueprint fb')
      ->join('feev2_cohort_student fcs','fb.id=fcs.blueprint_id')
      ->join('feev2_student_schedule fss','fcs.id=fss.feev2_cohort_student_id')
      ->where('fcs.student_id',$stdId)
      ->where('fb.acad_year_id',$fee_acad_year_id)
      ->get()->row();
      $total_fee_paid = 0;
      if(!empty($sch_data)){
        $total_fee_paid = $sch_data->total_fee_paid;
      }
      $trans_amount = 0;
      $trans_data = $this->db->select('sum(ft.amount_paid) as trans_amount')
      ->from('feev2_transaction ft')
      ->where('ft.student_id',$stdId)
      ->where('ft.soft_delete!=',1)
      ->where('ft.status','SUCCESS')
      ->where('ft.acad_year_id',$fee_acad_year_id)
      ->get()->row();
      if(!empty($trans_data)){
        $trans_amount = $trans_data->trans_amount;
      }	
      
      $closingBal = $total_fee_paid - $trans_amount;
      
      $AdjustAdditionExcessRemarks = $this->db->select('fam.remarks, 
        fam.total_amount, faau.used_fee_trans_id,fam.id')
      ->from('feev2_additional_amount fam')
      ->join('feev2_additional_amount_usage faau','fam.id=faau.fee_addt_amount_id','left')
      ->where('fam.student_id',$stdId)
      ->where('fam.acad_year_id',$fee_acad_year_id)
      ->get()->result();
      
      $transaction_details = $this->db->select('ft.id as trans_id, ft.amount_paid')
      ->from('feev2_transaction ft')
      ->where('ft.soft_delete !=','1')
      ->where('ft.status','SUCCESS')
      // ->where('ft.paid_datetime <=','2023-06-30')
      ->where('ft.acad_year_id',$fee_acad_year_id)
      ->where('ft.student_id',$stdId)
      ->get()->result();
      $transArry = [];
      foreach ($transaction_details as $key => $val) {
        $transArry[$val->trans_id] = $val->amount_paid;
      }

      $tmepArry = [];
      foreach ($AdjustAdditionExcessRemarks as $key => $val) {
        if(!array_key_exists($val->id, $tmepArry)){
        $tmepArry[$val->id]['used_fee_trans_id'] = [];
        $tmepArry[$val->id]['total_amount'] = 0;
        $tmepArry[$val->id]['remarks'] ='';
        }
        $tmepArry[$val->id]['used_fee_trans_id'][] = $val->used_fee_trans_id;
        $tmepArry[$val->id]['total_amount'] = $val->total_amount;
        $tmepArry[$val->id]['remarks'] = $val->remarks;
      }

      foreach ($tmepArry as $excess_id => $value) {
        $resultArray[$excess_id] = 0;
        foreach ($value['used_fee_trans_id'] as $key => $val) {
          if (array_key_exists($val, $transArry)) {
          $resultArray[$excess_id] += $transArry[$val];
          }
        }
        }
    
        foreach ($tmepArry as $id => $value) {
          if(array_key_exists($id, $resultArray)){
          $tmepArry[$id]['total_amount'] = $value['total_amount'] - $resultArray[$id];
          }
        }
    
        $excess_amount_current = [];
        foreach ($tmepArry as $key => $value) {
        $obj = new stdClass();
        $obj->remarks = $value['remarks'];
        $obj->total_amount = $value['total_amount'];
        array_push($excess_amount_current, $obj);
      }
      $Excesscurrent = 0;
      if(!empty($excess_amount_current)){
        foreach ($excess_amount_current as $key => $val) {
          $Excesscurrent += $val->total_amount;
        }
      }
      $result->excess_amount = $Excesscurrent;
      $result->closing_balance = $closingBal;
      return $result;
    }

    function get_acad_year_list_for_fees(){
      $result = $this->db_readonly->select('acad_year_id')
      ->from('feev2_blueprint')
      ->group_by('acad_year_id')
      ->order_by('acad_year_id','desc')
      ->get()->result();
      $years = [];
      if(!empty($result)){
        foreach ($result as $key => $val) {
          $years[$val->acad_year_id] = $this->acad_year->getAcadYearById($val->acad_year_id);
        }
      }
      return $years;
    }

    public function get_fee_student_class_id($student_id){
      return $this->db->select("c.id as class_id")
      ->from('student_year sy')
      ->where('sy.student_admission_id',$student_id)
      ->where('sy.acad_year_id',$this->yearId)
      ->join('class c','sy.class_id=c.id')
      ->get()->row();
    }

    public function get_email_report($from_date, $to_date) {
      $filter_type = $_POST['filter_type'];
      $id = $_POST['id'];
      
      $this->db_readonly->select("fis.id as invoice_id, fis.student_admission_id, date_format(fis.created_on,'%d-%m-%Y') as created_on, invoice_path, email_master_id");
      $this->db_readonly->from('feev2_invoice_template fit');
      $this->db_readonly->join('feev2_invoice_student fis','fit.id=fis.feev2_invoice_template_id');
      $this->db_readonly->where('fit.invoice_type',$_POST['selected_inovice_type']);
      $this->db_readonly->where('fis.pdf_status','1');
      if ($from_date && $to_date) {
        $fromDate = date('Y-m-d',strtotime($from_date));
        $toDate =date('Y-m-d',strtotime($to_date));
        $this->db_readonly->where('date_format(fis.created_on,"%Y-%m-%d") BETWEEN "'.$fromDate. '" and "'.$toDate.'"');
      }
      $invoice_master = $this->db_readonly->get()->result();
      $studentIds = [];
      $emailMasterIds = [];
      foreach ($invoice_master as $key => $val) {
        array_push($studentIds, $val->student_admission_id);
        array_push($emailMasterIds, $val->email_master_id);
      }
    
      
      if(!empty($studentIds)){
        $this->db_readonly->select("sa.id as student_admission_id, sa.admission_no, concat(sa.first_name, ' ', ifnull(sa.last_name, '')) as student, concat(c.class_name, ' ', ifnull(cs.section_name, '')) as section_name");
        $this->db_readonly->from('student_admission sa');
        $this->db_readonly->join("student_year sy","sy.student_admission_id = sa.id");
        $this->db_readonly->join("class c","sy.class_id = c.id");
        $this->db_readonly->join("class_section cs","sy.class_section_id = cs.id",'left');
        $this->db_readonly->where("sy.acad_year_id", $this->yearId);
        $this->db_readonly->where_in('sa.id',$studentIds);
        if($filter_type == 'class') {
          $this->db_readonly->where('c.id', $id);
        } else if($filter_type == 'section') {
          $this->db_readonly->where('cs.id', $id);
        } else if($filter_type == 'student') {
          $this->db_readonly->where("concat(sa.first_name, ' ', ifnull(sa.last_name, '')) like '%$id%'");
        } else if($filter_type == 'admission') {
          $this->db_readonly->where('sa.admission_no', $id);
        }
        $student_master = $this->db_readonly->get()->result();
        foreach ($invoice_master as $key => $val) {
          foreach ($student_master as $key => $std) {
            if($val->student_admission_id == $std->student_admission_id){
              $val->admission_no = $std->admission_no;
              $val->student = $std->student;
              $val->section_name = $std->section_name;
            }
          }
        }
      }

       $parent_master = [];
       if(!empty($invoice_master)){
        $this->db_readonly->select("concat(p.first_name, ' ', ifnull(p.last_name, '')) as parent, ifnull(p.email,'') as p_email, sr.relation_type, est.status as email_sent_status, p.student_id as student_admission_id, date_format(em.sent_on,'%d-%m-%Y') as sent_on");
        $this->db_readonly->from('email_sent_to est');
        $this->db_readonly->join('email_master em','est.email_master_id = em.id');
        $this->db_readonly->join('parent p','est.stakeholder_id = p.id');
        $this->db_readonly->where_in('est.email_master_id',$emailMasterIds);
        $this->db_readonly->join('student_relation sr','sr.relation_id = p.id');
        $this->db_readonly->where('est.avatar_type',2);
        $parent_master = $this->db_readonly->get()->result();
       }
      $parentData = [];
      if(!empty($parent_master)){
        foreach ($parent_master as $key => $val) {
          $parentData[$val->student_admission_id][] = $val;
        }
      }
      foreach ($invoice_master as $key => $val) {
        $val->parent_details = [];
        $val->sent_on = '';
        if(!empty($parentData)){
          if(array_key_exists($val->student_admission_id, $parentData)){
            $val->parent_details = $parentData[$val->student_admission_id];
            $val->sent_on = $val->sent_on;
          }
        }
      }

      return $invoice_master;

      // echo "<pre>a"; print_r($invoice_master);
     
      // $email= $this->db_readonly->select("et.status as email_sent_status, fit.invoice_type, fis.student_admission_id, fit.invoice_type, sa.id as student_id, sa.admission_no, concat(sa.first_name, ' ', ifnull(sa.last_name, '')) as student, cs.section_name, cs.class_id, cs.id as class_section_id, cs.class_name, concat(p.first_name, ' ', ifnull(p.last_name, '')) as parent, ifnull(p.email,'') as p_email, sr.relation_type");
      // $this->db_readonly->from('feev2_invoice_template fit');
      // $this->db_readonly->join('feev2_invoice_student fis','fit.id=fis.feev2_invoice_template_id');
      // $this->db_readonly->join('student_admission sa','sa.id=fis.student_admission_id');
      // $this->db_readonly->join("student_year sy","sy.student_admission_id = sa.id");
      // $this->db_readonly->join('class_section cs','cs.id = sy.class_section_id');
      // $this->db_readonly->where("sy.acad_year_id", $this->yearId);
      // $this->db_readonly->join('parent p','p.student_id = sa.id');
      // $this->db_readonly->join('email_sent_to et', 'et.stakeholder_id=p.id');
      // $this->db_readonly->join('student_relation sr','sr.relation_id = p.id');
      // $this->db_readonly->where('fit.invoice_type',$_POST['selected_inovice_type']);
      // if($filter_type == 'class') {
      //   $this->db_readonly->where('cs.class_id', $id);
      // } else if($filter_type == 'section') {
      //   $this->db_readonly->where('cs.id', $id);
      // } else if($filter_type == 'student') {
      //   $this->db_readonly->where("concat(sa.first_name, ' ', ifnull(sa.last_name, '')) like '%$id%'");
      // } else if($filter_type == 'admission') {
      //   $this->db_readonly->where('sa.admission_no', $id);
      // }

      // if ($from_date && $to_date) {
      //   $fromDate = date('Y-m-d',strtotime($from_date));
      //   $toDate =date('Y-m-d',strtotime($to_date));
      //   $this->db_readonly->where('date_format(fis.created_on,"%Y-%m-%d") BETWEEN "'.$fromDate. '" and "'.$toDate.'"');
      // }
      // // $this->db_readonly->group_by('p.id');
      // $email = $this->db_readonly->get()->result();
      // echo "<pre>"; print_r($email); die();
      // return $email;
      // foreach($email as $key => $val) {      
      //   $this->db_readonly->select("sa.id as student_id, sa.admission_no, concat(sa.first_name, ' ', ifnull(sa.last_name, '')) as student, cs.section_name, cs.class_id, cs.id as class_section_id, cs.class_name, concat(p.first_name, ' ', ifnull(p.last_name, '')) as parent, p.email as p_email, sr.relation_type")
      //       ->from('student_admission sa')
      //       ->join("student_year sy","sy.student_admission_id= sa.id")
      //       ->join('class_section cs','cs.id= sy.class_section_id')
      //       ->where("sy.acad_year_id", $this->yearId)
      //       ->join('parent p','p.student_id= sa.id')
      //       ->where('sa.id', $val->student_admission_id)
      //       ->join('student_relation sr','sr.relation_id= p.id');
      //   if($filter_type == 'class') {
      //     $this->db_readonly->where('cs.class_id', $id);
      //   } else if($filter_type == 'section') {
      //     $this->db_readonly->where('cs.id', $id);
      //   } else if($filter_type == 'student') {
      //     $this->db_readonly->where("concat(sa.first_name, ' ', ifnull(sa.last_name, '')) like '%$id%'");
      //   } else if($filter_type == 'admission') {
      //     $this->db_readonly->where('sa.admission_no', $id);
      //   }
      //   $student_details= $this->db_readonly->get()->row();
      //   echo "<pre>"; print_r($student_details);die();
      //   $email[$key]->student_details= $student_details;
      //   // $email[$key]->student_details->filter_type= $filter_type;
      //   $email[$key]->student_details->filter_id= $id;
      // }

      

      // return $email;
    }

    public function get_all_url_for_download($input){
      $filter_type = $input['filter_type'];
      $id = $input['id'];
      $from_date = $input['from_date']; 
      $to_date = $input['to_date'];
      $this->db_readonly->select("fis.student_admission_id, invoice_path");
      $this->db_readonly->from('feev2_invoice_template fit');
      $this->db_readonly->join('feev2_invoice_student fis','fit.id=fis.feev2_invoice_template_id');
      $this->db_readonly->where('fit.invoice_type',$input['selected_inovice_type']);
      if ($from_date && $to_date) {
        $fromDate = date('Y-m-d',strtotime($from_date));
        $toDate =date('Y-m-d',strtotime($to_date));
        $this->db_readonly->where('date_format(fis.created_on,"%Y-%m-%d") BETWEEN "'.$fromDate. '" and "'.$toDate.'"');
      }
      $invoice_master = $this->db_readonly->get()->result();
      $studentIds = [];
      foreach ($invoice_master as $key => $val) {
        array_push($studentIds, $val->student_admission_id);
      }

      if(empty($invoice_master)){
        return array();
      }

      if(!empty($studentIds)){
        $this->db_readonly->select("sa.id as student_admission_id, concat(sa.first_name, ' ', ifnull(sa.last_name, '')) as student, concat(c.class_name, ' ', ifnull(cs.section_name, '')) as section_name");
        $this->db_readonly->from('student_admission sa');
        $this->db_readonly->join("student_year sy","sy.student_admission_id = sa.id");
        $this->db_readonly->join("class c","sy.class_id = c.id");
        $this->db_readonly->join("class_section cs","sy.class_section_id = cs.id",'left');
        $this->db_readonly->where("sy.acad_year_id", $this->yearId);
        $this->db_readonly->where_in('sa.id',$studentIds);
        if($filter_type == 'class') {
          $this->db_readonly->where('c.id', $id);
        } else if($filter_type == 'section') {
          $this->db_readonly->where('cs.id', $id);
        } else if($filter_type == 'student') {
          $this->db_readonly->where("concat(sa.first_name, ' ', ifnull(sa.last_name, '')) like '%$id%'");
        } else if($filter_type == 'admission') {
          $this->db_readonly->where('sa.admission_no', $id);
        }
        $student_master = $this->db_readonly->order_by('sa.first_name')->get()->result();
        foreach ($invoice_master as $key => $val) {
          foreach ($student_master as $key => $std) {
            if($val->student_admission_id == $std->student_admission_id){
              $val->file_name = $std->student . ' ' . $std->section_name . ' ' . $input['selected_inovice_type'];
            }
          }
        }
      }
      if(!empty($invoice_master)){
        return $invoice_master;
      } else {
        return array();
      }
    }

    public function get_all_class() {
      return $this->db_readonly->select("c.class_name, c.id")->where('c.acad_year_id', $this->yearId)->get('class c')->result();
    }

    public function get_sections() {
      return $this->db_readonly->select("cs.section_name, cs.id")->where('cs.class_id', $_POST['class_id'])->get('class_section cs')->result();
    }

    public function get_std_fee_for_invoice_ielcjh($std_id) {
      
      $result =  $this->db->select("fcs.id as cohort_student_id, fcs.id as feev2_cohort_student_id, fcs.fee_collect_status, fcs.fee_cohort_status, fss.id as std_sch_id, fb.name as blueprint_name, fbc.name as component_name, sum(fsic.component_amount) as component_amount, fsic.concession_amount as concession_amount, sum(ifnull(component_amount_paid,0)) as component_paid,  sum(fsi.installment_amount) as installment_amount, fi.name as installment_name, date_format(fi.end_date,'%d-%m-%Y') as ins_date")
        ->from('feev2_blueprint fb')
        ->join('feev2_cohort_student fcs','fcs.blueprint_id=fb.id')
        ->where('fcs.student_id',$std_id)
        ->where('fb.acad_year_id',$this->yearId)
        ->join('feev2_student_schedule fss','fcs.id=fss.feev2_cohort_student_id')
        ->join('feev2_student_installments fsi','fss.id=fsi.fee_student_schedule_id')
        ->join('feev2_student_installments_components fsic','fsic.fee_student_installment_id=fsi.id')
        ->join('feev2_blueprint_components fbc','fbc.id=fsic.blueprint_component_id')
        ->join('feev2_installments fi','fsi.feev2_installments_id=fi.id')
        ->group_by('fbc.id,fi.id')
        ->get()->result();
       
        $bpAmount = [];
        foreach ($result as $key => $val) {
          $bpAmount[$val->blueprint_name][$val->component_name] = $val->component_amount;
        }
        $bpInstallmentAmount = [];
        foreach ($result as $key => $val) { 
          // $bpInstallmentAmount[$val->blueprint_name][$val->component_name][$val->installment_name] = array('component_amount'=>$val->component_amount,'installment_amount'=>$val->installment_amount);
          $bpInstallmentAmount[$val->blueprint_name][$val->component_name][$val->installment_name] = array('component_amount'=>$val->component_amount,'component_paid'=>$val->component_paid);
        }
      
        return $bpInstallmentAmount;

        // echo "<pre>"; print_r($bpInstallmentAmount); die();
        // return array('bpAmount'=>$bpAmount, 'bpInstallmentAmount'=>$bpInstallmentAmount);

    }
    
    public function check_invoice_opening_balance_amount($std_id){
      $sch_data = $this->db->select('fss.id as sch_id, fb.acad_year_id')
      ->from('feev2_cohort_student fcs')
      ->join('feev2_student_schedule fss','fcs.id=fss.feev2_cohort_student_id')
      ->join('feev2_blueprint fb','fb.id=fcs.blueprint_id')
      ->where('fcs.student_id',$std_id)
      ->get()->result();
      $acadYearSchData = [];
      foreach ($sch_data as $key => $sch) {
        $acadYearSchData[$sch->sch_id] = $sch;
      }
      $trans_data = $this->db->select('ft.id as trans_id, ft.fee_student_schedule_id, sum(ft.amount_paid) as trans_amount, ft.acad_year_id')
        ->from('feev2_transaction ft')
        ->where('ft.student_id',$std_id)
        ->where('ft.soft_delete!=',1)
        ->where('ft.status','SUCCESS')
        ->where('ft.acad_year_id',$this->yearId)
        ->group_by('ft.fee_student_schedule_id')
        ->get()->result();

      $result = $this->db_readonly->select("fb.name as bpName, ifnull(fss.total_fee,0) - ifnull(fss.total_fee_paid,0) -  ifnull(fss.total_concession_amount,0)  - ifnull(fss.total_concession_amount_paid,0) - ifnull(fss.total_adjustment_amount,0) - ifnull(fss.total_adjustment_amount_paid,0) as balance")
        ->from('feev2_blueprint fb')
        ->where('fb.acad_year_id <',$this->yearId)
        ->join('feev2_cohort_student fcs','fb.id=fcs.blueprint_id')
        ->where('fcs.student_id',$std_id)
        ->join('feev2_student_schedule fss','fcs.id=fss.feev2_cohort_student_id')
        ->where('fss.payment_status!=','FULL')
        ->get()->result();

      foreach ($trans_data as $key => $val) {
        $val->balance = 0;
        if(array_key_exists($val->fee_student_schedule_id, $acadYearSchData)){
          if($val->acad_year_id != $acadYearSchData[$val->fee_student_schedule_id]->acad_year_id){
            $val->balance += $val->trans_amount;
          }
        }
      }
      $merge = array_merge($result, $trans_data);
      foreach ($merge as $key => $value) {
        if($value->balance != 0){
          $value->balance = $value->balance;
        }
      }

      return $merge;
    }
    
    public function get_excess_amount_previous_ob_by_std($std_id){

      $AdjustAdditionExcessRemarks = $this->db->select('fam.remarks, 
      fam.total_amount, fam.total_used_amount, fam.excess_refund_amount, faau.used_fee_trans_id,fam.id')
      ->from('feev2_additional_amount fam')
      ->join('feev2_additional_amount_usage faau','fam.id=faau.fee_addt_amount_id','left')
      ->where('fam.student_id',$std_id)
      ->where('fam.acad_year_id<',$this->yearId)
      ->get()->result();
   
      $transaction_details = $this->db->select('ft.id as trans_id, ft.amount_paid')
      ->from('feev2_transaction ft')
      ->where('ft.soft_delete !=','1')
      ->where('ft.status','SUCCESS')
      // ->where('ft.paid_datetime <=','2023-06-30')
      ->where('ft.acad_year_id<',$this->yearId)
      ->where('ft.student_id',$std_id)
      ->get()->result();
      $transArry = [];
      foreach ($transaction_details as $key => $val) {
        $transArry[$val->trans_id] = $val->amount_paid;
      }

      $tmepArry = [];
      foreach ($AdjustAdditionExcessRemarks as $key => $val) {
        if(!array_key_exists($val->id, $tmepArry)){
        $tmepArry[$val->id]['used_fee_trans_id'] = [];
        $tmepArry[$val->id]['total_amount'] = 0;
        $tmepArry[$val->id]['recived_amount'] = 0;
        $tmepArry[$val->id]['remarks'] ='';
        }
        $tmepArry[$val->id]['used_fee_trans_id'][] = $val->used_fee_trans_id;
        $tmepArry[$val->id]['total_amount'] = $val->total_amount;
        $tmepArry[$val->id]['recived_amount'] = $val->total_amount;
        $tmepArry[$val->id]['refund_amount'] = $val->excess_refund_amount;
        $tmepArry[$val->id]['remarks'] = $val->remarks;
      }
      
      foreach ($tmepArry as $excess_id => $value) {
        $resultArray[$excess_id] = 0;
        foreach ($value['used_fee_trans_id'] as $key => $val) {
          if (array_key_exists($val, $transArry)) {
          $resultArray[$excess_id] += $transArry[$val];
          }
        }
        }
    
        foreach ($tmepArry as $id => $value) {
          if(array_key_exists($id, $resultArray)){
            $tmepArry[$id]['total_amount'] = $value['total_amount'] - $resultArray[$id];
            $tmepArry[$id]['refund_amount'] = $value['refund_amount'];
            $tmepArry[$id]['recived_amount'] = $value['recived_amount']  - $resultArray[$id];;
          }
        }
    
        $excess_amount_current = [];
        foreach ($tmepArry as $key => $value) {
        $obj = new stdClass();
        $obj->remarks = $value['remarks'];
        $obj->total_amount = $value['total_amount'];
        $obj->return_amount = $value['refund_amount'];
        $obj->recived_amount = $value['recived_amount'];
        array_push($excess_amount_current, $obj);
      }
    
      $Excesscurrent = 0;
      $Excesscurrent_refund = 0;
      $ExcessRecived = 0;
      if(!empty($excess_amount_current)){
        foreach ($excess_amount_current as $key => $val) {
          $Excesscurrent += $val->total_amount;
          $Excesscurrent_refund += $val->return_amount;
          $ExcessRecived += $val->recived_amount;
        }
      }
    $result = new stdClass();
    $result->total_ob_excess_bal = $Excesscurrent;
    $result->total_ob_excess_return = $Excesscurrent_refund;
    $result->total_ob_excess_recived = $ExcessRecived;
    return $result;
      // return $this->db->select('sum(total_amount) - sum(total_used_amount) - sum(excess_refund_amount) as total_ob_excess_bal, faau.used_fee_trans_id,fam.id')
      //   ->from('feev2_additional_amount fam')
      //   ->join('feev2_additional_amount_usage faau','fam.id=faau.fee_addt_amount_id','left')
      //   ->where('fam.student_id',$std_id)
      //   ->where('fam.acad_year_id <',$this->yearId)
      //   ->get()->row();

    }


    public function concession_view_new($schedule_installment_id){
      $result = $this->db->select('fss.feev2_blueprint_installment_types_id, fsi.id as stdInsIds, fsic.id as stdinsCompIds, fsic.component_amount as compAmount, fbc.id component_id, fbc.name as compName, fi.id as feev2_installment_id, fi.name as insName, fsic.concession_amount,  ifnull(fsic.concession_amount_paid,0) as pre_concession_amount, ifnull(fsic.component_amount_paid,0) as component_amount_paid,  fbc.is_concession_eligible, (fsic.component_amount - ifnull(fsic.component_amount_paid + fsic.concession_amount_paid, 0)) as balance, fss.id as std_sch_id')
      ->from('feev2_student_schedule fss')
      ->join('feev2_student_installments fsi','fss.id=fsi.fee_student_schedule_id')
      ->where('fsi.id',$schedule_installment_id)
      ->join('feev2_student_installments_components fsic','fsi.id=fsic.fee_student_installment_id')
      ->join('feev2_installments fi','fsi.feev2_installments_id=fi.id')
      ->join('feev2_blueprint_components fbc','fsic.blueprint_component_id=fbc.id')
      ->get()->result();
      $pre_data = [];
      foreach ($result as $key => $val) {
        $pre_data[$val->insName][] = $val;
      }
      return $pre_data;

    // $concession = $this->db->select('fss.id as schId, fss.feev2_blueprint_installment_types_id, fsi.id as stdInsIds, fsic.id as stdinsCompIds, fsic.component_amount as compAmount, fbc.id component_id, fbc.name as compName, fi.id as feev2_installment_id, fi.name as insName, fsic.concession_amount,  ifnull(fsic.concession_amount_paid,0) as pre_concession_amount, ifnull(fsic.component_amount_paid,0) as component_amount_paid,  fbc.is_concession_eligible, (fsic.component_amount - ifnull(fsic.component_amount_paid + fsic.concession_amount_paid, 0)) as balance')
    // ->from('feev2_student_schedule fss')
    // ->where('fss.id',$schId)
    // ->join('feev2_student_installments fsi','fss.id=fsi.fee_student_schedule_id')
    // // ->where('fsi.status!=','FULL')
    // ->join('feev2_student_installments_components fsic','fsi.id=fsic.fee_student_installment_id')
    // ->join('feev2_installments fi','fsi.feev2_installments_id=fi.id')
    // ->join('feev2_blueprint_components fbc',"fsic.blueprint_component_id=fbc.id")
    // ->get()->result();

    // return $concession;
  }

  public function update_concession_amount_new($std_sch_id, $concession_amount=[], $cohort_student_id, $feev2_blueprint_installment_types_id, $concession_name, $concession_amount_add =[], $pre_defined_name, $pre_defined_con_amount, $remove_pre_definedIds){

    $total_con = 0;
    foreach ($concession_amount as $key => $val) {
      $total_con += array_sum($val);
    }
    if($total_con == 0){
      return false;
    }
    $this->db->trans_start();
    $fee_student_schedule_id = $this->_update_concession_amount_student_sch_tb($std_sch_id, $total_con);

    $fee_ins_query = $this->_update_con_feev2_student_installments_table($concession_amount);

    $fee_ins_ids = $this->_update_con_feev2_student_installments_components($concession_amount);

    $fee_concessionId = $this->_insert_feev2_concessions($cohort_student_id, $concession_name);
    
    if (!empty($remove_pre_definedIds)){
      $sql1 = 'delete from  feev2_concessiontype2_pre_defined_concession where id in ('.$remove_pre_definedIds.')';
      $this->db->query($sql1);
      $this->db->where('feev2_concessionv2_id',$remove_pre_definedIds);
      $this->db->delete('feev2_concessionsv2_installment_components');
    }else{
      $fstdinsIds = array();
      foreach ($concession_amount as $stdInsIds => $val) {
        array_push($fstdinsIds, $stdInsIds);
      }
      
      $result = $this->db->select('fcpc.id')
      ->from('feev2_concessiontype2_pre_defined_concession fcpc')
      ->join('feev2_concessionsv2_installment_components fcic','fcpc.id=fcic.feev2_concessionv2_id')
      ->where('fcpc.cohort_student_id',$cohort_student_id)
      ->where_in('fcic.feev2_installments_id',$fstdinsIds)
      ->where('is_applied_status',0)
      ->get()->row();

      if(!empty($result)){
        $this->db->where('id',$result->id);
        $this->db->delete('feev2_concessiontype2_pre_defined_concession');

        $this->db->where('feev2_concessionv2_id',$result->id);
        $this->db->delete('feev2_concessionsv2_installment_components');
      }

      $insert_id = $this->_insert_pre_defined_concession_details_new($cohort_student_id, $total_con, $pre_defined_name, $pre_defined_con_amount, $concession_name);
      $this->_insert_pre_defined_concession_component_details($insert_id, $concession_amount,$feev2_blueprint_installment_types_id);
    }
    $this->db->trans_complete();
    return $this->db->trans_status();
    // if ($this->db->trans_status() === TRUE) {
    //   return TRUE;
    // }else{
    //   return FALSE;
    // }
  }

  public function _insert_pre_defined_concession_details_new($cohort_student_id, $total_con, $pre_defined_name, $pre_defined_con_amount, $concession_name){
    $data = array(
      'feev2_predefined_name' => (!empty($pre_defined_name))? $pre_defined_name:'Custom',
      'concession_amount' =>$total_con,
      'cohort_student_id' =>$cohort_student_id,
      'is_applied_status' =>0,
      'created_by' =>$this->authorization->getAvatarId(),
      'created_on' => $this->Kolkata_datetime(),
      'remarks' => $concession_name,
    );
    $this->db->insert('feev2_concessiontype2_pre_defined_concession',$data);
    return $this->db->insert_id();
  }

  public function _insert_pre_defined_concession_component_details($insert_id, $concession_amount, $feev2_blueprint_installment_types_id){
    $fstdinsConComp = array();
    foreach ($concession_amount as $stdInsIds => $val) {
      foreach ($val as $stdinsCompIds => $con_amount) {
        $fstdinsConComp[] = array(
          'feev2_concessionv2_id' => $insert_id,
          'feev2_blueprint_installment_types_id' => $feev2_blueprint_installment_types_id,
          'feev2_installments_id' => $stdInsIds,
          'feev2_blueprint_components_id' => $stdinsCompIds,
          'amount' => $con_amount,
        );
      }
    }
    return $this->db->insert_batch('feev2_concessionsv2_installment_components',$fstdinsConComp);  
  }

  public function save_fliter_names($input){
    $data = array(
      'saved_report_name'=> $input['title'],
      'master_report_name '=> 'daily_transaction_filter_selections',
      'filters_selected' => json_encode($input),
      'created_by' => $this->authorization->getAvatarStakeHolderId(),
      'created_on' =>$this->Kolkata_datetime()
    );
    return $this->db->insert('predefined_reports',$data);
  }

  public function save_fliter_names1($input){//fee_detail_report
    $data = array(
      'saved_report_name'=> $input['title'],
      'master_report_name '=> 'fee_detail_filter_selections',
      'filters_selected' => json_encode($input),
      'created_by' => $this->authorization->getAvatarStakeHolderId(),
      'created_on' =>$this->Kolkata_datetime()
    );
    return $this->db->insert('predefined_reports',$data);
  }
  
  public function save_fliter_names2($input){//balance_sms
    $data = array(
      'saved_report_name'=> $input['title'],
      'master_report_name '=> 'sms_report_filter_selections',
      'filters_selected' => json_encode($input),
      'created_by' => $this->authorization->getAvatarStakeHolderId(),
      'created_on' =>$this->Kolkata_datetime()
    );
    return $this->db->insert('predefined_reports',$data);
  }
  
  public function save_fliter_names3($input){//management_summary
    $data = array(
      'saved_report_name'=> $input['title'],
      'master_report_name '=> 'management_summary_filter_selections',
      'filters_selected' => json_encode($input),
      'created_by' => $this->authorization->getAvatarStakeHolderId(),
      'created_on' =>$this->Kolkata_datetime()
    );
    return $this->db->insert('predefined_reports',$data);
  }
  
  public function save_fliter_names4($input){//fee summary detail report
    $data = array(
      'saved_report_name'=> $input['title'],
      'master_report_name '=> 'student_fees_summary_v2_filter_selections',
      'filters_selected' => json_encode($input),
      'created_by' => $this->authorization->getAvatarStakeHolderId(),
      'created_on' =>$this->Kolkata_datetime()
    );
    return $this->db->insert('predefined_reports',$data);
  }
  
  public function save_fliter_names5($input){//management_day_wise_summary
    $data = array(
      'saved_report_name'=> $input['title'],
      'master_report_name '=> 'management_day_wise_summary',
      'filters_selected' => json_encode($input),
      'created_by' => $this->authorization->getAvatarStakeHolderId(),
      'created_on' =>$this->Kolkata_datetime()
    );
    return $this->db->insert('predefined_reports',$data);
  }

  public function update_fliter_names($input){
    $data = array(
      'master_report_name '=> 'daily_transaction_filter_selections',
      'filters_selected' => json_encode($input),
      'created_by' => $this->authorization->getAvatarStakeHolderId(),
      'created_on' =>$this->Kolkata_datetime()
    );
    $this->db->where('id',$input['filter_types_id']);
    return $this->db->update('predefined_reports',$data);
  }

  public function update_fliter_names1($input){//fee_detail_report
    $data = array(
      'master_report_name '=> 'fee_detail_filter_selections',
      'filters_selected' => json_encode($input),
      'created_by' => $this->authorization->getAvatarStakeHolderId(),
      'created_on' =>$this->Kolkata_datetime()
    );
    $this->db->where('id',$input['filter_types_id']);
    return $this->db->update('predefined_reports',$data);
  }

  public function update_fliter_names2($input){//balance_sms
    $data = array(
      'master_report_name '=> 'sms_report_filter_selections',
      'filters_selected' => json_encode($input),
      'created_by' => $this->authorization->getAvatarStakeHolderId(),
      'created_on' =>$this->Kolkata_datetime()
    );
    $this->db->where('id',$input['filter_types_id']);
    return $this->db->update('predefined_reports',$data);
  }

  public function update_fliter_names3($input){//management_summary
    $data = array(
      'master_report_name '=> 'management_summary_filter_selections',
      'filters_selected' => json_encode($input),
      'created_by' => $this->authorization->getAvatarStakeHolderId(),
      'created_on' =>$this->Kolkata_datetime()
    );
    $this->db->where('id',$input['filter_types_id']);
    return $this->db->update('predefined_reports',$data);
  }

  public function update_fliter_names4($input){//fee summary detail report
    $data = array(
      'master_report_name '=> 'student_fees_summary_v2_filter_selections',
      'filters_selected' => json_encode($input),
      'created_by' => $this->authorization->getAvatarStakeHolderId(),
      'created_on' =>$this->Kolkata_datetime()
    );
    $this->db->where('id',$input['filter_types_id']);
    return $this->db->update('predefined_reports',$data);
  }

  public function update_fliter_names5($input){//management_day_wise_summary
    $data = array(
      'master_report_name '=> 'management_day_wise_summary',
      'filters_selected' => json_encode($input),
      'created_by' => $this->authorization->getAvatarStakeHolderId(),
      'created_on' =>$this->Kolkata_datetime()
    );
    $this->db->where('id',$input['filter_types_id']);
    return $this->db->update('predefined_reports',$data);
  }

  public function save_column_filters($input){
    $data = array(
      'saved_report_name'=> $input['title'],
      'input_type'=> 'daily_transaction_column_selections',
      'filters_selected' => json_encode($input)
    );
    return $this->db->insert('predefined_reports',$data);
  }

  // public function get_concession_student_list_count($class_id, $concession_fee_type,$admission_no, $stdName, $fitler){

  //   $this->db->select('sa.id as student_id')
  //   ->from('feev2_cohort_student fcs')
  //   ->join('student_admission sa','fcs.student_id=sa.id')
  //   ->join('student_year sy',"sa.id=sy.student_admission_id and sy.acad_year_id = $this->yearId")
  //   ->join('class c','c.id=sy.class_id')
  //   ->join('class_section cs','cs.id=sy.class_section_id','left');
  //   if($concession_fee_type){
  //     $this->db->where('fcs.blueprint_id',$concession_fee_type);
  //   }
  //   if($class_id){
  //     $this->db->where_in('c.id',$class_id);
  //   }
  //   if($admission_no){
  //     $this->db->where('sa.admission_no',$admission_no);
  //   }
  //   if($stdName){
  //     $this->db->where("(LOWER(sa.first_name) like '%$stdName%' OR (LOWER(sa.last_name) like '%$stdName%'))");
  //   }
  //   if($this->current_branch) {
  //     $this->db->where('c.branch_id',$this->current_branch);
  //   }
  //   $this->db->order_by('c.id','asc');
  //   $result = $this->db->get()->result();
  //   // echo "<pre>a"; print_r($this->db->last_query());die();
  //   $stdIds = [];
  //   foreach ($result as $key => $res) {
  //     array_push($stdIds, $res->student_id);
  //   }
  //   return $stdIds;

  // }

  // public function get_fees_concession_data_by_student_id($student_ids, $concession_fee_type,$fitler){
  //   $constatus = '';
  //   if($fitler == 'pending-approval'){
  //     $constatus = 0;
  //   }
  //   $this->db->select("sa.id as student_id, concat(ifnull(sa.first_name,''),' ',ifnull(sa.last_name,'')) as student_name, concat(ifnull(c.class_name,''),'',ifnull(cs.section_name,'')) as class_name,  sa.admission_no, fss.id as std_sch_id, fcs.id as cohort_student_id")
  //   ->from('feev2_cohort_student fcs')
  //   ->join('feev2_student_schedule fss','fcs.id=fss.feev2_cohort_student_id')
  //   ->join('student_admission sa','fcs.student_id = sa.id')
  //   ->join('student_year sy',"sa.id=sy.student_admission_id and sy.acad_year_id = $this->yearId")
  //   ->join('class c','c.id=sy.class_id')
  //   ->join('class_section cs','cs.id=sy.class_section_id','left')
  //   ->join('feev2_concessiontype2_pre_defined_concession fcpdc','fcs.id =fcpdc.cohort_student_id ')
  //   ->where_in('sa.id',$student_ids);

  //   if($concession_fee_type){
  //     $this->db->where('fcs.blueprint_id',$concession_fee_type);
  //   }
  //   if($constatus == 0){
  //     $this->db->where('fcpdc.status',$constatus);
  //   }
  //   $this->db->order_by('c.id','asc');
  //   $studentData = $this->db->get()->result();

  //    $this->db->select("fcs.id as cohort_student_id, (case when fcpdc.is_applied_status = 1 then sum(fcpdc.concession_amount) else 0 end) as con_received, (case when fcpdc.is_applied_status = 0 then sum(fcpdc.concession_amount) else 0 end) as con_assinged, sum(fcpdc.concession_amount) as total_concesison, fcs.student_id, fcpdc.remarks, (case when fcpdc.status = 0 then 'Pending' else 'Approved' end) as con_status, fcpdc.approved_by, fcpdc.created_by, date_format(fcpdc.created_on,'%d-%m-%Y') as created_date, date_format(fcpdc.approved_date,'%d-%m-%Y') as approved_date, fcpdc.approved_remarks")
  //   ->from('feev2_concessiontype2_pre_defined_concession fcpdc')
  //   ->join('feev2_cohort_student fcs','fcpdc.cohort_student_id=fcs.id')
  //   ->where_in('fcs.student_id',$student_ids);

  //   if($constatus == 0){
  //     $this->db->where('fcpdc.status',$constatus);
  //   }
  //   if($concession_fee_type){
  //     $this->db->where('fcs.blueprint_id',$concession_fee_type);
  //   }
  //   $this->db->group_by('fcpdc.cohort_student_id');
  //   $concessionData =$this->db->get()->result();
    
  //   $conArry = [];
  //   foreach ($concessionData as $key => $val) {
  //     $conArry[$val->cohort_student_id] = $val;
  //   }

  //   foreach ($studentData as $key => &$val) {
  //     $val->created_name = '-';
  //     $val->approved_name = '-';
  //     $val->con_received = '-';
  //     $val->con_assinged = '-';
  //     $val->total_concesison = '-';
  //     $val->remarks = '-';
  //     $val->con_status = '-';
  //     $val->created_date = '-';
  //     $val->approved_date = '-';
  //     $val->approved_remarks = '-';
     
  //     if (array_key_exists($val->cohort_student_id, $conArry)){
  //       $val->created_name = $this->get_staff_name_from_avatar_id($conArry[$val->cohort_student_id]->created_by);
  //       $val->approved_name = $this->get_staff_name_from_avatar_id($conArry[$val->cohort_student_id]->approved_by);
  //       $val->con_received = $conArry[$val->cohort_student_id]->con_received;
  //       $val->con_assinged = $conArry[$val->cohort_student_id]->con_assinged;
  //       $val->total_concesison = $conArry[$val->cohort_student_id]->total_concesison;
  //       $val->remarks = $conArry[$val->cohort_student_id]->remarks;
  //       $val->con_status = $conArry[$val->cohort_student_id]->con_status;
  //       $val->created_date = $conArry[$val->cohort_student_id]->created_date;
  //       $val->approved_date = $conArry[$val->cohort_student_id]->approved_date;
  //       $val->approved_remarks = $conArry[$val->cohort_student_id]->approved_remarks;
  //     }
  //   }

  //   return $studentData;
   
  // }

  public function get_assing_fees_concession_details($concession_fee_type, $fitler, $class_id, $admission_no, $stdName){
    $constatus = '';
    if($fitler == 'pending-approval'){
      $constatus = 0;
    }

    $this->db->select("sa.id as student_id, concat(ifnull(sa.first_name,''),' ',ifnull(sa.last_name,'')) as student_name, concat(ifnull(c.class_name,''),'',ifnull(cs.section_name,'')) as class_name,  sa.admission_no")
    ->from('student_admission sa')
    ->join('student_year sy',"sa.id=sy.student_admission_id and sy.acad_year_id = $this->yearId")
    ->join('class c','c.id=sy.class_id')
    ->join('class_section cs','cs.id=sy.class_section_id','left');
    if($class_id){
      $this->db->where_in('c.id',$class_id);
    }
    if($admission_no){
      $this->db->where('sa.admission_no',$admission_no);
    }
    if($stdName){
      $this->db->where("(LOWER(sa.first_name) like '%$stdName%' OR (LOWER(sa.last_name) like '%$stdName%'))");
    }
    if($this->current_branch) {
      $this->db->where('c.branch_id',$this->current_branch);
    }
    $this->db->order_by('c.id','asc');
    $studentData = $this->db->get()->result();

    $studentIds = [];
    $studentArry = [];
    foreach ($studentData as $key => $val) {
      array_push($studentIds, $val->student_id);
      $studentArry[$val->student_id] = $val;
    }

    $this->db->select("fcs.student_id, fss.id as std_sch_id, fcs.id as cohort_student_id, fb.name as blueprint_name, fss.total_fee, fcs.exclude_dynamic_concession")
    ->from('feev2_cohort_student fcs')
    ->join('feev2_student_schedule fss','fcs.id=fss.feev2_cohort_student_id')
    ->join('feev2_blueprint fb','fcs.blueprint_id=fb.id')

    ->where_in('fcs.student_id',$studentIds);
    if($concession_fee_type){
      $this->db->where('fcs.blueprint_id',$concession_fee_type);
    }
    $fees_data =$this->db->get()->result();

    $cohort_student_ids = [];
    foreach ($fees_data as $key => $val) {
      array_push($cohort_student_ids, $val->cohort_student_id);
      if (array_key_exists($val->student_id, $studentArry)){
        $val->student_name = $studentArry[$val->student_id]->student_name;
        $val->class_name = $studentArry[$val->student_id]->class_name;
        $val->admission_no = $studentArry[$val->student_id]->admission_no;
      }
    }

    $this->db->select("fcpdc.cohort_student_id as cohort_student_id, (case when fcpdc.is_applied_status = 1 then sum(fcpdc.concession_amount) else 0 end) as con_received, (case when fcpdc.is_applied_status = 0 then sum(fcpdc.concession_amount) else 0 end) as con_assinged, sum(fcpdc.concession_amount) as total_concesison,  (case when fcpdc.status = 0 then 'Pending' else 'Approved' end) as con_status")
    ->from('feev2_concessiontype2_pre_defined_concession fcpdc')
    ->where_in('fcpdc.cohort_student_id',$cohort_student_ids);
    if($constatus == 0){
      $this->db->where('fcpdc.status',$constatus);
    }
    $this->db->group_by('fcpdc.cohort_student_id');
    $fees_con_data =$this->db->get()->result();
    $conData = [];
    foreach ($fees_con_data as $key => $value) {
      $conData[$value->cohort_student_id] = $value;
    }
    foreach ($fees_data as $key => $val) {
      $val->con_received = '-';
      $val->con_assinged = '-';
      $val->total_concesison = '-';
      $val->con_status = '-';
      if (array_key_exists($val->cohort_student_id, $conData)){
        $val->con_received = $conData[$val->cohort_student_id]->con_received;
        $val->con_assinged = $conData[$val->cohort_student_id]->con_assinged;
        $val->total_concesison = $conData[$val->cohort_student_id]->total_concesison;
        $val->con_status = $conData[$val->cohort_student_id]->con_status;
      }
    }
    return $fees_data;
  }

  private function get_staff_name_from_avatar_id($avatarId) {
    $collected = $this->db_readonly->select('CONCAT(ifnull(sm.first_name," "), " ", ifnull(sm.last_name," ")) as staffName')
        ->from('staff_master sm')
        ->join('avatar a', 'sm.id=a.stakeholder_id')
        ->where('a.avatar_type', '4') // 4 avatar type staff        
        ->where('a.id',$avatarId)
        ->get()->row();
    if (!empty($collected)) {
      return $collected->staffName;
    }else{
      return 'Admin';
    }
  }


  public function update_concession_amount_v3($std_sch_id, $concession_amount=[], $cohort_student_id, $feev2_blueprint_installment_types_id, $concession_name, $concession_amount_add =[], $pre_defined_name, $pre_defined_con_amount, $remove_pre_definedIds){

    $total_con = 0;
    foreach ($concession_amount as $key => $val) {
      $total_con += array_sum($val);
    }
    if($total_con == 0){
      return false;
    }
    $isConcessionApprovedFlowTrue = $this->settings->getSetting('fees_concession_approval_flow');

    $this->db->trans_start();
    if(!$isConcessionApprovedFlowTrue){
      $fee_student_schedule_id = $this->_update_concession_amount_student_sch_v1($std_sch_id, $total_con);

      $fee_ins_query = $this->_update_con_feev2_student_installments_table_v1($concession_amount);
  
      $fee_ins_ids = $this->_update_con_feev2_student_installments_components_v1($concession_amount);
  
      $fee_concessionId = $this->_insert_feev2_concessions($cohort_student_id, $concession_name);
    } 
    if (!empty($remove_pre_definedIds)){
      $sql1 = 'delete from  feev2_concessiontype2_pre_defined_concession where id in ('.$remove_pre_definedIds.')';
      $this->db->query($sql1);
      $this->db->where('feev2_concessionv2_id',$remove_pre_definedIds);
      $this->db->delete('feev2_concessionsv2_installment_components');
    }else{
      $insert_id = $this->_insert_pre_defined_concession_details_new($cohort_student_id, $total_con, $pre_defined_name, $pre_defined_con_amount, $concession_name);
      
      $this->_insert_pre_defined_concession_component_details($insert_id, $concession_amount,$feev2_blueprint_installment_types_id);
    }
    $this->db->trans_complete();
    return $this->db->trans_status();
  }

  private function _update_concession_amount_student_sch_v1($std_sch_id, $total_con, $isTrue = 1){
    $result = $this->db->select('total_concession_amount')
    ->from('feev2_student_schedule')
    ->where('id',$std_sch_id)
    ->get()->row();
    $total_concession_amount = 0;
    if(!empty($result)){
      $total_concession_amount = $result->total_concession_amount;
    }
    $fstdSchd = array(
      'total_concession_amount'=>($isTrue)? $total_concession_amount + $total_con : $total_concession_amount - $total_con
    );
    $this->db->where('id', $std_sch_id);
    return $this->db->update('feev2_student_schedule',$fstdSchd);
  }

  private function _update_con_feev2_student_installments_table_v1($concession_amount, $isTrue = 1){
    $fstdins = array();
    foreach ($concession_amount as $stdInsIds => $val) {
      $result = $this->db->select('total_concession_amount')
      ->from('feev2_student_installments')
      ->where('id',$stdInsIds)
      ->get()->row();
      $total_concession_amount = 0;
      if(!empty($result)){
        $total_concession_amount = $result->total_concession_amount;
      }
      $conAmount = array_sum($val);
      $fstdins[] = array(
        'id'=>$stdInsIds,
        'total_concession_amount' => ($isTrue)? $total_concession_amount + $conAmount : $total_concession_amount - $conAmount
      );
    }

    return $this->db->update_batch('feev2_student_installments',$fstdins,'id');
  }

  private function _update_con_feev2_student_installments_components_v1($concession_amount, $isTrue = 1){
    $fstdinsComp = array();
    foreach ($concession_amount as $stdInsIds => $val) {
      foreach ($val as $stdinsCompIds => $con_amount) {
        $result = $this->db->select('concession_amount')
        ->from('feev2_student_installments_components')
        ->where('id',$stdinsCompIds)
        ->get()->row();
        $total_concession_amount = 0;
        if(!empty($result)){
          $total_concession_amount = $result->concession_amount;
        }
        $con_amount = (empty($con_amount)) ? 0 : $con_amount;

        $fstdinsComp[] = array(
          'id' => $stdinsCompIds,
          'concession_amount' => ($isTrue)? $total_concession_amount + $con_amount : $total_concession_amount - $con_amount
        );
      }
    }
    return $this->db->update_batch('feev2_student_installments_components',$fstdinsComp,'id');  
  }

  public function remove_applied_concession_by_ids($preConId){
    $result = $this->db->select('feev2_installments_id,feev2_blueprint_components_id,amount')
    ->from('feev2_concessionsv2_installment_components')
    ->where('feev2_concessionv2_id',$preConId)
    ->get()->result();
    if(empty($result)){
      return false;
    }
    $schStudent = $this->db->select('fss.id as std_sch_id, fcpc.status')
    ->from('feev2_concessiontype2_pre_defined_concession fcpc')
    ->join('feev2_cohort_student fcs','fcpc.cohort_student_id=fcs.id')
    ->join('feev2_student_schedule fss','fcs.id=fss.feev2_cohort_student_id')
    ->where('fcpc.id',$preConId)
    ->get()->row();
    if(empty($schStudent)){
      return false;
    }
    $totalCon = 0;
    $concessionComp = [];
    foreach ($result as $key => $val) {
      $totalCon += $val->amount;
      $concessionComp[$val->feev2_installments_id][$val->feev2_blueprint_components_id] = $val->amount;
    }
    
    $this->db->trans_start();
    $isConcessionApprovedFlowTrue = $this->settings->getSetting('fees_concession_approval_flow');
    if(!$isConcessionApprovedFlowTrue && $schStudent->status == 0){
      $fee_student_schedule_id = $this->_update_concession_amount_student_sch_v1($schStudent->std_sch_id, $totalCon, false);
      $fee_ins_query = $this->_update_con_feev2_student_installments_table_v1($concessionComp, false);
      $fee_ins_ids = $this->_update_con_feev2_student_installments_components_v1($concessionComp, false);
    }
    $sql1 = 'delete from  feev2_concessiontype2_pre_defined_concession where id in ('.$preConId.')';
    $this->db->query($sql1);
    $this->db->where('feev2_concessionv2_id',$preConId);
    $this->db->delete('feev2_concessionsv2_installment_components');
    
    $this->db->trans_complete();
    return $this->db->trans_status();
    
  }

  public function update_concession_details_by_id($predefinedId, $remarks){
    $this->db->trans_start();

    $result = $this->db->select('feev2_installments_id,feev2_blueprint_components_id,amount')
    ->from('feev2_concessionsv2_installment_components')
    ->where('feev2_concessionv2_id',$predefinedId)
    ->get()->result();

    if(empty($result)){
      return false;
    }

    $schStudent = $this->db->select('fss.id as std_sch_id, fcpc.status')
    ->from('feev2_concessiontype2_pre_defined_concession fcpc')
    ->join('feev2_cohort_student fcs','fcpc.cohort_student_id=fcs.id')
    ->join('feev2_student_schedule fss','fcs.id=fss.feev2_cohort_student_id')
    ->where('fcpc.id',$predefinedId)
    ->get()->row();

    if(empty($schStudent)){
      return false;
    }
    $totalCon = 0;
    $concessionComp = [];
    foreach ($result as $key => $val) {
      $totalCon += $val->amount;
      $concessionComp[$val->feev2_installments_id][$val->feev2_blueprint_components_id] = $val->amount;
    }
    
    $isConcessionApprovedFlowTrue = $this->settings->getSetting('fees_concession_approval_flow');

    if($isConcessionApprovedFlowTrue && $schStudent->status != 1){
    
      $fee_student_schedule_id = $this->_update_concession_amount_student_sch_v1($schStudent->std_sch_id, $totalCon, true);
      $fee_ins_query = $this->_update_con_feev2_student_installments_table_v1($concessionComp, true);
      $fee_ins_ids = $this->_update_con_feev2_student_installments_components_v1($concessionComp, true);
    }
    $data = array(
      'status' => 1,
      'approved_date' =>$this->Kolkata_datetime(),
      'approved_by' => $this->authorization->getAvatarId(),
      'approved_remarks' => $remarks,
    );
    $this->db->where('id',$predefinedId);
    $this->db->update('feev2_concessiontype2_pre_defined_concession',$data);

    $this->db->trans_complete();
    return $this->db->trans_status();

  }

  public function get_fee_structure_for_filter_amount($std_id, $blueprintIds){
    if(empty($blueprintIds)){
      return false;
    }
   
    $bpIds = $this->db->select('id, name, concession_mode,concession_algo, acad_year_id')
    ->from('feev2_blueprint')
    ->where_in('id',$blueprintIds)
    ->get()->result();
    if(empty($bpIds)){
      return false;
    }
    $bpCohorts = [];
    foreach ($bpIds as $key => $bpid) {
      $student_data = $this->get_std_detailsbyId($std_id, $bpid->acad_year_id);

      $cohorts = $this->get_cohorts_details($bpid->id, $this->yearId);
      $cohort_id = $this->determine_cohort($bpid->id, $student_data);
      $bpCohorts[$bpid->id] = array();
      foreach ($cohorts as $key => $val) {
        $obj = new stdClass();
        $obj->cohort_id = $val->id;
        $obj->total_fee = $val->total_fee;
        $obj->friendly_name = $val->friendly_name;
        $obj->blueprint_name = $bpid->name;
        $obj->concession_mode = $bpid->concession_mode;
        $obj->concession_algo = $bpid->concession_algo;
        $obj->selected = 0;
        if($cohort_id == $val->id){
          $obj->cohort_id = $cohort_id;
          $obj->total_fee = $val->total_fee;
          $obj->friendly_name = $val->friendly_name;
          $obj->selected = 1;
          $obj->blueprint_name = $bpid->name;
          $obj->concession_mode = $bpid->concession_mode;
          $obj->concession_algo = $bpid->concession_algo;
        }
        $bpCohorts[$bpid->id][] = $obj;
      }
    }
    return $bpCohorts;
  }

  public function get_fee_structure($student_id){
    $bpIds = $this->db->select('id, name, concession_mode,concession_algo, acad_year_id')
    ->from('feev2_blueprint')
    ->where('acad_year_id',$this->yearId)
    ->get()->result();
   
    if(empty($bpIds)){
      return false;
    }
    $bpCohorts = [];
    foreach ($bpIds as $key => $bpid) {
      $student_data = $this->get_std_detailsbyId($student_id, $bpid->acad_year_id);

      $cohorts = $this->get_cohorts_details($bpid->id, $this->yearId);
      $cohort_id = $this->determine_cohort($bpid->id, $student_data);
      $bpCohorts[$bpid->id] = array();
      foreach ($cohorts as $key => $val) {
        $obj = new stdClass();
        $obj->cohort_id = $val->id;
        $obj->total_fee = $val->total_fee;
        $obj->friendly_name = $val->friendly_name;
        $obj->blueprint_name = $bpid->name;
        $obj->concession_mode = $bpid->concession_mode;
        $obj->concession_algo = $bpid->concession_algo;
        $obj->selected = 0;
        if($cohort_id == $val->id){
          $obj->cohort_id = $cohort_id;
          $obj->total_fee = $val->total_fee;
          $obj->friendly_name = $val->friendly_name;
          $obj->selected = 1;
          $obj->blueprint_name = $bpid->name;
          $obj->concession_mode = $bpid->concession_mode;
          $obj->concession_algo = $bpid->concession_algo;
        }
        $bpCohorts[$bpid->id][] = $obj;
      }
    }
    return $bpCohorts;
  }

  public function get_cohorts_details($bpId, $yearId){
    $result = $this->db->select('fc.id, fc.total_fee, fc.friendly_name')
    ->from('feev2_cohorts fc')
    ->where('acad_year_id',$yearId)
    ->where('blueprint_id',$bpId)
    ->get()->result();
    return $result;
  }

  public function insert_cohort_details_mass_assign($blueprint_id, $cohort_id, $cohort_status, $isntallment_type_id, $comp_amount=[], $un_assinged_concession_amount=[], $student_id, $concession_name, $fine_amount, $insert_into_concession = 1, $pay_date ='', $concession_remarks){

    $query = $this->db->query("select * from feev2_cohort_student where student_id = $student_id and blueprint_id = $blueprint_id");

    if($query->num_rows() >= 1){
      return FALSE;
    }
  
    $total_fee = 0;
    foreach ($comp_amount as $key => $val) {
      $total_fee += array_sum($val);
    }
    
    $this->db->trans_start();
    //TODO: Make the following 3 inserts as stored procedure

    $isConcessionApprovedFlowTrue = $this->settings->getSetting('fees_concession_approval_flow');
    if($isConcessionApprovedFlowTrue){
      $total_con = 0;
      $concession_amount = [];
      foreach ($un_assinged_concession_amount as $insId => $comp) {
        foreach ($comp as $comId => $value) {
          $concession_amount[$insId][$comId] = 0;
        }
      }
    }else{
      $total_con = 0;
      foreach ($un_assinged_concession_amount as $key => $val) {
        $total_con += array_sum($val);
      }
      $concession_amount = $un_assinged_concession_amount;
    }
  
    $cohort_student_id = $this->_insert_confirm_std_fee_cohort($student_id, $blueprint_id, $cohort_id, $cohort_status, 'COHORT_CONFIRM', $pay_date);

    $fee_student_schedule_id = $this->_insert_feev2_student_schedule_table($cohort_student_id, $isntallment_type_id, $total_fee,$total_con, $fine_amount);

    $fee_ins_query = $this->_insert_feev2_student_installments_table($fee_student_schedule_id, $comp_amount, $concession_amount, $fine_amount);

    $fee_ins_ids = $this->_insert_feev2_student_installments_components($fee_student_schedule_id, $fee_ins_query, $comp_amount, $concession_amount);

    $total_con_unassinged = 0;
    foreach ($un_assinged_concession_amount as $key => $val) {
      $total_con_unassinged += array_sum($val);
    }

    if($total_con_unassinged !=0){
      $fee_concessionId = $this->_insert_pre_defined_concession_details_new($cohort_student_id, $total_con_unassinged, $concession_name, '', $concession_remarks);
      $studentInsIds = [];
      foreach ($fee_ins_query as $key => $value) {
        array_push($studentInsIds, $value->id);
      }
      $result = $this->db->select('fsic.id as compId, fsi.id as fee_student_installment_id, concession_amount, fsi.feev2_installments_id, fsic.blueprint_component_id')
      ->from('feev2_student_installments fsi')
      ->join('feev2_student_installments_components fsic','fsi.id=fsic.fee_student_installment_id')
      ->where_in('fsi.id',$studentInsIds)
      ->get()->result();
      
      $assinged_concessionAmount = [];
      foreach ($result as $key => $val) {
        $assinged_concessionAmount[$val->fee_student_installment_id][$val->compId] = $un_assinged_concession_amount[$val->feev2_installments_id][$val->blueprint_component_id];
      }
      $this->_insert_pre_defined_concession_component_details($fee_concessionId, $assinged_concessionAmount, $isntallment_type_id);
    }
    
    $this->db->trans_complete();
    if ($this->db->trans_status()) {
      return array('std_sch_id'=>$fee_student_schedule_id, 'cohort_student_id'=>$cohort_student_id);
    }else{
      return FALSE;
    }
  }

  public function get_std_fee_for_installment_breakup_iish($std_id) {

    $result =  $this->db->select("fcs.id as cohort_student_id, fcs.id as feev2_cohort_student_id, fcs.fee_collect_status, fcs.fee_cohort_status, fss.id as std_sch_id, fb.name as blueprint_name, fbc.name as component_name, sum(fsic.component_amount) as component_amount, fsic.concession_amount as concession_amount, sum(ifnull(component_amount_paid,0)) as component_paid,  sum(fsi.installment_amount) as installment_amount, sum(ifnull(fsic.concession_amount_paid,0)) as concession_amount_paid, fi.name as installment_name, fi.name as ins_name, concat(ifnull(fi.name,''), ' Due by ' ,ifnull(date_format(fi.end_date,'%D %b %Y') ,'')) as ins_date")
      ->from('feev2_blueprint fb')
      ->join('feev2_cohort_student fcs','fcs.blueprint_id=fb.id')
      ->where('fcs.student_id',$std_id)
      ->where('fb.acad_year_id',$this->yearId)
      ->join('feev2_student_schedule fss','fcs.id=fss.feev2_cohort_student_id')
      ->join('feev2_student_installments fsi','fss.id=fsi.fee_student_schedule_id')
      ->join('feev2_student_installments_components fsic','fsic.fee_student_installment_id=fsi.id')
      ->join('feev2_blueprint_components fbc','fbc.id=fsic.blueprint_component_id')
      ->join('feev2_installments fi','fsi.feev2_installments_id=fi.id')
      ->group_by('fsic.id')
      ->get()->result();
      // echo "<pre>"; print_r($result);die();
      $bpAmount = [];
      foreach ($result as $key => $val) {
        $bpAmount[$val->ins_date][$val->component_name] = $val->component_amount - $val->component_paid - $val->concession_amount - $val->concession_amount_paid;
      } 
      return $bpAmount;
  }

  public function update_adjustment_amount_from_terminate_student($std_sch_id, $adjustment_amount=[], $cohort_student_id,  $adjustment_remarks){

    $total_adjust = 0;
    foreach ($adjustment_amount as $key => $val) {
      $total_adjust += array_sum($val);
    }
    $this->db->trans_start();
    $fee_student_schedule_id = $this->_update_adjustment_amount_student_sch_tb($std_sch_id, $total_adjust);

    $fee_ins_query = $this->_update_adjustment_feev2_student_installments_table($adjustment_amount);

    $fee_ins_ids = $this->_update_adjustment_feev2_student_installments_components($adjustment_amount);

    $fee_concessionId = $this->_insert_feev2_adjustment($cohort_student_id, $adjustment_remarks);

    $this->db->trans_complete();
    return $this->db->trans_status();
  }

  public function download_invoice_statment_pdf($invoice_statement_id){
    return $this->db->select('fis.invoice_path')
    ->from('feev2_invoice_student fis')
    ->where('fis.id',$invoice_statement_id)
    ->get()->row();
  }

  public function class_wise_statemnet_student_data_fee_byId($input){
    $this->db_readonly->select('sa.id as stdId')
    ->from('student_admission sa')
    ->where('sa.admission_status','2')
    ->join('student_year sy','sa.id=sy.student_admission_id')
    ->join('class c','sy.class_id=c.id')
    ->join('class_section cs','sy.class_section_id=cs.id')
    ->where('sy.promotion_status!=', '4')
    ->where('sy.promotion_status!=', '5')
    ->where('sy.promotion_status!=', 'JOINED')
    ->where('sy.acad_year_id',$this->yearId);    
    if(isset($input['classSectionId'])) {
      $this->db_readonly->where_in('sy.class_id',$input['classSectionId']);
    }
    if(isset($input['stdName'])) {
      $stdName = $input['stdName'];
      $this->db_readonly->where("(LOWER(sa.first_name) like '%$stdName%' OR (LOWER(sa.last_name) like '%$stdName%'))");
    }
    if(isset($input['admission_no'])) {
      $this->db_readonly->where('sa.admission_no',$input['admission_no']);
    }
    if($this->current_branch) {
      $this->db_readonly->where('c.branch_id',$this->current_branch);
    }
    $this->db_readonly->order_by('c.id','sa.first_name');
    $result = $this->db_readonly->get()->result();
    $stdIds = [];
    foreach ($result as $key => $res) {
      array_push($stdIds, $res->stdId);
    }
    return $stdIds;
  }

  public function get_single_window_data($std_id){
    return $this->db->select("swpt.status,date_format(taken_on,'%d-%M-%Y') as taken_on,concat(ifnull(sm.first_name,''),' ',ifnull(sm.last_name,'')) as taken_by,remarks")
        ->from('single_window_approval_tracking swpt')
        ->join('staff_master sm','swpt.taken_by=sm.id','left')
        ->where('student_id',$std_id)
        ->where('team_name','Accounts')
        ->where('academic_year_id',$this->acad_year->getAcadYearId())
        ->get()->row();
  }

  public function get_student_details_fees_dispaly($stdId,$student_acad_year_id){
    return $this->db_readonly->select("concat(ifnull(sd.first_name,''),' ',ifnull(sd.last_name,'')) as stdName, sd.admission_no, c.class_name as className, cs.section_name, sy.board, sy.boarding, sy.medium, sd.category, sy.is_rte, sd.sibling_type as has_sibling")
      ->from('student_year sy')
      ->where('sy.promotion_status!=', '4')
      ->where('sy.promotion_status!=', '5')
      ->where('sd.id',$stdId)
      ->where('sy.acad_year_id',$student_acad_year_id)
      ->join('student_admission sd','sy.student_admission_id=sd.id')
      // ->where('sd.admission_status','2')
      ->join('student_health sh','sh.student_id=sd.id','left')
      ->join('class c','sy.class_id=c.id')
      ->join('class_section cs','sy.class_section_id=cs.id')
      ->get()->row();
    }
  
    public function get_blueprints_data_v2(){
      $years =array($this->yearId, ($this->yearId-1));
      $this->db->select('id, name, acad_year_id');
      $this->db->from('feev2_blueprint');
      if($this->current_branch) {
        $this->db->where('branches',$this->current_branch);
      }
      $this->db->order_by('acad_year_id');
      $this->db->where_in('acad_year_id',$years);
      return $this->db->get()->result();
    }
  
    public function get_all_blueprint_fees_details($student_id){
      
      $years = array($this->yearId, ($this->yearId - 1), ($this->yearId - 2));
      
      $this->db->select('id, name, acad_year_id');
      $this->db->from('feev2_blueprint');
      // $this->db->where_in('id',array('33','32'));
      if ($this->current_branch) {
          $this->db->where('branches', $this->current_branch);
      }
      $this->db->order_by('acad_year_id');
      $this->db->where_in('acad_year_id', $years);
      $bp_data = $this->db->get()->result();
      if(empty($bp_data)){
        return false;
      }
      $bpIds = [];
      foreach ($bp_data as $key => $val) {
          array_push($bpIds, $val->id);
      }
      // Fetch fee details
      $this->db->select('fcs.blueprint_id, fcs.id as cohort_student_id, fcs.fee_collect_status, fcs.publish_status, fcs.online_payment,
          ifnull(fss.total_fee, 0) as total_fee, fss.id as std_sch_id, ifnull(fss.total_fee_paid, 0) as total_fee_paid, fss.payment_status,
          fss.total_card_charge_amount, sum(ifnull(fsi.total_concession_amount, 0)) as total_concession_amount,
          sum(ifnull(fsi.total_concession_amount_paid, 0)) as total_concession_amount_paid, sum(ifnull(fsi.total_adjustment_amount, 0)) as total_adjustment_amount,
          sum(ifnull(fsi.total_adjustment_amount_paid, 0)) as total_adjustment_amount_paid, sum(ifnull(fsi.total_fine_amount, 0)) as total_fine_amount,
          sum(ifnull(fsi.total_fine_amount_paid, 0)) as total_fine_amount_paid, sum(ifnull(fsi.total_fine_waived, 0)) as total_fine_waived,
          sum(ifnull(fsi.refund_amount, 0)) as refund_amount, 
          (ifnull(fss.total_fee, 0) - ifnull(fss.total_fee_paid, 0) - ifnull(fss.total_concession_amount, 0) - ifnull(fss.total_concession_amount_paid, 0)) as balance, ifnull(fss.discount, 0) as discount');
      $this->db->from('feev2_cohort_student fcs');
      $this->db->join('feev2_student_schedule fss', 'fcs.id=fss.feev2_cohort_student_id', 'left');
      $this->db->join('feev2_student_installments fsi', 'fss.id=fsi.fee_student_schedule_id', 'left');
      $this->db->where('fcs.student_id', $student_id);
      $this->db->where_in('fcs.blueprint_id', $bpIds);
      $this->db->group_by('fcs.blueprint_id');
      $fee_details = $this->db->get()->result();
      $reconciliation = $this->db->select("fcs.blueprint_id, ft.id as lastTransId, reconciliation_status, receipt_number, amount_paid, payment_type, bank_name, bank_branch, card_reference_number, cheque_dd_nb_cc_dd_number, case reconciliation_status when '1' then 1 else 0 end as recon_status")
      ->from('feev2_cohort_student fcs')
      ->join('feev2_student_schedule fss','fcs.id=fss.feev2_cohort_student_id')
      ->join('feev2_transaction ft','fss.id=ft.fee_student_schedule_id')
      ->join('feev2_transaction_payment ftp','ft.id=ftp.fee_transaction_id')
      ->where('fcs.student_id',$student_id)
      ->where_in('fcs.blueprint_id',$bpIds)
      ->order_by('ft.id','desc')
      ->get()->result();
  
      // Fetch transaction data
      if(!empty($fee_details)){
        $this->db->select('ft.fee_student_schedule_id, sum(ft.amount_paid) as trans_amount');
        $this->db->from('feev2_transaction ft');
        $this->db->where_in('ft.fee_student_schedule_id', array_column($fee_details, 'std_sch_id'));
        $this->db->where('ft.soft_delete !=', 1);
        $this->db->where('ft.status', 'SUCCESS');
        $this->db->where('ft.acad_year_id', $this->yearId);
        $this->db->group_by('ft.fee_student_schedule_id');
        $transaction_data = $this->db->get()->result();
      }
  
  
      // Fetch concession data if necessary
      $isConcessionApprovedFlowTrue = $this->settings->getSetting('fees_concession_approval_flow');
      if ($isConcessionApprovedFlowTrue) {
          $this->db->select('fcs.blueprint_id, fcpdc.status, fcpdc.concession_amount');
          $this->db->from('feev2_cohort_student fcs');
          $this->db->join('feev2_concessiontype2_pre_defined_concession fcpdc', 'fcs.id=fcpdc.cohort_student_id', 'left');
          $this->db->where('fcs.student_id', $student_id);
          $this->db->where_in('fcs.blueprint_id', $bpIds);
          $this->db->where('fcpdc.status', 0);
          $concession_data = $this->db->get()->result();
      }
  
      $fee_details_map = [];
      if(!empty($fee_details)){
        foreach ($fee_details as $fd) {
          $fee_details_map[$fd->blueprint_id] = $fd;
        }
      }
  
      
      $reconciliation_map = [];
      if(!empty($reconciliation)){
        foreach ($reconciliation as $rec) {
          if($rec->reconciliation_status == 1){
            $reconciliation_map[$rec->blueprint_id] = $rec;
          }
        }
      }
      $transaction_map = [];
      if(!empty($transaction_data)){
        foreach ($transaction_data as $td) {
          $transaction_map[$td->fee_student_schedule_id] = $td;
        }
      }
      
      foreach ($fee_details_map as $key => $val) {
        $val->opening_balance = isset($transaction_map[$val->std_sch_id]) ? $val->total_fee_paid - $transaction_map[$val->std_sch_id]->trans_amount : 0;
      }
  
      
      $concession_map = [];
      if ($isConcessionApprovedFlowTrue) {
        if(!empty($concession_data)){
          foreach ($concession_data as $cd) {
            $concession_map[$cd->blueprint_id] = $cd;
          }
        }
      }      
      foreach ($bp_data as $key => $val) {
        $val->std_fee_details = isset($fee_details_map[$val->id]) ? $fee_details_map[$val->id] : [];
        $val->reconciliation = isset($reconciliation_map[$val->id]) ? $reconciliation_map[$val->id] : [];
        $val->opening_balance = isset($fee_details_map[$val->id]) ? $fee_details_map[$val->id]->opening_balance : 0; 
        $val->isConcessionPending = isset($concession_map[$val->id]) ? $concession_map[$val->id] : '';
      }
      return $bp_data;
      
    }

    public function get_fees_std_detailsbyId($stdId, $fee_acad_year_id){
      $this->db->select("concat(ifnull(sd.first_name,''),' ',ifnull(sd.last_name,'')) as stdName, sd.admission_no, c.class_name as className, sy.is_rte, sd.has_staff as has_staff, sd.sibling_type as has_sibling, sd.life_time_fee_mode as is_lifetime_student, admission_status, sd.admission_form_id ,enrollment_number")
      ->from('student_year sy')
      ->where('sd.id',$stdId)
      ->where('sy.acad_year_id',$fee_acad_year_id)
      ->join('student_admission sd','sy.student_admission_id=sd.id')
      ->join('class c','sy.class_id=c.id');
      return $this->db->get()->row();
    }

    public function remove_applied_concession_admin_by_ids($preConId){
      $result = $this->db->select('feev2_installments_id,feev2_blueprint_components_id,amount')
      ->from('feev2_concessionsv2_installment_components')
      ->where('feev2_concessionv2_id',$preConId)
      ->get()->result();
      if(empty($result)){
        return false;
      }
      $schStudent = $this->db->select('fss.id as std_sch_id, fcpc.status')
      ->from('feev2_concessiontype2_pre_defined_concession fcpc')
      ->join('feev2_cohort_student fcs','fcpc.cohort_student_id=fcs.id')
      ->join('feev2_student_schedule fss','fcs.id=fss.feev2_cohort_student_id')
      ->where('fcpc.id',$preConId)
      ->get()->row();
      if(empty($schStudent)){
        return false;
      }
      $totalCon = 0;
      $concessionComp = [];
      foreach ($result as $key => $val) {
        $totalCon += $val->amount;
        $concessionComp[$val->feev2_installments_id][$val->feev2_blueprint_components_id] = $val->amount;
      }
      
      $this->db->trans_start();
      $isSuperAdmin = $this->authorization->isSuperAdmin();
      if($isSuperAdmin){
        $fee_student_schedule_id = $this->_update_concession_amount_student_sch_v1($schStudent->std_sch_id, $totalCon, false);
        $fee_ins_query = $this->_update_con_feev2_student_installments_table_v1($concessionComp, false);
        $fee_ins_ids = $this->_update_con_feev2_student_installments_components_v1($concessionComp, false);
      }
  
      $sql1 = 'delete from  feev2_concessiontype2_pre_defined_concession where id in ('.$preConId.')';
      $this->db->query($sql1);
      $this->db->where('feev2_concessionv2_id',$preConId);
      $this->db->delete('feev2_concessionsv2_installment_components');
      
      $this->db->trans_complete();
      return $this->db->trans_status();
      
    }

    public function get_stud_data($std_id){
      $result = $this->db->select("CASE WHEN sy.admission_type = 1 THEN 'Re-admission' WHEN sy.admission_type = 2 THEN 'New Admission' ELSE '' END AS admission_type,sy.boarding,sy.is_rte,CASE WHEN sa.has_transport = 1 THEN 'Yes' WHEN sa.has_transport = 0 THEN 'No' ELSE '' END AS has_transport")
      ->from('student_admission sa')
      ->join('student_year sy','sa.id=sy.student_admission_id')
      ->where('sa.id',$std_id)
      ->where('sy.acad_year_id',$this->acad_year->getAcadYearId())
      ->get()->row();

      if(!empty($result->boarding)){
        $result->boarding = $this->settings->getSetting('boarding')[$result->boarding];
      }
      if(!empty($result->is_rte)){
        $result->is_rte = $this->settings->getSetting('rte')[$result->is_rte];
      }
      return $result;
    }

    public function check_cohort_student_id($blueprint_id, $stdId){
      $result = $this->db->select('fcs.id')
      ->from('feev2_cohort_student fcs')
      ->where('student_id',$stdId)
      ->where('blueprint_id',$blueprint_id)
      ->get()->row();
      if (empty($result))
        return 0;
      else
        return $result->id;
    }
    public function get_class_wsie_details(){
      $this->db_readonly->select('c.id');
      $this->db_readonly->from('class c');
      $this->db_readonly->where('c.acad_year_id', $this->yearId);
      if($this->current_branch) {
        $this->db_readonly->where('c.branch_id', $this->current_branch);
      }
      $this->db_readonly->order_by('c.display_order');
      return $this->db_readonly->get()->result();
    }

}


