<?php
defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * Staff Login Logger Library
 * 
 * This library handles comprehensive logging of staff login activities
 * including IP tracking, session management, and user agent parsing
 * without affecting existing CodeIgniter functionality.
 * 
 * @package    CodeIgniter
 * @subpackage Libraries
 * @category   Authentication
 * <AUTHOR> Name
 * @version    1.0
 */
class Staff_login_logger {

    protected $CI;
    protected $config;
    protected $table_name = 'staff_login_logs';
    protected $config_table = 'staff_login_config';

    public function __construct($params = array()) {
        $this->CI =& get_instance();
        $this->CI->load->database();
        $this->CI->load->library('user_agent');
        $this->CI->load->library('session');

        // Load configuration
        $this->load_config();

        log_message('debug', 'Staff Login Logger Library Initialized');
    }

    /**
     * Load configuration from database
     */
    private function load_config() {
        $query = $this->CI->db->get($this->config_table);
        $this->config = array();
        
        if ($query->num_rows() > 0) {
            foreach ($query->result() as $row) {
                $this->config[$row->config_key] = $row->config_value;
            }
        }
        
        // Set default values if not found
        $defaults = array(
            'log_retention_days' => 90,
            'track_ip_location' => 1,
            'log_user_agent_details' => 1,
            'auto_close_inactive_sessions' => 1,
            'session_timeout_minutes' => 1440,
            'enable_concurrent_session_limit' => 0,
            'max_concurrent_sessions' => 3,
            'use_external_ip_services' => 1
        );
        
        foreach ($defaults as $key => $value) {
            if (!isset($this->config[$key])) {
                $this->config[$key] = $value;
            }
        }
    }

    /**
     * Log user login event
     *
     * @param int $user_id User ID
     * @param string $username Username
     * @param string $login_method Login method (web, mobile, api)
     * @return int|bool Insert ID on success, FALSE on failure
     */
    public function log_login($user_id, $username, $login_method = 'web') {
        try {
            // Check if staff login logging is enabled
            if (!$this->is_staff_login_logging_enabled()) {
                return FALSE;
            }

            // Check if user is a staff member (avatar_type = 4)
            if (!$this->is_staff_member($user_id)) {
                return FALSE;
            }

            // Check if there's already an active session for this exact session ID
            $current_session_id = session_id();
            $existing_session = $this->CI->db->select('id')
                                            ->where('user_id', $user_id)
                                            ->where('session_id', $current_session_id)
                                            ->where('is_active', 1)
                                            ->get($this->table_name)
                                            ->row();

            if ($existing_session) {
                return $existing_session->id; // Return existing session ID
            }

            // Close any existing active sessions for this user if concurrent sessions are limited
            if ($this->config['enable_concurrent_session_limit'] == 1) {
                $this->limit_concurrent_sessions($user_id);
            }

            // Get client information
            $client_info = $this->get_client_info();

            // Get public IP address
            $public_ip = $this->get_public_ip_address();



            // Prepare login data
            $login_data = array(
                'user_id' => $user_id,
                'username' => $username,
                'session_id' => session_id(),
                'ip_address' => $public_ip,
                'user_agent' => $this->CI->input->user_agent(),
                'login_time' => date('Y-m-d H:i:s'),
                'login_method' => $login_method,
                'device_type' => $client_info['device_type'],
                'browser_name' => $client_info['browser_name'],
                'browser_version' => $client_info['browser_version'],
                'operating_system' => $client_info['operating_system'],
                'is_active' => 1,
                'additional_data' => json_encode(array(
                    'referrer' => $this->CI->input->server('HTTP_REFERER'),
                    'accept_language' => $this->CI->input->server('HTTP_ACCEPT_LANGUAGE'),
                    'server_name' => $this->CI->input->server('SERVER_NAME'),
                    'request_uri' => $this->CI->input->server('REQUEST_URI'),
                    'local_ip' => $this->CI->input->ip_address(),
                    'public_ip' => $public_ip
                ))
            );

            // Add IP location if enabled
            if ($this->config['track_ip_location'] == 1) {
                $location = $this->get_ip_location($login_data['ip_address']);
                $login_data['country'] = $location['country'];
                $login_data['city'] = $location['city'];
            }

            // Insert login record
            $this->CI->db->insert($this->table_name, $login_data);
            $insert_id = $this->CI->db->insert_id();

            if ($insert_id) {
                // Store login log ID in session for later reference
                $this->CI->session->set_userdata('staff_login_log_id', $insert_id);

                // Create log file if enabled
                $this->create_log_file('login', $login_data);

                log_message('info', "Staff login logged: User ID {$user_id}, Username {$username}, IP {$login_data['ip_address']}");
                return $insert_id;
            }

            return FALSE;

        } catch (Exception $e) {
            log_message('error', 'Staff Login Logger Error: ' . $e->getMessage());
            return FALSE;
        }
    }

    /**
     * Log user logout event
     * 
     * @param int $user_id User ID (optional if session data available)
     * @param string $logout_reason Reason for logout
     * @return bool Success status
     */
    public function log_logout($user_id = null, $logout_reason = 'manual') {
        try {
            // Check if staff login logging is enabled
            if (!$this->is_staff_login_logging_enabled()) {
                return FALSE;
            }

            // Get user ID from session if not provided
            if (!$user_id) {
                $user_id = $this->CI->session->userdata('user_id');
            }

            // Check if user is a staff member
            if (!$this->is_staff_member($user_id)) {
                return FALSE;
            }

            // Get login log ID from session
            $login_log_id = $this->CI->session->userdata('staff_login_log_id');
            $current_session_id = session_id();

            if ($login_log_id) {
                // Update specific login record
                $update_data = array(
                    'logout_time' => date('Y-m-d H:i:s'),
                    'is_active' => 0,
                    'logout_reason' => $logout_reason,
                    'updated_at' => date('Y-m-d H:i:s')
                );

                // Calculate session duration
                $login_record = $this->CI->db->where('id', $login_log_id)->get($this->table_name)->row();
                if ($login_record) {
                    $login_time = strtotime($login_record->login_time);
                    $logout_time = time();
                    $update_data['session_duration'] = $logout_time - $login_time;
                }

                $this->CI->db->where('id', $login_log_id)->update($this->table_name, $update_data);

                // Create logout log file
                $logout_data = array(
                    'user_id' => $user_id,
                    'username' => $login_record ? $login_record->username : '',
                    'ip_address' => $this->get_public_ip_address(),
                    'user_agent' => $this->CI->input->user_agent(),
                    'logout_reason' => $logout_reason,
                    'session_duration' => isset($update_data['session_duration']) ? $update_data['session_duration'] : null
                );
                $this->create_log_file('logout', $logout_data);
            } else {
                // Fallback: Find active session by user_id and ci_session_id
                $active_session = $this->CI->db
                    ->where('user_id', $user_id)
                    ->where('is_active', 1)
                    ->where('ci_session_id', $current_session_id)
                    ->get($this->table_name)
                    ->row();

                if ($active_session) {
                    // Update the found session
                    $update_data = array(
                        'logout_time' => date('Y-m-d H:i:s'),
                        'is_active' => 0,
                        'logout_reason' => $logout_reason,
                        'updated_at' => date('Y-m-d H:i:s')
                    );

                    $login_time = strtotime($active_session->login_time);
                    $logout_time = time();
                    $update_data['session_duration'] = $logout_time - $login_time;

                    $this->CI->db->where('id', $active_session->id)->update($this->table_name, $update_data);
                } else {
                    // Fallback: close all active sessions for this user
                    $this->close_user_active_sessions($user_id, $logout_reason);
                }
            }

            log_message('info', "Staff logout logged: User ID {$user_id}, Reason: {$logout_reason}");
            return TRUE;

        } catch (Exception $e) {
            log_message('error', 'Staff Logout Logger Error: ' . $e->getMessage());
            return FALSE;
        }
    }

    /**
     * Create initial session record for already logged-in users
     * This is used when a staff member is already logged in but doesn't have a tracking record
     *
     * @param int $user_id User ID
     * @param string $username Username
     * @return int|bool Insert ID on success, FALSE on failure
     */
    public function create_initial_session_record($user_id, $username) {
        try {
            // Check if staff login logging is enabled
            if (!$this->is_staff_login_logging_enabled()) {
                return FALSE;
            }

            // Check if user is a staff member (avatar_type = 4)
            if (!$this->is_staff_member($user_id)) {
                return FALSE;
            }

            // Check if there's already an active session for this user with current session ID
            $existing_session = $this->CI->db->select('id')
                                            ->where('user_id', $user_id)
                                            ->where('session_id', session_id())
                                            ->where('is_active', 1)
                                            ->get($this->table_name)
                                            ->row();

            if ($existing_session) {
                return $existing_session->id;
            }

            // Allow creating new session records even if user has existing logs
            // This enables tracking multiple browser/device sessions

            // Get client information
            $client_info = $this->get_client_info();

            // Get public IP address
            $public_ip = $this->get_public_ip_address();



            // Create initial session record (backdated to session start if possible)
            $session_start_time = $this->estimate_session_start_time();

            $login_data = array(
                'user_id' => $user_id,
                'username' => $username,
                'session_id' => session_id(),
                'ip_address' => $public_ip,
                'user_agent' => $this->CI->input->user_agent(),
                'login_time' => $session_start_time,
                'login_method' => 'existing_session',
                'device_type' => $client_info['device_type'],
                'browser_name' => $client_info['browser_name'],
                'browser_version' => $client_info['browser_version'],
                'operating_system' => $client_info['operating_system'],
                'is_active' => 1,
                'additional_data' => json_encode(array(
                    'initial_tracking' => TRUE,
                    'detected_at' => date('Y-m-d H:i:s'),
                    'referrer' => $this->CI->input->server('HTTP_REFERER'),
                    'accept_language' => $this->CI->input->server('HTTP_ACCEPT_LANGUAGE'),
                    'server_name' => $this->CI->input->server('SERVER_NAME'),
                    'request_uri' => $this->CI->input->server('REQUEST_URI'),
                    'local_ip' => $this->CI->input->ip_address(),
                    'public_ip' => $public_ip
                ))
            );

            // Add IP location if enabled
            if ($this->config['track_ip_location'] == 1) {
                $location = $this->get_ip_location($public_ip);
                $login_data['country'] = $location['country'];
                $login_data['city'] = $location['city'];
            }

            // Insert the record
            $this->CI->db->insert($this->table_name, $login_data);
            $insert_id = $this->CI->db->insert_id();

            if ($insert_id) {
                // Create log file if enabled
                $this->create_log_file('initial_session', $login_data);

                log_message('info', "Initial session record created: User ID {$user_id}, Username {$username}, IP {$login_data['ip_address']}");
                return $insert_id;
            }

            return FALSE;

        } catch (Exception $e) {
            log_message('error', 'Create Initial Session Record Error: ' . $e->getMessage());
            return FALSE;
        }
    }

    /**
     * Track session-based IP for already logged-in users
     * This can be called periodically to update IP if it changes
     *
     * @return bool Success status
     */
    public function track_session_ip() {
        try {
            $user_id = $this->CI->session->userdata('user_id');
            $login_log_id = $this->CI->session->userdata('staff_login_log_id');
            
            if (!$user_id || !$login_log_id) {
                return FALSE;
            }

            $current_ip = $this->get_public_ip_address();
            
            // Get the current logged IP
            $current_record = $this->CI->db->select('ip_address')
                                         ->where('id', $login_log_id)
                                         ->get($this->table_name)
                                         ->row();

            // Update IP if it has changed
            if ($current_record && $current_record->ip_address !== $current_ip) {
                $update_data = array(
                    'ip_address' => $current_ip,
                    'updated_at' => date('Y-m-d H:i:s'),
                    'additional_data' => json_encode(array(
                        'ip_changed' => TRUE,
                        'previous_ip' => $current_record->ip_address,
                        'ip_change_time' => date('Y-m-d H:i:s')
                    ))
                );

                $this->CI->db->where('id', $login_log_id)->update($this->table_name, $update_data);
                log_message('info', "IP address updated for User ID {$user_id}: {$current_record->ip_address} -> {$current_ip}");
            }

            return TRUE;

        } catch (Exception $e) {
            log_message('error', 'Session IP Tracking Error: ' . $e->getMessage());
            return FALSE;
        }
    }

    /**
     * Get client information from user agent
     *
     * @return array Client information
     */
    private function get_client_info() {
        $info = array(
            'device_type' => 'unknown',
            'browser_name' => 'unknown',
            'browser_version' => 'unknown',
            'operating_system' => 'unknown'
        );

        if ($this->config['log_user_agent_details'] == 1) {
            // Load user agent library if not already loaded
            if (!isset($this->CI->agent)) {
                $this->CI->load->library('user_agent');
            }

            // Determine device type
            if ($this->CI->agent->is_mobile()) {
                $info['device_type'] = 'mobile';
            } elseif ($this->CI->agent->is_robot()) {
                $info['device_type'] = 'robot';
            } else {
                $info['device_type'] = 'desktop';
            }

            // Get browser information with fallback
            $browser_name = $this->CI->agent->browser();
            if ($browser_name && $browser_name !== FALSE) {
                $info['browser_name'] = $browser_name;
            } else {
                // Fallback browser detection
                $info['browser_name'] = $this->detect_browser_fallback();
            }

            $browser_version = $this->CI->agent->version();
            if ($browser_version && $browser_version !== FALSE) {
                $info['browser_version'] = $browser_version;
            }

            $platform = $this->CI->agent->platform();
            if ($platform && $platform !== FALSE) {
                $info['operating_system'] = $platform;
            }
        }

        return $info;
    }

    /**
     * Fallback browser detection method
     *
     * @return string Browser name
     */
    private function detect_browser_fallback() {
        $user_agent = $this->CI->input->user_agent();

        if (strpos($user_agent, 'Chrome') !== false) {
            return 'Chrome';
        } elseif (strpos($user_agent, 'Firefox') !== false) {
            return 'Firefox';
        } elseif (strpos($user_agent, 'Safari') !== false) {
            return 'Safari';
        } elseif (strpos($user_agent, 'Edge') !== false) {
            return 'Edge';
        } elseif (strpos($user_agent, 'Opera') !== false) {
            return 'Opera';
        } elseif (strpos($user_agent, 'MSIE') !== false || strpos($user_agent, 'Trident') !== false) {
            return 'Internet Explorer';
        }

        return 'Unknown';
    }

    /**
     * Get IP location information (basic implementation)
     * You can enhance this with a proper IP geolocation service
     * 
     * @param string $ip_address IP address
     * @return array Location information
     */
    private function get_ip_location($ip_address) {
        // Basic implementation - you can integrate with services like:
        // - MaxMind GeoIP
        // - ipapi.co
        // - ip-api.com
        
        $location = array(
            'country' => null,
            'city' => null
        );

        // Skip for local/private IPs
        if (filter_var($ip_address, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
            // You can implement actual IP geolocation here
            // For now, we'll leave it as null to avoid external API calls
        }

        return $location;
    }

    /**
     * Close all active sessions for a user
     * 
     * @param int $user_id User ID
     * @param string $logout_reason Reason for logout
     * @return bool Success status
     */
    public function close_user_active_sessions($user_id, $logout_reason = 'forced') {
        try {
            $update_data = array(
                'logout_time' => date('Y-m-d H:i:s'),
                'is_active' => 0,
                'logout_reason' => $logout_reason,
                'updated_at' => date('Y-m-d H:i:s')
            );

            // Update session duration for active sessions
            $active_sessions = $this->CI->db->where('user_id', $user_id)
                                           ->where('is_active', 1)
                                           ->get($this->table_name)
                                           ->result();

            foreach ($active_sessions as $session) {
                $login_time = strtotime($session->login_time);
                $logout_time = time();
                $session_duration = $logout_time - $login_time;
                
                $this->CI->db->where('id', $session->id)
                            ->update($this->table_name, array_merge($update_data, array(
                                'session_duration' => $session_duration
                            )));
            }

            return TRUE;

        } catch (Exception $e) {
            log_message('error', 'Close Active Sessions Error: ' . $e->getMessage());
            return FALSE;
        }
    }

    /**
     * Limit concurrent sessions for a user
     * 
     * @param int $user_id User ID
     * @return bool Success status
     */
    private function limit_concurrent_sessions($user_id) {
        try {
            $max_sessions = (int)$this->config['max_concurrent_sessions'];
            // Count active sessions
            $active_count = $this->CI->db->where('user_id', $user_id)
                                        ->where('is_active', 1)
                                        ->count_all_results($this->table_name);

            // If at or above limit, close oldest sessions
            if ($active_count >= $max_sessions) {
                $sessions_to_close = $this->CI->db->where('user_id', $user_id)
                                                  ->where('is_active', 1)
                                                  ->order_by('login_time', 'ASC')
                                                  ->limit($active_count - $max_sessions + 1)
                                                  ->get($this->table_name)
                                                  ->result();

                foreach ($sessions_to_close as $session) {
                    $this->CI->db->where('id', $session->id)
                                ->update($this->table_name, array(
                                    'logout_time' => date('Y-m-d H:i:s'),
                                    'is_active' => 0,
                                    'logout_reason' => 'forced',
                                    'session_duration' => time() - strtotime($session->login_time),
                                    'updated_at' => date('Y-m-d H:i:s')
                                ));
                }
            }

            return TRUE;

        } catch (Exception $e) {
            log_message('error', 'Limit Concurrent Sessions Error: ' . $e->getMessage());
            return FALSE;
        }
    }

    /**
     * Get active sessions for a user
     * 
     * @param int $user_id User ID
     * @return array Active sessions
     */
    public function get_active_sessions($user_id) {
        return $this->CI->db->where('user_id', $user_id)
                           ->where('is_active', 1)
                           ->order_by('login_time', 'DESC')
                           ->get($this->table_name)
                           ->result();
    }

    /**
     * Get login history for a user
     * 
     * @param int $user_id User ID
     * @param int $limit Number of records to return
     * @return array Login history
     */
    public function get_login_history($user_id, $limit = 50) {
        return $this->CI->db->where('user_id', $user_id)
                           ->order_by('login_time', 'DESC')
                           ->limit($limit)
                           ->get($this->table_name)
                           ->result();
    }

    /**
     * Clean up old login logs based on retention policy
     *
     * @return bool Success status
     */
    public function cleanup_old_logs() {
        try {
            $retention_days = (int)$this->config['log_retention_days'];
            $cutoff_date = date('Y-m-d H:i:s', strtotime("-{$retention_days} days"));

            $this->CI->db->where('login_time <', $cutoff_date)
                        ->delete($this->table_name);

            $deleted_count = $this->CI->db->affected_rows();
            log_message('info', "Cleaned up {$deleted_count} old staff login logs");

            return TRUE;

        } catch (Exception $e) {
            log_message('error', 'Cleanup Old Logs Error: ' . $e->getMessage());
            return FALSE;
        }
    }

    /**
     * Safely get a setting value
     *
     * @param string $setting_name Setting name
     * @param mixed $default Default value if setting not found
     * @return mixed Setting value
     */
    private function get_setting($setting_name, $default = null) {
        // First check staff_login_config table
        $result = $this->CI->db->select('config_value')
                              ->where('config_key', $setting_name)
                              ->get($this->config_table)
                              ->row();

        if ($result) {
            return $result->config_value;
        }

        // Fallback: check if settings library is available
        if (isset($this->CI->settings)) {
            return $this->CI->settings->getSetting($setting_name);
        }

        // Final fallback: check general settings table
        $result = $this->CI->db->select('value')
                              ->where('name', $setting_name)
                              ->get('settings')
                              ->row();

        return $result ? $result->value : $default;
    }

    /**
     * Check if staff login logging is enabled
     *
     * @return bool
     */
    private function is_staff_login_logging_enabled() {
        $setting = $this->get_setting('staff_login_logging_enabled', '1');
        return $setting == '1' || $setting === true;
    }

    /**
     * Check if user is a staff member (avatar_type = 4)
     *
     * @param int $user_id User ID
     * @return bool
     */
    private function is_staff_member($user_id) {
        $result = $this->CI->db->select('id')
                              ->where('user_id', $user_id)
                              ->where('avatar_type', 4)
                              ->get('avatar')
                              ->row();

        return !empty($result);
    }

    /**
     * Get public IP address
     *
     * @return string Public IP address
     */
    private function get_public_ip_address() {
        // Try to get the real IP address from various headers
        $ip_keys = array(
            'HTTP_CF_CONNECTING_IP',     // Cloudflare
            'HTTP_CLIENT_IP',            // Proxy
            'HTTP_X_FORWARDED_FOR',      // Load balancer/proxy
            'HTTP_X_FORWARDED',          // Proxy
            'HTTP_X_CLUSTER_CLIENT_IP',  // Cluster
            'HTTP_FORWARDED_FOR',        // Proxy
            'HTTP_FORWARDED',            // Proxy
            'HTTP_X_REAL_IP',            // Nginx proxy
            'REMOTE_ADDR'                // Standard
        );

        foreach ($ip_keys as $key) {
            if (array_key_exists($key, $_SERVER) === true) {
                $ip_list = explode(',', $_SERVER[$key]);
                foreach ($ip_list as $ip) {
                    $ip = trim($ip);

                    // Validate IP and check if it's not private/reserved
                    if (filter_var($ip, FILTER_VALIDATE_IP,
                        FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) !== false) {
                        return $ip;
                    }
                }
            }
        }

        // Try CodeIgniter's IP detection
        $ci_ip = $this->CI->input->ip_address();
        if (filter_var($ci_ip, FILTER_VALIDATE_IP,
            FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) !== false) {
            return $ci_ip;
        }

        // If all above methods return private/local IP, try external services (if enabled)
        if ($this->config['use_external_ip_services'] == 1) {
            // Check session cache first to avoid repeated external calls
            $cached_public_ip = $this->CI->session->userdata('cached_public_ip');
            $cache_time = $this->CI->session->userdata('cached_public_ip_time');

            // Use cached IP if it's less than 30 minutes old
            if ($cached_public_ip && $cache_time && (time() - $cache_time) < 1800) {
                return $cached_public_ip;
            }

            $external_ip = $this->get_external_public_ip();

            // Cache the result if it's a valid public IP
            if (filter_var($external_ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) !== false) {
                $this->CI->session->set_userdata('cached_public_ip', $external_ip);
                $this->CI->session->set_userdata('cached_public_ip_time', time());
            }

            return $external_ip;
        }

        // Return the best available IP even if it's private
        return $ci_ip;
    }

    /**
     * Get public IP address using external services
     *
     * @return string Public IP address or fallback IP
     */
    private function get_external_public_ip() {
        // Use only the most reliable and fastest services
        $ip_services = array(
            'https://api.ipify.org',
            'https://ipv4.icanhazip.com',
            'https://checkip.amazonaws.com'
        );

        foreach ($ip_services as $service) {
            try {
                // Reduced timeout for better performance
                $context = stream_context_create(array(
                    'http' => array(
                        'timeout' => 3, // 3 second timeout
                        'user_agent' => 'Staff-Login-Logger/1.0',
                        'method' => 'GET'
                    )
                ));

                $response = @file_get_contents($service, false, $context);

                if ($response !== false) {
                    $ip = trim($response);

                    // Validate the IP address
                    if (filter_var($ip, FILTER_VALIDATE_IP,
                        FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) !== false) {


                        return $ip;
                    }
                }
            } catch (Exception $e) {
                // Continue to next service if this one fails
                continue;
            }
        }

        // Ultimate fallback - return the best available IP even if it's private
        $fallback_ip = $this->CI->input->ip_address();
        return $fallback_ip;
    }



    /**
     * Check if an IP address is private/local
     *
     * @param string $ip IP address to check
     * @return bool True if private/local, false if public
     */
    private function is_private_ip($ip) {
        return filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) === false;
    }

    /**
     * Create log file for staff login activity
     *
     * @param string $action Action type (login, logout)
     * @param array $data Login/logout data
     * @return bool Success status
     */
    private function create_log_file($action, $data) {
        try {
            // Check if log file creation is enabled
            $log_files_enabled = $this->get_setting('staff_login_log_files_enabled', '0');

            if ($log_files_enabled != '1' && $log_files_enabled !== true) {
                return FALSE;
            }

            // Create logs directory if it doesn't exist
            $log_dir = APPPATH . 'logs/staff_login/';
            if (!is_dir($log_dir)) {
                mkdir($log_dir, 0755, true);
            }

            // Create log file name based on date
            $log_file = $log_dir . 'staff_login_' . date('Y-m-d') . '.log';

            // Prepare log entry
            $log_entry = array(
                'timestamp' => date('Y-m-d H:i:s'),
                'action' => $action,
                'user_id' => $data['user_id'],
                'username' => $data['username'],
                'ip_address' => $data['ip_address'],
                'user_agent' => $data['user_agent'],
                'session_id' => isset($data['session_id']) ? $data['session_id'] : null,
                'device_type' => isset($data['device_type']) ? $data['device_type'] : null,
                'browser_name' => isset($data['browser_name']) ? $data['browser_name'] : null,
                'login_method' => isset($data['login_method']) ? $data['login_method'] : null
            );

            // Write to log file
            $log_line = json_encode($log_entry) . "\n";
            file_put_contents($log_file, $log_line, FILE_APPEND | LOCK_EX);

            return TRUE;

        } catch (Exception $e) {
            log_message('error', 'Create Log File Error: ' . $e->getMessage());
            return FALSE;
        }
    }

    /**
     * Estimate session start time for existing sessions
     *
     * @return string Estimated session start time
     */
    private function estimate_session_start_time() {
        // Try to get session creation time from various sources

        // Method 1: Check if session has a creation timestamp
        if (isset($_SESSION['__ci_last_regenerate'])) {
            return date('Y-m-d H:i:s', $_SESSION['__ci_last_regenerate']);
        }

        // Method 2: Check user's last login time from users table
        $user_id = $this->CI->session->userdata('user_id');
        if ($user_id) {
            $user_last_login = $this->CI->db->select('last_login')
                                           ->where('id', $user_id)
                                           ->get('users')
                                           ->row();

            if ($user_last_login && $user_last_login->last_login) {
                return date('Y-m-d H:i:s', $user_last_login->last_login);
            }
        }

        // Method 3: Check session file modification time (if using file sessions)
        $session_id = session_id();
        if ($session_id) {
            $session_path = session_save_path();
            if ($session_path) {
                $session_file = $session_path . '/ci_session' . $session_id;
                if (file_exists($session_file)) {
                    return date('Y-m-d H:i:s', filemtime($session_file));
                }
            }
        }

        // Fallback: Use current time minus estimated session duration (30 minutes ago)
        return date('Y-m-d H:i:s', time() - 1800);
    }
}
