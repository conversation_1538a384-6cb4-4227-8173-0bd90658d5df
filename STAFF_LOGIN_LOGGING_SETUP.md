# Staff Login Logging System - Setup Guide

This system provides comprehensive logging of staff login activities including IP addresses, session tracking, and user agent details without affecting existing CodeIgniter functionality.

## Features

- **Automatic Login Logging**: Captures login events automatically using CodeIgniter hooks
- **IP Address Tracking**: Records and tracks IP address changes during sessions
- **Session Management**: Tracks active sessions and session duration
- **User Agent Parsing**: Extracts browser, device, and OS information
- **Logout Tracking**: Records logout events with reasons (manual, timeout, forced)
- **Security Features**: Detects suspicious login patterns and concurrent session limits
- **Reporting**: Comprehensive dashboard and reporting capabilities
- **Non-Intrusive**: Works without modifying existing authentication code

## Installation Steps

### 1. Database Setup

Run the SQL script to create the required tables:

```sql
-- Execute the contents of: application/db/staff_login_logs.sql
```

This creates:
- `staff_login_logs` - Main logging table
- `staff_login_config` - Configuration table
- Views and stored procedures for reporting

### 2. Verify Hooks are Enabled

Ensure hooks are enabled in `application/config/config.php`:

```php
$config['enable_hooks'] = TRUE;
```

### 3. Files Created

The following files have been created:

#### Core Files:
- `application/libraries/Staff_login_logger.php` - Main logging library
- `application/hooks/staff_login_hook.php` - Hook functions
- `application/models/Staff_login_model.php` - Database model
- `application/helpers/staff_login_helper.php` - Helper functions

#### Admin Interface:
- `application/controllers/admin/Staff_login_logs.php` - Admin controller
- `application/views/admin/staff_login_logs/test.php` - Test page

#### Database:
- `application/db/staff_login_logs.sql` - Database schema

#### Configuration:
- `application/config/hooks.php` - Updated with logging hooks

### 4. Test the System

1. Access the test page: `/admin/staff_login_logs/test`
2. This will verify:
   - Database tables exist
   - Libraries are loaded correctly
   - Current session information
   - Recent login logs

### 5. Access the Admin Interface

Once setup is complete, access the admin interface at:
- `/admin/staff_login_logs` - Dashboard
- `/admin/staff_login_logs/logs` - All logs with filtering
- `/admin/staff_login_logs/active_sessions` - Active sessions
- `/admin/staff_login_logs/config` - Configuration settings

## How It Works

### Automatic Logging

The system uses CodeIgniter hooks to automatically capture login events:

1. **Login Monitoring**: `post_controller_constructor` hook monitors login attempts
2. **Success Logging**: `post_controller` hook logs successful logins
3. **Logout Tracking**: `pre_controller` hook captures logout events
4. **Session Tracking**: Periodic IP address and session monitoring

### Hook Points

- `monitor_staff_login()` - Monitors login attempts
- `log_successful_login()` - Logs successful logins
- `log_logout_event()` - Logs logout events
- `track_session_activity()` - Tracks session changes
- `check_session_timeout()` - Handles session timeouts

### Data Captured

For each login, the system captures:
- User ID and username
- IP address and location (optional)
- User agent details (browser, OS, device type)
- Session ID and duration
- Login/logout timestamps
- Login method (web, mobile, API)
- Additional metadata

## Configuration Options

The system includes configurable options:

- `log_retention_days` - How long to keep logs (default: 90 days)
- `track_ip_location` - Enable IP geolocation (default: enabled)
- `log_user_agent_details` - Parse user agent strings (default: enabled)
- `session_timeout_minutes` - Session timeout (default: 1440 minutes)
- `enable_concurrent_session_limit` - Limit concurrent sessions (default: disabled)
- `max_concurrent_sessions` - Maximum concurrent sessions (default: 3)

## Helper Functions

The system provides helper functions for easy integration:

```php
// Load the helper
$this->load->helper('staff_login_helper');

// Get current user's login info
$login_info = get_current_user_login_info();

// Get active sessions for a user
$sessions = get_user_active_sessions($user_id);

// Get login history
$history = get_user_login_history($user_id, 20);

// Force logout a user
force_user_logout($user_id, 'admin_action');

// Get login statistics
$stats = get_login_statistics(30); // Last 30 days

// Check for suspicious login
$suspicion = is_suspicious_login($user_id, $ip_address);
```

## Security Features

### Suspicious Login Detection

The system automatically detects:
- New IP addresses for users
- Multiple IPs in short time periods
- Rapid login attempts
- Logins outside normal hours

### Session Management

- Track concurrent sessions
- Force logout capabilities
- Session timeout handling
- IP address change detection

## Reporting and Analytics

### Dashboard Metrics
- Today's logins
- Active sessions count
- Total logins in period
- Unique users
- Browser statistics
- Daily login trends

### Reports Available
- Login history with filtering
- Active sessions monitoring
- Suspicious login alerts
- User-specific login history
- Export to CSV functionality

## Maintenance

### Automatic Cleanup

The system includes automatic cleanup of old logs based on the retention policy. This runs periodically (1% chance per request) to avoid performance impact.

### Manual Cleanup

You can also manually clean up old logs:

```php
$this->load->library('staff_login_logger');
$this->staff_login_logger->cleanup_old_logs();
```

## Performance Considerations

- Hooks are designed to be lightweight
- Database operations are optimized with proper indexing
- Cleanup runs infrequently to avoid performance impact
- Session tracking is throttled (every 5 minutes)

## Troubleshooting

### Common Issues

1. **Hooks not working**: Ensure `$config['enable_hooks'] = TRUE;` in config.php
2. **Database errors**: Run the SQL script to create required tables
3. **No logs appearing**: Check that users are logging in after installation
4. **Performance issues**: Adjust cleanup frequency or retention period

### Debug Mode

For development, you can enable additional logging by checking CodeIgniter's log files in `application/logs/`.

## Security Notes

- The system logs IP addresses and user agents for security purposes
- Sensitive data like passwords are never logged
- All database operations use CodeIgniter's query builder for security
- Session data is handled securely through CodeIgniter's session library

## Customization

The system is designed to be easily customizable:

- Modify `Staff_login_logger.php` to add custom fields
- Update hooks to capture additional events
- Extend the model for custom reporting
- Add custom helper functions as needed

## Support

For issues or questions:
1. Check the test page for system status
2. Review CodeIgniter logs for errors
3. Verify database table structure
4. Ensure proper permissions for admin access
