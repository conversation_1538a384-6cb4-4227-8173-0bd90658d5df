<?php defined('BASEPATH') OR exit('No direct script access allowed'); ?>

<ul class="breadcrumb">
  <li><a href="<?php echo site_url('dashboard');?>">Dashboard</a></li>
  <li>Staff Login Logs</li>
</ul>

<div class="col-md-12">
    <div class="card cd_border">
        <div class="card-header panel_heading_new_style_staff_border">
            <div class="row" style="margin: 0px;">
                <div class="d-flex justify-content-between" style="width:100%;">
                    <h3 class="card-title panel_title_new_style_staff">
                        Staff Login Logs
                    </h3>
                    <ul class="panel-controls">
                        <a style="margin-left:3px;" href="<?php echo base_url('admin/staff_login_logs/logs'); ?>" class="btn btn-primary pull-right">
                            <i class="fas fa-list"></i> All Logs
                        </a>
                        <a style="margin-left:3px;" href="<?php echo base_url('admin/staff_login_logs/active_sessions'); ?>" class="btn btn-success pull-right">
                            <i class="fas fa-users"></i> Active Sessions
                        </a>
                    </ul>
                </div>
            </div>
        </div>

        <div class="card-body pt-1">
            <!-- Statistics Section -->
            <div class="row">
                <div data-toggle="tooltip" data-original-title="View today's login details" class="col-md-3 text-center stat-pointer" onclick="goToTodayLogins()">
                    <div class="stat-class">
                        <h5><b>Today's Logins</b></h5>
                        <h5><b><?php echo isset($stats['today_logins']) ? $stats['today_logins'] : 0; ?></b></h5>
                    </div>
                </div>

                <div data-toggle="tooltip" data-original-title="View active sessions" class="col-md-3 text-center stat-pointer" onclick="goToActiveSessions()">
                    <div class="stat-class">
                        <h5><b>Active Sessions</b></h5>
                        <h5><b><?php echo isset($stats['active_sessions']) ? $stats['active_sessions'] : 0; ?></b></h5>
                    </div>
                </div>

                <div data-toggle="tooltip" data-original-title="View unique users today" class="col-md-3 text-center stat-pointer" onclick="goToUniqueUsers()">
                    <div class="stat-class">
                        <h5><b>Unique Users Today</b></h5>
                        <h5><b><?php echo isset($stats['today_unique_users']) ? $stats['today_unique_users'] : 0; ?></b></h5>
                    </div>
                </div>

                <div data-toggle="tooltip" data-original-title="View multiple system logins" class="col-md-3 text-center stat-pointer" onclick="goToMultipleLogins()">
                    <div class="stat-class">
                        <h5><b>Multiple System Logins</b></h5>
                        <h5 <?php echo (isset($stats['multiple_system_logins']) && $stats['multiple_system_logins'] > 0) ? 'style="color:red;"' : ''; ?>><b><?php echo isset($stats['multiple_system_logins']) ? $stats['multiple_system_logins'] : 0; ?></b></h5>
                    </div>
                </div>
            </div>
            <br>

            <div class="row">
                <div data-toggle="tooltip" data-original-title="View device statistics" class="col-md-4 text-center stat-pointer" onclick="goToDeviceStats()">
                    <div class="stat-class">
                        <h5><b>Device Types</b></h5>
                        <h5><b>
                            <?php
                            if (!empty($stats['device_stats'])) {
                                $device_summary = array();
                                foreach ($stats['device_stats'] as $device) {
                                    $device_summary[] = ucfirst($device['device_type']) . ': ' . $device['count'];
                                }
                                echo implode(' | ', array_slice($device_summary, 0, 3));
                            } else {
                                echo 'No data';
                            }
                            ?>
                        </b></h5>
                    </div>
                </div>

                <div data-toggle="tooltip" data-original-title="View browser statistics" class="col-md-4 text-center stat-pointer" onclick="goToBrowserStats()">
                    <div class="stat-class">
                        <h5><b>Top Browsers</b></h5>
                        <h5><b>
                            <?php
                            if (!empty($stats['browser_stats'])) {
                                $browser_summary = array();
                                foreach (array_slice($stats['browser_stats'], 0, 2) as $browser) {
                                    $browser_summary[] = $browser['browser_name'] . ': ' . $browser['count'];
                                }
                                echo implode(' | ', $browser_summary);
                            } else {
                                echo 'No data';
                            }
                            ?>
                        </b></h5>
                    </div>
                </div>

                <div data-toggle="tooltip" data-original-title="View all login logs" class="col-md-4 text-center stat-pointer" onclick="goToAllLogs()">
                    <div class="stat-class">
                        <h5><b>Total Logins (7 days)</b></h5>
                        <h5><b><?php echo isset($stats['total_logins']) ? $stats['total_logins'] : 0; ?></b></h5>
                    </div>
                </div>
            </div>
            <br>

            <!-- Device & Browser Statistics -->
            <div class="row mt-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title"><i class="fas fa-mobile-alt"></i> Login by Device Type</h5>
                        </div>
                        <div class="card-body">
                            <?php if (!empty($stats['device_stats'])): ?>
                                <?php foreach ($stats['device_stats'] as $device): ?>
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <div>
                                            <i class="fas fa-<?php echo get_device_icon($device['device_type']); ?>"></i>
                                            <?php echo ucfirst($device['device_type']); ?>
                                        </div>
                                        <div>
                                            <span class="badge badge-primary"><?php echo $device['count']; ?></span>
                                        </div>
                                    </div>
                                    <div class="progress mb-3" style="height: 6px;">
                                        <div class="progress-bar" role="progressbar" 
                                             style="width: <?php echo ($device['count'] / max($stats['total_logins'], 1)) * 100; ?>%"></div>
                                    </div>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <p class="text-muted">No device statistics available</p>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title"><i class="fas fa-globe"></i> Login by Browser</h5>
                        </div>
                        <div class="card-body">
                            <?php if (!empty($stats['browser_stats'])): ?>
                                <?php foreach ($stats['browser_stats'] as $browser): ?>
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <div>
                                            <i class="fab fa-<?php echo get_browser_icon($browser['browser_name']); ?>"></i>
                                            <?php echo $browser['browser_name']; ?>
                                        </div>
                                        <div>
                                            <span class="badge badge-info"><?php echo $browser['count']; ?></span>
                                        </div>
                                    </div>
                                    <div class="progress mb-3" style="height: 6px;">
                                        <div class="progress-bar bg-info" role="progressbar" 
                                             style="width: <?php echo ($browser['count'] / max($stats['total_logins'], 1)) * 100; ?>%"></div>
                                    </div>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <p class="text-muted">No browser statistics available</p>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Today's Login Details -->
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title"><i class="fas fa-calendar-day"></i> Today's Login Details</h5>
                            <div class="card-tools">
                                <a href="<?php echo base_url('admin/staff_login_logs/today_logins'); ?>" class="btn btn-sm btn-primary">
                                    View All Today's Logins
                                </a>
                            </div>
                        </div>
                        <div class="card-body">
                            <?php if (!empty($today_login_details)): ?>
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover" id="todayLoginsTable">
                                        <thead>
                                            <tr>
                                                <th><i class="fas fa-user"></i> User</th>
                                                <th><i class="fas fa-network-wired"></i> IP Address</th>
                                                <th><i class="fas fa-clock"></i> Login Time</th>
                                                <th><i class="fas fa-mobile-alt"></i> Device</th>
                                                <th><i class="fas fa-globe"></i> Browser</th>
                                                <th><i class="fas fa-info-circle"></i> Status</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach (array_slice($today_login_details, 0, 10) as $login): ?>
                                                <tr>
                                                    <td>
                                                        <strong><?php echo htmlspecialchars($login['username']); ?></strong>
                                                        <?php if (!empty($login['email'])): ?>
                                                            <br><small class="text-muted"><?php echo htmlspecialchars($login['email']); ?></small>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td>
                                                        <span class="badge badge-info"><?php echo htmlspecialchars($login['ip_address']); ?></span>
                                                    </td>
                                                    <td>
                                                        <?php echo date('H:i:s', strtotime($login['login_time'])); ?>
                                                        <br><small class="text-muted"><?php echo time_elapsed_string($login['login_time']); ?> ago</small>
                                                    </td>
                                                    <td>
                                                        <i class="fas fa-<?php echo get_device_icon($login['device_type']); ?>"></i>
                                                        <?php echo ucfirst($login['device_type'] ?: 'Unknown'); ?>
                                                    </td>
                                                    <td>
                                                        <i class="fab fa-<?php echo get_browser_icon($login['browser_name']); ?>"></i>
                                                        <?php echo $login['browser_name'] ?: 'Unknown'; ?>
                                                    </td>
                                                    <td>
                                                        <?php if ($login['is_active']): ?>
                                                            <span class="badge badge-success">Active</span>
                                                        <?php else: ?>
                                                            <span class="badge badge-secondary">Ended</span>
                                                        <?php endif; ?>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php else: ?>
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle"></i>
                                    No logins recorded for today.
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Multiple System Logins -->
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title"><i class="fas fa-users-cog"></i> Multiple System Logins</h5>
                            <div class="card-tools">
                                <a href="<?php echo base_url('admin/staff_login_logs/multiple_system_logins'); ?>" class="btn btn-sm btn-warning">
                                    View All Multiple Logins
                                </a>
                            </div>
                        </div>
                        <div class="card-body">
                            <?php if (!empty($multiple_system_users)): ?>
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover" id="multipleSystemTable">
                                        <thead>
                                            <tr>
                                                <th><i class="fas fa-user"></i> User</th>
                                                <th><i class="fas fa-desktop"></i> Active Sessions</th>
                                                <th><i class="fas fa-network-wired"></i> IP Addresses</th>
                                                <th><i class="fas fa-mobile-alt"></i> Devices</th>
                                                <th><i class="fas fa-cogs"></i> Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($multiple_system_users as $user): ?>
                                                <tr>
                                                    <td>
                                                        <strong><?php echo htmlspecialchars($user['username']); ?></strong>
                                                        <?php if (!empty($user['email'])): ?>
                                                            <br><small class="text-muted"><?php echo htmlspecialchars($user['email']); ?></small>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td>
                                                        <span class="badge badge-warning"><?php echo $user['session_count']; ?> Sessions</span>
                                                    </td>
                                                    <td>
                                                        <?php
                                                        $unique_ips = array_unique(array_column($user['sessions'], 'ip_address'));
                                                        foreach (array_slice($unique_ips, 0, 3) as $ip):
                                                        ?>
                                                            <span class="badge badge-info"><?php echo $ip; ?></span><br>
                                                        <?php endforeach; ?>
                                                        <?php if (count($unique_ips) > 3): ?>
                                                            <small class="text-muted">+<?php echo count($unique_ips) - 3; ?> more</small>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td>
                                                        <?php
                                                        $unique_devices = array_unique(array_column($user['sessions'], 'device_type'));
                                                        foreach ($unique_devices as $device):
                                                        ?>
                                                            <i class="fas fa-<?php echo get_device_icon($device); ?>"></i>
                                                            <?php echo ucfirst($device ?: 'Unknown'); ?><br>
                                                        <?php endforeach; ?>
                                                    </td>
                                                    <td>
                                                        <a href="<?php echo base_url('admin/staff_login_logs/user_history/' . $user['user_id']); ?>"
                                                           class="btn btn-sm btn-info" title="View History">
                                                            <i class="fas fa-history"></i>
                                                        </a>
                                                        <a href="<?php echo base_url('admin/staff_login_logs/force_logout/' . $user['user_id']); ?>"
                                                           class="btn btn-sm btn-danger" title="Force Logout All"
                                                           onclick="return confirm('Are you sure you want to force logout all sessions for this user?')">
                                                            <i class="fas fa-sign-out-alt"></i>
                                                        </a>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php else: ?>
                                <div class="alert alert-success">
                                    <i class="fas fa-check-circle"></i>
                                    No users currently have multiple active sessions.
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Date Range Report Section -->
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title"><i class="fas fa-calendar-alt"></i> Date Range Report</h5>
                        </div>
                        <div class="card-body">
                            <form method="GET" action="<?php echo base_url('admin/staff_login_logs/date_range_report'); ?>">
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label>From Date</label>
                                            <input type="date" name="date_from" class="form-control"
                                                   value="<?php echo date('Y-m-d', strtotime('-7 days')); ?>">
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label>To Date</label>
                                            <input type="date" name="date_to" class="form-control"
                                                   value="<?php echo date('Y-m-d'); ?>">
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label>Username (Optional)</label>
                                            <input type="text" name="username" class="form-control"
                                                   placeholder="Enter username">
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label>&nbsp;</label>
                                            <div>
                                                <button type="submit" class="btn btn-primary">
                                                    <i class="fas fa-search"></i> Generate Report
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Active Sessions Table -->
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title"><i class="fas fa-users"></i> Currently Active Sessions</h5>
                            <div class="card-tools">
                                <a href="<?php echo base_url('admin/staff_login_logs/active_sessions'); ?>" class="btn btn-sm btn-primary">
                                    View All Active Sessions
                                </a>
                            </div>
                        </div>
                        <div class="card-body">
                            <?php if (!empty($active_sessions)): ?>
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover" id="activeSessionsTable">
                                        <thead>
                                            <tr>
                                                <th><i class="fas fa-user"></i> User</th>
                                                <th><i class="fas fa-network-wired"></i> IP Address</th>
                                                <th><i class="fas fa-clock"></i> Login Time</th>
                                                <th><i class="fas fa-hourglass-half"></i> Duration</th>
                                                <th><i class="fas fa-mobile-alt"></i> Device</th>
                                                <th><i class="fas fa-globe"></i> Browser</th>
                                                <th><i class="fas fa-map-marker-alt"></i> Location</th>
                                                <th><i class="fas fa-cogs"></i> Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($active_sessions as $session): ?>
                                                <tr>
                                                    <td>
                                                        <div class="user-info">
                                                            <strong><?php echo htmlspecialchars($session['username']); ?></strong>
                                                            <?php if (!empty($session['email'])): ?>
                                                                <br><small class="text-muted"><?php echo htmlspecialchars($session['email']); ?></small>
                                                            <?php endif; ?>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <span class="badge badge-info"><?php echo htmlspecialchars($session['ip_address']); ?></span>
                                                    </td>
                                                    <td>
                                                        <?php echo date('M j, Y H:i', strtotime($session['login_time'])); ?>
                                                        <br><small class="text-muted"><?php echo time_elapsed_string($session['login_time']); ?> ago</small>
                                                    </td>
                                                    <td>
                                                        <span class="badge badge-success"><?php echo isset($session['session_minutes']) ? $session['session_minutes'] . ' min' : 'N/A'; ?></span>
                                                    </td>
                                                    <td>
                                                        <i class="fas fa-<?php echo get_device_icon($session['device_type']); ?>"></i>
                                                        <?php echo ucfirst($session['device_type'] ?: 'Unknown'); ?>
                                                    </td>
                                                    <td>
                                                        <i class="fab fa-<?php echo get_browser_icon($session['browser_name']); ?>"></i>
                                                        <?php echo $session['browser_name'] ?: 'Unknown'; ?>
                                                    </td>
                                                    <td>
                                                        <?php if (!empty($session['country']) || !empty($session['city'])): ?>
                                                            <i class="fas fa-map-marker-alt"></i>
                                                            <?php echo trim(($session['city'] ?: '') . ', ' . ($session['country'] ?: ''), ', '); ?>
                                                        <?php else: ?>
                                                            <span class="text-muted">Unknown</span>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td>
                                                        <div class="btn-group" role="group">
                                                            <a href="<?php echo base_url('admin/staff_login_logs/user_history/' . $session['user_id']); ?>" 
                                                               class="btn btn-sm btn-info" title="View History">
                                                                <i class="fas fa-history"></i>
                                                            </a>
                                                            <a href="<?php echo base_url('admin/staff_login_logs/force_logout/' . $session['user_id']); ?>" 
                                                               class="btn btn-sm btn-danger" title="Force Logout"
                                                               onclick="return confirm('Are you sure you want to force logout this user?')">
                                                                <i class="fas fa-sign-out-alt"></i>
                                                            </a>
                                                        </div>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php else: ?>
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle"></i>
                                    No active sessions found.
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Helper Functions for Icons -->
<?php
if (!function_exists('get_device_icon')) {
    function get_device_icon($device_type) {
        switch (strtolower($device_type)) {
            case 'mobile': return 'mobile-alt';
            case 'tablet': return 'tablet-alt';
            case 'desktop': return 'desktop';
            default: return 'question-circle';
        }
    }
}

if (!function_exists('get_browser_icon')) {
    function get_browser_icon($browser_name) {
        switch (strtolower($browser_name)) {
            case 'chrome': return 'chrome';
            case 'firefox': return 'firefox';
            case 'safari': return 'safari';
            case 'edge': return 'edge';
            case 'opera': return 'opera';
            case 'internet explorer': return 'internet-explorer';
            default: return 'globe';
        }
    }
}

if (!function_exists('time_elapsed_string')) {
    function time_elapsed_string($datetime, $full = false) {
        $now = new DateTime;
        $ago = new DateTime($datetime);
        $diff = $now->diff($ago);

        $diff->w = floor($diff->d / 7);
        $diff->d -= $diff->w * 7;

        $string = array(
            'y' => 'year',
            'm' => 'month',
            'w' => 'week',
            'd' => 'day',
            'h' => 'hour',
            'i' => 'minute',
            's' => 'second',
        );
        foreach ($string as $k => &$v) {
            if ($diff->$k) {
                $v = $diff->$k . ' ' . $v . ($diff->$k > 1 ? 's' : '');
            } else {
                unset($string[$k]);
            }
        }

        if (!$full) $string = array_slice($string, 0, 1);
        return $string ? implode(', ', $string) : 'just now';
    }
}
?>

<style>
/* Icon alignment improvements */
.fas, .fab, .far {
    margin-right: 5px;
    vertical-align: middle;
}

.btn .fas, .btn .fab, .btn .far {
    margin-right: 3px;
}

.card-title .fas, .card-title .fab, .card-title .far {
    margin-right: 8px;
}

.table th .fas, .table th .fab, .table th .far {
    margin-right: 5px;
}

.badge .fas, .badge .fab, .badge .far {
    margin-right: 3px;
}

.alert .fas, .alert .fab, .alert .far {
    margin-right: 5px;
}

/* Ensure proper spacing for back buttons */
.back_anchor .fas {
    margin-right: 8px;
}
</style>

<script>
$(document).ready(function() {
    // Initialize DataTable for today's logins
    $('#todayLoginsTable').DataTable({
        "responsive": true,
        "lengthChange": false,
        "autoWidth": false,
        "pageLength": 10,
        "order": [[ 2, "desc" ]], // Sort by login time descending
        "columnDefs": [
            { "orderable": false, "targets": 5 } // Disable sorting on Status column
        ]
    });

    // Initialize DataTable for multiple system logins
    $('#multipleSystemTable').DataTable({
        "responsive": true,
        "lengthChange": false,
        "autoWidth": false,
        "pageLength": 10,
        "order": [[ 1, "desc" ]], // Sort by session count descending
        "columnDefs": [
            { "orderable": false, "targets": 4 } // Disable sorting on Actions column
        ]
    });

    // Initialize DataTable for active sessions
    $('#activeSessionsTable').DataTable({
        "responsive": true,
        "lengthChange": false,
        "autoWidth": false,
        "pageLength": 10,
        "order": [[ 2, "desc" ]], // Sort by login time descending
        "columnDefs": [
            { "orderable": false, "targets": 7 } // Disable sorting on Actions column
        ]
    });
});

// Navigation functions
function goToTodayLogins() {
    window.location.href = '<?php echo base_url("admin/staff_login_logs/today_logins"); ?>';
}

function goToActiveSessions() {
    window.location.href = '<?php echo base_url("admin/staff_login_logs/active_sessions"); ?>';
}

function goToUniqueUsers() {
    window.location.href = '<?php echo base_url("admin/staff_login_logs/today_logins"); ?>';
}

function goToMultipleLogins() {
    window.location.href = '<?php echo base_url("admin/staff_login_logs/multiple_system_logins"); ?>';
}

function goToDeviceStats() {
    window.location.href = '<?php echo base_url("admin/staff_login_logs/logs?device_filter=1"); ?>';
}

function goToBrowserStats() {
    window.location.href = '<?php echo base_url("admin/staff_login_logs/logs?browser_filter=1"); ?>';
}

function goToAllLogs() {
    window.location.href = '<?php echo base_url("admin/staff_login_logs/logs"); ?>';
}
</script>
