-- Update Staff Login Configuration for Public IP Detection
-- This update enhances IP detection to capture public IP addresses instead of local server IPs

-- Insert the new configuration options if they don't exist
INSERT IGNORE INTO `staff_login_config` (`config_key`, `config_value`, `description`, `created_at`, `updated_at`)
VALUES
('staff_login_logging_enabled', '1', 'Enable/disable staff login logging system (1=enabled, 0=disabled)', NOW(), NOW()),
('use_external_ip_services', '1', 'Enable/disable external IP detection services when local IP detection fails (1=enabled, 0=disabled)', NOW(), NOW());

-- Update existing configuration descriptions for clarity
UPDATE `staff_login_config` 
SET `description` = 'Number of days to retain login logs (default: 90 days)' 
WHERE `config_key` = 'log_retention_days' AND (`description` IS NULL OR `description` = '');

UPDATE `staff_login_config` 
SET `description` = 'Enable IP location tracking using external services (1=enabled, 0=disabled)' 
WHERE `config_key` = 'track_ip_location' AND (`description` IS NULL OR `description` = '');

UPDATE `staff_login_config` 
SET `description` = 'Log detailed user agent information (1=enabled, 0=disabled)' 
WHERE `config_key` = 'log_user_agent_details' AND (`description` IS NULL OR `description` = '');

UPDATE `staff_login_config` 
SET `description` = 'Automatically close inactive sessions (1=enabled, 0=disabled)' 
WHERE `config_key` = 'auto_close_inactive_sessions' AND (`description` IS NULL OR `description` = '');

UPDATE `staff_login_config` 
SET `description` = 'Session timeout in minutes (default: 1440 minutes = 24 hours)' 
WHERE `config_key` = 'session_timeout_minutes' AND (`description` IS NULL OR `description` = '');

UPDATE `staff_login_config` 
SET `description` = 'Limit concurrent sessions per user (1=enabled, 0=disabled)' 
WHERE `config_key` = 'enable_concurrent_session_limit' AND (`description` IS NULL OR `description` = '');

UPDATE `staff_login_config` 
SET `description` = 'Maximum number of concurrent sessions allowed per user (default: 3)' 
WHERE `config_key` = 'max_concurrent_sessions' AND (`description` IS NULL OR `description` = '');

-- Show current configuration
SELECT * FROM `staff_login_config` ORDER BY `config_key`;
