<?php 
    if($this->settings->isParentModuleEnabled('FEES_MULTIPLE_BLUEPRINT')){
      $backUrl = site_url('parent_controller/display_fee_blueprints_multiple_selection');
    }else{
      $backUrl =  site_url('parent_controller/display_fee_blueprints');
    } ?>
<div class="col-md-12">
<div class="card cd_border">
  <div class="card-header panel_heading_new_style_staff_border">
      <div class="row" style="margin: 0px">
          <h3 class="card-title panel_title_new_style_staff">
            <a class="back_anchor" href="<?php echo $backUrl ?>" class="control-primary">
              <span class="fa fa-arrow-left"></span>
            </a> 
           Transaction Summary
          </h3>
          <?php 
            $acadyearStds = [];
              foreach ($acad_year_selection as $key => $val) {
                if (!in_array($val->acad_year_id, $acadyearStds)) {
                  $acadyearStds[$val->acad_year_id] = $val->acad_year_id;
                }
              }
              $acadyearSelection = [];
              foreach ($allacadyears as $key => $year) {
                if(array_key_exists($year->id, $acadyearStds)){
                  array_push($acadyearSelection, $year);
                }
              }
            ?>
            <div class="col-md-6 d-flex justify-content-end align-items-center pr-0">
           
          </div>

      </div>

  </div>
   
    <div class="card-body">
      <div class="row">
        <div class="col-md-2">
          <div class="form-group">
            <select class="form-control"  id="acadyearId" onchange="get_fee_summary_amount('<?php echo $student_id ?>',this.value)" >
              <?php foreach ($acadyearSelection as $key => $val) { ?>
                <option <?php if($this->acad_year->getAcadYearId() == $val->id) echo "selected"; ?> value="<?php echo $val->id ?>"><?php echo $val->acad_year ?></option>
              <?php } ?>
            </select>
          </div>
        </div>
      </div>
      <br>
      <div id="fee_summary_details">
      </div>

       

    </div>
</div>
</div>
<style type="text/css">
  .borderlesstBody tr td {
    border: none !important;
    padding: 4px !important;
  }
   .borderlesstBody tr th {
    border: none !important;
    padding: 4px !important;
  }
  .form-control {
    border-radius: 0.6rem;
  }

</style>
<style type="text/css">
    #feeWidget span{
        line-height: 40px;
    }
    .loadingClassNew {
      border: 8px solid #eee;
      border-top: 8px solid #7193be;
      border-radius: 50%;
      width: 48px;
      height: 48px;
      position: fixed;
      z-index: 1;
      animation: spin 2s linear infinite;
      margin-top: 35%;
      margin-left: 40%;
      position: absolute;
      z-index: 99999;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

</style>
<script>
$(document).ready(function(){
    var acadyearId = $('#acadyearId').val();
    var stdId = '<?php echo $student_id ?>';
    get_fee_summary_amount(stdId, acadyearId);
});

function get_fee_summary_amount(stdId, acadyearId) {
    get_fee_summary_details(stdId, acadyearId);
}

function get_fee_summary_details(stdId, acadyearId) {
  $.ajax({
    url: '<?php echo site_url('parent_controller/fee_detailed_summary'); ?>',
    type: 'post',
    data: {'stdId':stdId,'acadyearId':acadyearId},
    success: function(data) {
      var rData = JSON.parse(data);
      if(rData.length!=0){
        $('#fee_summary_details').html(construct_summary_table(rData));
      }else{
        $('#fee_summary_details').html('<div class="no-data-display">No fees record found Contact your school administration</div>');
      }
    }
  });
}

function construct_summary_table(rData) {
  var html = '';
  for (var i = 0; i < rData.length; i++) {
      html +='<div class="col-md-4 box" style="border: 1px solid #ccc; border-radius: 30px;margin-bottom: 0.6rem;margin-right:1rem">';
      html +='<button class="btn btn btn-primary" style="margin-left: -15px;border-top-left-radius: 60px;border-bottom-right-radius: 60px;padding: 4px 10px;">'+rData[i].blueprint_name+'</button>';
      var btnColor = 'btn-danger';
      var payemntStatus = 'Not Started';
      if (rData[i].payment_status == 'FULL') {
        btnColor = 'btn-success';
        payemntStatus = 'Fully Paid';
      }else if(rData[i].payment_status =='PARTIAL') {
        btnColor = 'btn-warning';
        payemntStatus = 'Partially Paid';
      }else{
        btnColor = 'btn-danger';
        payemntStatus = 'Not Started';
      }
      html +='<button class="btn '+btnColor+'" style="border-top-left-radius: 0px;border-bottom-right-radius: 0px;padding: 4px 10px;float: right;margin-right: -8px;"> '+payemntStatus+' </button>';
      html +='<table class="table borderless">';
      html +='<tr>';
      html +='<th>Total Fee</th>';
      html +='<td>'+numberToCurrency(rData[i].total_fee)+'</td>';
      html +='</tr>';
      html +='<tr>';
      html +='<th>Paid Amount</th>';
      html +='<td>'+numberToCurrency(rData[i].total_fee_paid)+'</td>';
      html +='</tr>';
      if (rData[i].total_concession != 0) {
        html +='<tr>';
        html +='<th>Concession</th>';
        html +='<td>'+numberToCurrency(rData[i].total_concession)+'</td>';
        html +='</tr>';
      } 
      if (rData[i].total_adjustment != 0) {        
        html +='<tr>';
        html +='<th>Adjustment</th>';
        html +='<td>'+numberToCurrency(rData[i].total_adjustment)+'</td>';
        html +='</tr>';
      }
      
      if (rData[i].discount != 0) {        
        html +='<tr>';
        html +='<th>Discount</th>';
        html +='<td>'+numberToCurrency(rData[i].discount)+'</td>';
        html +='</tr>';
      }

      if (rData[i].refund_amount != 0) {        
        html +='<tr>';
        html +='<th>Refund</th>';
        html +='<td>'+numberToCurrency(rData[i].refund_amount)+'</td>';
        html +='</tr>';
      }
      html +='<tr>';
      html +='<th>Balance</th>';
      html +='<td>'+numberToCurrency(rData[i].balance)+'</td>';
      html +='</tr>';

      if (rData[i].total_fine != 0) { 
        html +='<tr>';
        html +='<th>Total Fine</th>';
        html +='<td>'+numberToCurrency(rData[i].total_fine)+'</td>';
        html +='</tr>';
      }
      var overallbalance = 0;
      overallbalance = parseFloat(rData[i].balance)  +  parseFloat(rData[i].total_fine);
      if (rData[i].total_fine != 0) {
        html +='<tr>';
        html +='<th>Over All Balance</th>';
        html +='<td>'+numberToCurrency(overallbalance)+'</td>';
        html +='</tr>';
      }

      html +='</table>';
      html +='</div>';
  }
  return html;
}
</script>
