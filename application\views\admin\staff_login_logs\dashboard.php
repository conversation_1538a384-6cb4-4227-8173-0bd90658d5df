<?php defined('BASEPATH') OR exit('No direct script access allowed'); ?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title"><?php echo $title; ?></h3>
                    <div class="card-tools">
                        <a href="<?php echo base_url('admin/staff_login_logs/logs'); ?>" class="btn btn-primary btn-sm">
                            <i class="fas fa-list"></i> View All Logs
                        </a>
                        <a href="<?php echo base_url('admin/staff_login_logs/active_sessions'); ?>" class="btn btn-info btn-sm">
                            <i class="fas fa-users"></i> Active Sessions
                        </a>
                        <a href="<?php echo base_url('admin/staff_login_logs/config'); ?>" class="btn btn-secondary btn-sm">
                            <i class="fas fa-cog"></i> Settings
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Statistics Cards -->
                    <div class="row">
                        <div class="col-lg-3 col-6">
                            <div class="small-box bg-info">
                                <div class="inner">
                                    <h3><?php echo $stats['today_logins']; ?></h3>
                                    <p>Today's Logins</p>
                                </div>
                                <div class="icon">
                                    <i class="fas fa-sign-in-alt"></i>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-6">
                            <div class="small-box bg-success">
                                <div class="inner">
                                    <h3><?php echo $stats['active_sessions']; ?></h3>
                                    <p>Active Sessions</p>
                                </div>
                                <div class="icon">
                                    <i class="fas fa-users"></i>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-6">
                            <div class="small-box bg-warning">
                                <div class="inner">
                                    <h3><?php echo $stats['total_logins']; ?></h3>
                                    <p>Total Logins (7 days)</p>
                                </div>
                                <div class="icon">
                                    <i class="fas fa-chart-line"></i>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-6">
                            <div class="small-box bg-danger">
                                <div class="inner">
                                    <h3><?php echo $stats['unique_users']; ?></h3>
                                    <p>Unique Users (7 days)</p>
                                </div>
                                <div class="icon">
                                    <i class="fas fa-user-friends"></i>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Active Sessions -->
                    <div class="row">
                        <div class="col-md-8">
                            <div class="card">
                                <div class="card-header">
                                    <h3 class="card-title">Active Sessions</h3>
                                </div>
                                <div class="card-body">
                                    <?php if (!empty($active_sessions)): ?>
                                        <div class="table-responsive">
                                            <table class="table table-sm table-striped">
                                                <thead>
                                                    <tr>
                                                        <th>Username</th>
                                                        <th>IP Address</th>
                                                        <th>Login Time</th>
                                                        <th>Duration</th>
                                                        <th>Device</th>
                                                        <th>Browser</th>
                                                        <th>Actions</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <?php foreach ($active_sessions as $session): ?>
                                                        <tr>
                                                            <td>
                                                                <strong><?php echo htmlspecialchars($session['username']); ?></strong>
                                                                <?php if ($session['email']): ?>
                                                                    <br><small class="text-muted"><?php echo htmlspecialchars($session['email']); ?></small>
                                                                <?php endif; ?>
                                                            </td>
                                                            <td><?php echo htmlspecialchars($session['ip_address']); ?></td>
                                                            <td><?php echo date('M j, Y H:i', strtotime($session['login_time'])); ?></td>
                                                            <td><?php echo $session['session_minutes']; ?> min</td>
                                                            <td><?php echo htmlspecialchars($session['device_type'] ?: '-'); ?></td>
                                                            <td><?php echo htmlspecialchars($session['browser_name'] ?: '-'); ?></td>
                                                            <td>
                                                                <a href="<?php echo base_url('admin/staff_login_logs/user_history/' . $session['user_id']); ?>" 
                                                                   class="btn btn-xs btn-info" title="View History">
                                                                    <i class="fas fa-history"></i>
                                                                </a>
                                                                <a href="<?php echo base_url('admin/staff_login_logs/force_logout/' . $session['user_id']); ?>" 
                                                                   class="btn btn-xs btn-danger" title="Force Logout"
                                                                   onclick="return confirm('Are you sure you want to force logout this user?')">
                                                                    <i class="fas fa-sign-out-alt"></i>
                                                                </a>
                                                            </td>
                                                        </tr>
                                                    <?php endforeach; ?>
                                                </tbody>
                                            </table>
                                        </div>
                                    <?php else: ?>
                                        <p class="text-muted">No active sessions found.</p>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <!-- Browser Statistics -->
                            <div class="card">
                                <div class="card-header">
                                    <h3 class="card-title">Top Browsers (7 days)</h3>
                                </div>
                                <div class="card-body">
                                    <?php if (!empty($stats['top_browsers'])): ?>
                                        <?php foreach ($stats['top_browsers'] as $browser): ?>
                                            <div class="d-flex justify-content-between align-items-center mb-2">
                                                <span><?php echo htmlspecialchars($browser['browser_name']); ?></span>
                                                <span class="badge badge-primary"><?php echo $browser['count']; ?></span>
                                            </div>
                                        <?php endforeach; ?>
                                    <?php else: ?>
                                        <p class="text-muted">No browser data available.</p>
                                    <?php endif; ?>
                                </div>
                            </div>

                            <!-- Suspicious Activity -->
                            <?php if (!empty($suspicious_logins['new_ip_logins']) || !empty($suspicious_logins['multiple_ip_users'])): ?>
                                <div class="card">
                                    <div class="card-header">
                                        <h3 class="card-title">Suspicious Activity</h3>
                                    </div>
                                    <div class="card-body">
                                        <?php if (!empty($suspicious_logins['new_ip_logins'])): ?>
                                            <h6>New IP Logins</h6>
                                            <?php foreach (array_slice($suspicious_logins['new_ip_logins'], 0, 5) as $login): ?>
                                                <div class="alert alert-warning alert-sm">
                                                    <strong><?php echo htmlspecialchars($login['username']); ?></strong><br>
                                                    <small>IP: <?php echo htmlspecialchars($login['ip_address']); ?></small><br>
                                                    <small><?php echo date('M j, H:i', strtotime($login['login_time'])); ?></small>
                                                </div>
                                            <?php endforeach; ?>
                                        <?php endif; ?>

                                        <?php if (!empty($suspicious_logins['multiple_ip_users'])): ?>
                                            <h6>Multiple IPs (1 hour)</h6>
                                            <?php foreach ($suspicious_logins['multiple_ip_users'] as $user): ?>
                                                <div class="alert alert-danger alert-sm">
                                                    <strong><?php echo htmlspecialchars($user['username']); ?></strong><br>
                                                    <small><?php echo $user['ip_count']; ?> different IPs</small>
                                                </div>
                                            <?php endforeach; ?>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Recent Login Logs -->
                    <div class="row">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h3 class="card-title">Recent Login Logs</h3>
                                    <div class="card-tools">
                                        <a href="<?php echo base_url('admin/staff_login_logs/logs'); ?>" class="btn btn-sm btn-primary">
                                            View All
                                        </a>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <?php if (!empty($logs['data'])): ?>
                                        <div class="table-responsive">
                                            <table class="table table-sm table-striped">
                                                <thead>
                                                    <tr>
                                                        <th>Username</th>
                                                        <th>IP Address</th>
                                                        <th>Login Time</th>
                                                        <th>Logout Time</th>
                                                        <th>Duration</th>
                                                        <th>Device</th>
                                                        <th>Browser</th>
                                                        <th>Status</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <?php foreach ($logs['data'] as $log): ?>
                                                        <tr>
                                                            <td>
                                                                <strong><?php echo htmlspecialchars($log['username']); ?></strong>
                                                                <?php if ($log['email']): ?>
                                                                    <br><small class="text-muted"><?php echo htmlspecialchars($log['email']); ?></small>
                                                                <?php endif; ?>
                                                            </td>
                                                            <td><?php echo htmlspecialchars($log['ip_address']); ?></td>
                                                            <td><?php echo date('M j, H:i', strtotime($log['login_time'])); ?></td>
                                                            <td><?php echo $log['logout_time'] ? date('M j, H:i', strtotime($log['logout_time'])) : '-'; ?></td>
                                                            <td>
                                                                <?php if ($log['session_duration']): ?>
                                                                    <?php echo gmdate('H:i:s', $log['session_duration']); ?>
                                                                <?php else: ?>
                                                                    -
                                                                <?php endif; ?>
                                                            </td>
                                                            <td><?php echo htmlspecialchars($log['device_type'] ?: '-'); ?></td>
                                                            <td><?php echo htmlspecialchars($log['browser_name'] ?: '-'); ?></td>
                                                            <td>
                                                                <?php if ($log['is_active']): ?>
                                                                    <span class="badge badge-success">Active</span>
                                                                <?php else: ?>
                                                                    <span class="badge badge-secondary">Ended</span>
                                                                <?php endif; ?>
                                                            </td>
                                                        </tr>
                                                    <?php endforeach; ?>
                                                </tbody>
                                            </table>
                                        </div>
                                    <?php else: ?>
                                        <p class="text-muted">No login logs found.</p>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.small-box {
    border-radius: 0.25rem;
    box-shadow: 0 0 1px rgba(0,0,0,.125), 0 1px 3px rgba(0,0,0,.2);
    display: block;
    margin-bottom: 20px;
    position: relative;
}

.small-box > .inner {
    padding: 10px;
}

.small-box > .small-box-footer {
    background: rgba(0,0,0,.1);
    color: rgba(255,255,255,.8);
    display: block;
    padding: 3px 0;
    position: relative;
    text-align: center;
    text-decoration: none;
    z-index: 10;
}

.small-box .icon {
    color: rgba(0,0,0,.15);
    z-index: 0;
}

.small-box .icon > i {
    font-size: 70px;
    position: absolute;
    right: 15px;
    top: 15px;
    transition: transform .3s linear;
}

.small-box:hover .icon > i {
    transform: scale(1.1);
}

.alert-sm {
    padding: 0.25rem 0.5rem;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
}

.btn-xs {
    padding: 0.125rem 0.25rem;
    font-size: 0.75rem;
    line-height: 1.5;
    border-radius: 0.15rem;
}
</style>
